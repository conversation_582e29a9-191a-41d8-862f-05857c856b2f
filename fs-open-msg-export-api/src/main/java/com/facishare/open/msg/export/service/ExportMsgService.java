package com.facishare.open.msg.export.service;

import com.facishare.open.msg.export.model.ExportMsgResultVO;
import com.facishare.open.msg.export.result.ExportMsgResult;

import java.util.Map;

/**
 * 导出消息记录到excel
 * Created by huanghp on 2016/05/24.
 */
public interface ExportMsgService {
    /**
     * 返回ResponseEntity的形式
     *
     * @param beginTime 可为null 表示不限
     * @param endTime   可为null 表示不限
     * @param appName   服务号名称
     * @param paramMap  ea  企业账号
     *                  appId 应用Id
     *                  starMark 是否星标 0:未标星 1：星标
     * @return
     */
    ExportMsgResult<ExportMsgResultVO> exportMsgToExcel(Long beginTime, Long endTime, String appName, Map<String,
            Object> paramMap);
    
    ExportMsgResult<ExportMsgResultVO> exportCrossMsgToExcel(Long beginTime, Long endTime, String appName, Map<String,
            Object> paramMap);
}
