package com.facishare.open.msg.export.result;

import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.google.common.base.MoreObjects;

/**
 * 导出消息到excel返回实体
 * Created by zhong<PERSON> on 2016/5/24.
 */
public class ExportMsgResult<T> extends MsgBaseResult {

    private static final long serialVersionUID = 5082447933103482758L;

    private T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("errorCode", errorCode)
                .add("errorMsg", errorMsg)
                .add("data", data)
                .toString();
    }

    public ExportMsgResult(MsgCodeEnum msgCodeEnum, T data) {
        super(msgCodeEnum);
        this.data = data;
    }
    public ExportMsgResult( T data) {
        super();
        this.data = data;
    }
    public ExportMsgResult(){}
}
