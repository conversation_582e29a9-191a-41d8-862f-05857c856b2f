<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-msg</artifactId>
    <version>2.0.0</version>
  </parent>
  
  <artifactId>fs-open-msg-api</artifactId>
  <description>msg service</description>
  <version>2.0.0-SNAPSHOT</version>
  
  <dependencies>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-api</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>biz-log-client</artifactId>
                  <groupId>com.fxiaoke</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-message-event</artifactId>
      </dependency>

      <dependency>
          <groupId>io.protostuff</groupId>
          <artifactId>protostuff-core</artifactId>
      </dependency>

      <dependency>
          <groupId>io.protostuff</groupId>
          <artifactId>protostuff-runtime</artifactId>
      </dependency>

      <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-common</artifactId>
            <version>2.0.0-SNAPSHOT</version>
      </dependency>


      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>mongo-spring-support</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.github.colin-lee</groupId>
                  <artifactId>rpc-trace</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>jedis-spring-support</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.github.colin-lee</groupId>
                  <artifactId>rpc-trace</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>jedis</artifactId>
                  <groupId>redis.clients</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-callback-api</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.facishare.open</groupId>
                  <artifactId>fs-open-msg-api</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>com.alibaba</groupId>
                  <artifactId>dubbo</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-common-storage</artifactId>
      </dependency>

  </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
