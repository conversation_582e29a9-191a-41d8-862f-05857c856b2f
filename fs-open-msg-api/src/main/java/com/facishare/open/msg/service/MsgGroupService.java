package com.facishare.open.msg.service;

import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.WechatGroupResult;

/**
 * Created by fengyh on 2016/12/27.
 *
 * 微信百川项目 企信群会话管理
 */
public interface MsgGroupService {

    /**企信建群
     *
     * @param arg :@see WechatCreateSessionVO
     * @return WechatGroupResult: @see  WechatGroupResult
     * */
    WechatGroupResult createGroupSession(WechatCreateSessionVO arg);

    /**企信退群
     *
     * @param arg :@see WechatExitSessionVO
     * @return WechatGroupResult: @see  WechatGroupResult
     * */
    WechatGroupResult exitGroupSession(WechatExitSessionVO arg);

    /**企信群加人
     *
     * @param arg :@see WechatInviteParticipantsVO
     * @return WechatGroupResult: @see  WechatGroupResult
     * */
    WechatGroupResult inviteGroupParticipants(WechatInviteParticipantsVO arg);

    /**企信群踢人
     *
     * @param arg :@see WechatKickParticipantsVO
     * @return WechatGroupResult: @see  WechatGroupResult
     * */
    WechatGroupResult kickGroupParticipants(WechatKickParticipantsVO arg);

    /**企信群改群名
     *
     * @param arg :@see WechatChangeSessionNameVO
     * @return WechatGroupResult: @see  WechatGroupResult
     * */
    WechatGroupResult changeGroupName(WechatChangeSessionNameVO arg);

    /**微信百川通知窗口消红点
     * @param setWXBaichuanUnreadFlagVO
     * @return
     * */
    MsgBaseResult setUnreadFlag(SetWXBaichuanUnreadFlagVO setWXBaichuanUnreadFlagVO);
}
