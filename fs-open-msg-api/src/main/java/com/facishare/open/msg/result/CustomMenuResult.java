package com.facishare.open.msg.result;

import java.util.List;

import com.facishare.open.msg.model.CustomMenuVO;
import com.google.common.base.MoreObjects;

public class CustomMenuResult extends MsgBaseResult {

    private static final long serialVersionUID = 1323119929000391175L;

    CustomMenuVO customMenuVO;

    public CustomMenuVO getCustomMenuVO() {
        return customMenuVO;
    }

    public void setCustomMenuVO(CustomMenuVO customMenuVO) {
        this.customMenuVO = customMenuVO;
    }

    public CustomMenuResult() {
        super();
    }

    public CustomMenuResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public CustomMenuResult(MsgCodeEnum resultCode, CustomMenuVO customMenuVO) {
        super(resultCode);
        this.customMenuVO = customMenuVO;
    }
    
    public CustomMenuResult(MsgCodeEnum resultCode) {
        super(resultCode);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("errCode", errorCode)
                .add("errorMsg", errorMsg)
                .add("customMenuVO", customMenuVO)
                .toString();
    }

    
}
