package com.facishare.open.msg.model;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/12/27.
 *
 * 微信群ID和企信群ID的对应关系，放到mongo中。
 * 他们是一对一的关系。
 */

@Entity(value = "wechat_qixin_groupID_mapping", noClassnameStored = true)
public class WechatGroupIDMappingDO implements Serializable {

    @Id
    private ObjectId id;

    /**微信群ID*/
    private String wechatGroupID;

    /**企信群ID*/
    private String qixinGroupID;

    /** 单聊时， 记录单聊的两个人账号
     * 怎么判断是单聊呢，当 wechatGroupID==qixinGroupID时。
     * */
    private String  user1;
    private String  user2;

    public WechatGroupIDMappingDO() {
    }

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getWechatGroupID() {
        return wechatGroupID;
    }

    public void setWechatGroupID(String wechatGroupID) {
        this.wechatGroupID = wechatGroupID;
    }

    public String getQixinGroupID() {
        return qixinGroupID;
    }

    public void setQixinGroupID(String qixinGroupID) {
        this.qixinGroupID = qixinGroupID;
    }

    public String getUser1() {
        return user1;
    }

    public void setUser1(String user1) {
        this.user1 = user1;
    }

    public String getUser2() {
        return user2;
    }

    public void setUser2(String user2) {
        this.user2 = user2;
    }

    @Override
    public String toString() {
        return "WechatGroupIDMappingDO{" +
                "id=" + id +
                ", wechatGroupID='" + wechatGroupID + '\'' +
                ", qixinGroupID='" + qixinGroupID + '\'' +
                ", user1='" + user1 + '\'' +
                ", user2='" + user2 + '\'' +
                '}';
    }
}
