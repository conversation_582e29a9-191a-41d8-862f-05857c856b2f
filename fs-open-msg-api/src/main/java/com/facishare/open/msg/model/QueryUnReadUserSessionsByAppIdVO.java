package com.facishare.open.msg.model;

import com.facishare.open.msg.constant.CustomerServiceMsgType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2017/7/22.
 */
@Data
public class QueryUnReadUserSessionsByAppIdVO implements Serializable {

    private static final long serialVersionUID = 3617835846272661845L;

    private String upStreamEa;
    private List<String> appIds;

    private CustomerServiceMsgType filterType;
}
