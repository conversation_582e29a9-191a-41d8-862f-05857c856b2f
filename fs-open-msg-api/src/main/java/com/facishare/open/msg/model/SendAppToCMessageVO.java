package com.facishare.open.msg.model;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Created by liuyu123 on 2015/12/22.
 */
public class SendAppToCMessageVO extends AbstractOpenMessageVO implements Serializable {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 接收消息的人(fsid)列表
     */
    private List<Integer> toUserList;

    /**
     * 仅当管理员回复时,记录管理员身份信息,格式为E.fs.123
     */
    private String adminUserId;
    
    protected boolean lowPriority;

    private InternationalInfo contentInfo;
    private InternationalInfo summaryInfo;

    public InternationalInfo getContentInfo() {
        return contentInfo;
    }

    public void setContentInfo(InternationalInfo contentInfo) {
        this.contentInfo = contentInfo;
    }

    public InternationalInfo getSummaryInfo() {
        return summaryInfo;
    }

    public void setSummaryInfo(InternationalInfo summaryInfo) {
        this.summaryInfo = summaryInfo;
    }


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public List<Integer> getToUserList() {
        return toUserList;
    }

    public void setToUserList(List<Integer> toUserList) {
        this.toUserList = toUserList;
    }

    public String getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(String adminUserId) {
        this.adminUserId = adminUserId;
    }
 
	public boolean isLowPriority() {
		return lowPriority;
	}

	public void setLowPriority(boolean lowPriority) {
		this.lowPriority = lowPriority;
	}

	@Override
    public boolean validateParam() {
        return super.validateParam() &&
                StringUtils.isNotEmpty(appId) &&
                getToUserList() != null &&
                getToUserList().size() > 0;
    }

    @Override
    public String toString() {
        return "SendAppToCMessageVO{" +
                ", appId='" + appId + '\'' +
                ", toUserList=" + toUserList +
                ", adminUserId='" + adminUserId + '\'' +
                ", lowPriority=" + lowPriority +
                '}';
    }

}
