package com.facishare.open.msg.model;

import com.facishare.qixin.api.model.open.WorkbenchType;
import com.facishare.qixin.api.weixin.model.arg.WXSendWBMessageArg;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/12/28.
 */
public class WechatBaichuanNotifyMessageVO implements Serializable {

    private static final long serialVersionUID = 4706120779284992724L;

    /**接收通知消息的员工所在的企业账号。 E.fs.xxx 中间这部分*/
    private String enterpriseAccount;
    /**摘要*/
    private String lastSummary;
    /**接收通知的企业员工ID, E.fs.xxx 最后一部分 */
    private List<Integer> receivers;
    /**点击跳转的url*/
    private String redirectUrl;
    /**session名称*/
    private String sessionName;
    /**session头像*/
    private String sessionIcon;

    public WechatBaichuanNotifyMessageVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getLastSummary() {
        return lastSummary;
    }

    public void setLastSummary(String lastSummary) {
        this.lastSummary = lastSummary;
    }

    public List<Integer> getReceivers() {
        return receivers;
    }

    public void setReceivers(List<Integer> receivers) {
        this.receivers = receivers;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public String getSessionIcon() {
        return sessionIcon;
    }

    public void setSessionIcon(String sessionIcon) {
        this.sessionIcon = sessionIcon;
    }

    @Override
    public String toString() {
        return "WechatBaichuanNotifyMessageVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", lastSummary='" + lastSummary + '\'' +
                ", receivers=" + receivers +
                ", redirectUrl='" + redirectUrl + '\'' +
                ", sessionName='" + sessionName + '\'' +
                ", sessionIcon='" + sessionIcon + '\'' +
                '}';
    }
}
