package com.facishare.open.msg.result;

import com.google.common.base.MoreObjects;


/**
 * 发送消息返回结果
 * @author: wangtao
 * @date: 2015年7月20日 下午5:30:26
 */
public class MessageResult extends MsgBaseResult {

	private static final long serialVersionUID = 1L;
	private Object data;
	public MessageResult() {
		super();
	}
	
	public MessageResult(MsgCodeEnum errCode){
		super(errCode);
	}
	
	public MessageResult(Object data){
		super();
		this.data = data;
	}

	public MessageResult(int errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}
	
	

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
				.add("errCode", errorCode)
				.add("errorMsg", errorMsg)
				.toString(); 
	}
}

