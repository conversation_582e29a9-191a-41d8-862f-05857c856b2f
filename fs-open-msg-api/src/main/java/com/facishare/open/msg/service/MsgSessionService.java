package com.facishare.open.msg.service;

import java.util.List;
import java.util.Set;

import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.BatchSessionResult;
import com.facishare.open.msg.result.CustomMenuResult;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.SessionResult;


import com.facishare.qixin.api.open.OpenCrossDefinitionService;

/**
 * 适配 企信的session 服务
 * @author: wangtao
 * @date: 2015年8月5日 下午5:01:56
 */
public interface MsgSessionService {

	/**查找服务号sessionid
	 *
	 * @param appID : 服务号appid
	 * @param ea : 服务号所属企业
	 * @param userID ：用户userid  形如E.fs.xxx
	 *@return  @see  SessionResult
	 * */
	SessionResult findSession(String appID, String ea, int userID);

	/**查找企业互联下 互联服务号sesssion
	 * */
	SessionResult findEaConnSession(String appID, String upEa, String downEa, int userID);

	/**
	 * 创建sessionID，如果已存在sessionID ，则查找
	 * @param appId
	 * @param enterpriseAccount
	 * @param userId
	 * @return
	 */
	SessionResult findOrCreateSession(String appId, String enterpriseAccount, Integer userId);
	
	/**
	 * 批量创建session
	 * @param createSessionBatchVO
	 * @return
	 */
	BatchSessionResult findOrCreateSessionBatch(CreateSessionBatchVO createSessionBatchVO);
	
	/** session删除
	 * @param appId  
	 * @param enterpriseAccount  
	 * @param userId  
	 * @param sessionId  
	 * @param deleteAllMessage   是否删除session对应的消息
	 * @param hide  是否隐藏session
	 * @return
	 */
	MessageResult deleteSession(String appId, String enterpriseAccount, Integer userId, String sessionId, boolean deleteAllMessage, boolean hide);

	/**
	 * 删除一个企业下的所有用户的appSession
	 * @param enterpriseAccount  企业号
	 * @param appId  appId
	 * @param deleteAllMessage  是否删除消息
	 * @param hide  是否是隐藏session
	 * @return
	 */
	@Deprecated
	MessageResult deleteSessionSingleEnterprise(String enterpriseAccount, String appId, boolean deleteAllMessage, boolean hide);
	
	MessageResult deleteSessionSingleEnterprise(DeleteSessionSingleEaVO deleteSessionSingleEaVO);
	
	/**
	 * 批量删除session
	 * @param vo  是deleteSession的参数的集合
	 */
	BatchSessionResult deleteSessionBatch(DeleteSessionBatchVO vo);
	
	/**修改session的消息（头像，sessionName）
	 *
	 * @param appId
	 * @param sessionName 新session名字
	 * @param portraitPath  新session图片
	 * @return
	 */
	@Deprecated
	MessageResult updateUniversalSessionDefinition(String appId, String sessionName, String portraitPath) ;

	/**修改服务号session的消息（头像，sessionName）
	 * 服务号session在手机端可能显示在企信频道的一级（普通服务号），或者二级session(企业互联下的服务号)
	 * 这个接口管理的是服务号的公共配置信息
	 *
	 * @param config
	 * @return
	 */
	MessageResult updatePublicAppServiceConfig(PublicAppServiceConfig config);
	/**修改服务号session的消息（头像，sessionName）
	 * 服务号session在手机端可能显示在企信频道的一级（普通服务号），或者二级session(企业互联下的服务号)
	 * 这个接口管理的是服务号的企业私有配置信息
	 *
	 * @param config
	 * @return
	 */
	MessageResult updatePrivateAppServiceConfig(PrivateAppServiceConfig config);

	/**
	 * 更新session 状态信息
	 * @param updateSessionStatusVO
	 * @return
	 */
	MessageResult updateSessionStatus(UpdateSessionStatusVO updateSessionStatusVO);


	/**
	 * 更新session的描述,是否隐藏输入框等。
	 * @param appId
	 * @param description
	 * @param hideInputPanel
	 * @param showSwitch
	 * @param sendMessgeTypes
	 * @return
	 */
	MessageResult updateSessionInfo(String appId, String description, boolean hideInputPanel, boolean showSwitch,
		Set<String> sendMessgeTypes);

	/** 往客户端推送数据（提醒客户端更新app列表）
	 * @param appId
	 * @param enterpriseAccount
	 * @param userId
	 * @param key  需要更新的属性key
	 * @param value 需要更新的属性value 可以使字符串形式的json
	 * @return
	 */
	MessageResult updateUserProperties(String appId, String enterpriseAccount, Integer userId, Integer key, String value);

	/**
	 * 注意：该接口是为了兼容老代码增加的，新业务请用updateUserProperties().
	 *
	 * 往客户端推送数据（提醒客户端更新app列表）
	 *
	 * @param enterpriseAccount
	 * @param fsEmployID
	 * @param key  需要更新的属性key
	 * @return
	 */
	MessageResult updateUserPropertiesDeprecated(String enterpriseAccount, String fsEmployID, Integer key);


	/**  批量推送数据
	 * @param appId
	 * @param enterpriseAccount
	 * @param ownerIds
	 * @param key
	 * @param value
	 */
	MessageResult updateUserPropertiesBatch(String appId, String enterpriseAccount, List<Integer> ownerIds, Integer key, String value);
	
	/** 批量推送数据 异步接口.
     * 这里的异步是指 接口理解返回，然后在新线程里面继续推送数据给企信。
     * 所以该接口是不可靠的，即使返回success, 也不代表所有消息都推送到客户端了。
     * 请根据应用场景选用。
     *
     * @param appId
     * @param enterpriseAccont
     * @param ownerIds
     * @param key  需要更新的属性key
     * @param value 需要更新的属性value 可以使字符串形式的json
     * @return
     */
    MessageResult updateUserPropertiesBatchAsync(String appId,  String enterpriseAccont, List<Integer> ownerIds, Integer key, String value);
	
	/**
	 * 修改自定义菜单
	 *
	 * 如果一个应用有多个ea在用，ea没有私有拷贝数据，全部ea公用一份菜单数据。
	 * 服务端不会主动通知客户端来拉取新菜单
	 *
	 * @param custumMenuVO 
	 * @return
	 */
	MessageResult updateCustomMenu(CustomMenuVO custumMenuVO);


	/**
	 * 自建应用修改自定义菜单，并通知ea下的所有终端来拉取新菜单
	 * */
	MessageResult updatePrivateAppCustomMenu(String ea, CustomMenuVO custumMenuVO);
	
	/**
     * 修改自定义菜绑定到公司
	 * 一个应用多个企业在用，每个ea有自己的自定义菜单拷贝。
	 * 服务端会通知ea下的客户端来拉取新菜单
     * @param custumMenuVO 
     * @return
     */
    MessageResult updateEaCustomMenu(String ea, CustomMenuVO custumMenuVO);

	/**
	 * 修改应用是否可用
	 * @param appId 应用ID
	 * @param usable 是否可用
	 * @return
	 */
	MessageResult updateAppUsable(String appId, boolean usable);
	
	/**
	 * 获取指定appID的自定义菜单
	 * @param appId 应用ID
	 * @return
	 */
	CustomMenuResult getCustomMenu(String appId);


	/**更新企业互联下的应用session配置信息, 该配置信息用来控制创建session时获取基本信息。
	 * session的消息由上游推给下游。发消息时，企信后台检查如果session还没有创建，
	 * 会取出配置信息来创建session.
	 *
	 * @param config
	 * @return :
	 * */
	MsgBaseResult updateEaConnAppSessionConfig(EaConnAppSessionConfig config);

	/**
	 * 企业互联session, 带上游企业的服务号session
	 * 清除红点，飘数等
	 * */
	MsgBaseResult updateEaConnSessionStatus(EaConnAppSessionStatus arg);

	/**
	 * 配置 企业互联下的审批提醒session的title和头像，	 * 更新摘要和未审批数目。
	 * 更新摘要会引起未读数变化。
	 *
	 * 不需要更新的字段传null.
	 * */
	MsgBaseResult updateEaConnRemindSessionConfig(EaConnRemindSessionConfigVO arg);
	
	/**
	 * 配置 /更改 企业互联下 【派工单】等跳转提醒session 信息
	 * 包括头像、名称、未读数、提醒摘要 等
	 * */
	MsgBaseResult updateEaConnRemindSessionInfo(UpdateEaConnRemindSessionInfo arg);
}

