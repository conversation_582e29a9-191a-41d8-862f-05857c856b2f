package com.facishare.open.msg.model;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.utils.IndexType;

import com.google.common.base.MoreObjects;

/**
 * 消息临时存放 存取消息对象存列化后的内容
 */
@Entity(value = "temp_open_msg", noClassnameStored = true)
@Indexes({ @Index(fields = { @Field("tempSendId"), @Field("upEnterpriseAccount") }, options = @IndexOptions(name = "IDX_tempSendId_upEa", unique = true)), })
public class TempOpenMsgDO implements Serializable {

    private static final long serialVersionUID = -8932001766192805390L;

    @Id
    private ObjectId id;
    
    private String tempSendId;

    /**
     *  上游企业帐号
     */
    private String upEnterpriseAccount;

    /**
     *  消息内容， pb格式二进制
     */
    private byte[] content;

    /**
     * 消息创建时间
     */
    private long createTime;
   
    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }
 
    public byte[] getContent() {
		return content;
	}

	public void setContent(byte[] content) {
		this.content = content;
	}

	public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
  
    public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}
 
    public String getTempSendId() {
		return tempSendId;
	}

	public void setTempSendId(String tempSendId) {
		this.tempSendId = tempSendId;
	}

	public String getCreateTimeDesc() {
        Instant specificTime = Instant.ofEpochMilli(createTime);
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.ofInstant(specificTime, ZoneId.of("GMT+8"));
        
        return dateTime.format(formatter);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("id", id)
                .add("tempSendId", tempSendId)
                .add("upEnterpriseAccount", upEnterpriseAccount)
                .add("createTime", createTime)
                .toString();
    }

}
