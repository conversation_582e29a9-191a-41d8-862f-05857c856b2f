package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

import com.google.common.collect.Lists;

/**
 * @Description: 用于存储评价消息
 * <AUTHOR>
 * @date 2017/8/22
 */
@Entity(value = "customer_evaluate", noClassnameStored = true)
public class CustomerEvaluateDO implements Serializable {

    private static final long serialVersionUID = 3616768976546829L;

    @Id
    private ObjectId id;

	/**
	 * 服务号Id
	 */
	private String appId;
	
	/**
	 * 用于判断是那种服务号 1  内部，2 互联
	 */
	private Integer appType;

	/**
	 * 上游企业号
	 */
	private String enterpriseAccount;
	
	/**
	 * 反馈者企业号
	 */
	private String replyEa;
	
	/**
	 * 反馈者Id
	 */
	private Integer evaluatorId;

	/**
	 * 模版消息LinkId,唯一标识一条评价
	 */
	private String msgLinkId;
	
	/**
	 * 客服Id
	 */
	private Integer customerId;
	
	/**
	 * 评价得分
	 */
	private Integer score;
	
	/**
	 * 评价说明
	 */
	private List<String> remarks = Lists.newArrayList();
	
	/**
	 * 评价结果
	 */
	private String evaluateResult;
	
	/**
	 * 评价状态 1.未评价 2.已经评价
	 */
	private Integer evaluateStatus;
	
	/**
	 * 创建时间
	 */
	private long createTime;

	/**
	 * 更新时间
	 */
	private long updateTime;
	
	/**
	 * 工作台二级sessionId(用于反馈通知找会话)
	 */
	private String customerSessionId;
	
	/**
	 * 原sessionId用于找到普通用户的sessionId/微联服务号表示openId
	 */
	private String originSessionId;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

	public String getMsgLinkId() {
		return msgLinkId;
	}

	public void setMsgLinkId(String msgLinkId) {
		this.msgLinkId = msgLinkId;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}
	
	public List<String> getRemarks() {
		return remarks;
	}

	public void setRemarks(List<String> remarks) {
		this.remarks = remarks;
	}

	public Integer getEvaluateStatus() {
		return evaluateStatus;
	}

	public void setEvaluateStatus(Integer evaluateStatus) {
		this.evaluateStatus = evaluateStatus;
	}

	public long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public long getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(long updateTime) {
		this.updateTime = updateTime;
	}

	public String getCustomerSessionId() {
		return customerSessionId;
	}

	public void setCustomerSessionId(String customerSessionId) {
		this.customerSessionId = customerSessionId;
	}

	public Integer getAppType() {
		return appType;
	}

	public void setAppType(Integer appType) {
		this.appType = appType;
	}

	public String getEvaluateResult() {
		return evaluateResult;
	}

	public void setEvaluateResult(String evaluateResult) {
		this.evaluateResult = evaluateResult;
	}

	public String getReplyEa() {
		return replyEa;
	}

	public void setReplyEa(String replyEa) {
		this.replyEa = replyEa;
	}

	public Integer getEvaluatorId() {
		return evaluatorId;
	}

	public void setEvaluatorId(Integer evaluatorId) {
		this.evaluatorId = evaluatorId;
	}

	public String getOriginSessionId() {
		return originSessionId;
	}

	public void setOriginSessionId(String originSessionId) {
		this.originSessionId = originSessionId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("id", id)
            .append("enterpriseAccount", enterpriseAccount)
            .append("appId", appId)
            .append("evaluatorId", evaluatorId)
            .append("replyEa", replyEa)
            .append("msgLinkId", msgLinkId)
            .append("customerId", customerId)
            .append("score", score)
            .append("remarks", remarks)
            .append("createTime", createTime)
            .append("updateTime", updateTime)
            .append("evaluateStatus", evaluateStatus)
            .append("evaluateResult", evaluateResult)
            .append("appType", appType)
            .append("customerSessionId", customerSessionId)
            .append("originSessionId", originSessionId)
            .toString();
    }

}
