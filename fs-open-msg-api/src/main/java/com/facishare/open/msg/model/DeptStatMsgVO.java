package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;

/**
 * 内部服务号部门统计 Created by huanghp on 2017/8/22.
 */
public class DeptStatMsgVO implements Serializable {

	private static final long serialVersionUID = 5224778645154200896L;

	/**
	 * 部门Id
	 */
	private Integer deptId;

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 反馈消息用户数
	 */
	private Integer upMsgUserCount;

	/**
	 * 反馈消息数
	 */
	private Integer upMsgCount;

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getUpMsgUserCount() {
		return upMsgUserCount;
	}

	public void setUpMsgUserCount(Integer upMsgUserCount) {
		this.upMsgUserCount = upMsgUserCount;
	}

	public Integer getUpMsgCount() {
		return upMsgCount;
	}

	public void setUpMsgCount(Integer upMsgCount) {
		this.upMsgCount = upMsgCount;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this).add("deptId", deptId)
				.add("deptName", deptName)
				.add("upMsgUserCount", upMsgUserCount)
				.add("upMsgCount", upMsgCount).toString();
	}
}
