package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/12/27.
 */
public class WechatKickParticipantsVO implements Serializable {

    private static final long serialVersionUID = -8726903268586431051L;

    private String enterpriseAccount;

    private String fromEmployee;//操作成员

    private String sessionId;

    private List<String> participants;//不包括fromEmployee

    public WechatKickParticipantsVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getFromEmployee() {
        return fromEmployee;
    }

    public void setFromEmployee(String fromEmployee) {
        this.fromEmployee = fromEmployee;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<String> getParticipants() {
        return participants;
    }

    public void setParticipants(List<String> participants) {
        this.participants = participants;
    }

    @Override
    public String toString() {
        return "WechatKickParticipantsVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", fromEmployee='" + fromEmployee + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", participants=" + participants +
                '}';
    }
}
