package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 消息导出到excel数据实体
 * Created by zhongcy on 2016/5/24.
 */
public class ExportMsgVO implements Serializable {

    private static final long serialVersionUID = -6481769208097049744L;

    /**
     * 消息ID
     */
    private long msgId;

    /**
     * 消息类型    文本T/位置L/图片I/图文IMAGE_TEXT/文档D/表情E/ 日程IGT / 任务 IGT /群通知 LWN /投票LWV
     */
    private String msgType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息发送者:APP或人名称
     */
    private String senderName;

    /**
     * 消息接受者:人或APP名称
     */
    private String receiverName;

    /**
     * 消息创建时间
     */
    private long createTime;
    /**
     * 消息的会话ID, 由"appId" + "-" + "fsUserId"组成
     * 服务号会话消息将来会允许删除, 如果同步企信会话ID会导致不唯一
     */
    private String sessionId;

    /**
     * 星标标记 0：未标记  1：表示已标记
     */
    private int starMark;

    /**
     * 会话用户名称，用于消息的归类
     */
    private String sessionUserName;
    
    /**
     * 是否上行消息: senderId为fsUserId即上行消息
     */
    private boolean isUplink;
    
    /**
     * 消息发送类型: 规则：正数表示 App到人的下行消息；负数表示人到APP的上行消息；0表示人与人消息；@see MessageSendTypeEnum
     * 1表示第三方推送, 2表示管理员推送, 3表示默认自动回复, 4表示管理员的回复, 5表示关键字回复, 6表示系统提示
     * 0表示其他<包括音视频，红包>, 
     * -1表示上行普通消息, -2表示上行事件消息
     */
    private int sendType;
    
    /**
     * 消息发送类型名称描述
     */
    private String sendTypeName;
    
    /**
     * 回复管理员名称
     */
    private String adminUserName;
    
    /**
     * 用户昵称
     */
    private String userName;

    /**用户所在公司名*/
    private String eaName;
    
    /**用户所在公司**/
    private String ea;

    public long getMsgId() {
        return msgId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getStarMark() {
        return starMark;
    }

    public void setStarMark(int starMark) {
        this.starMark = starMark;
    }

    public String getSessionUserName() {
        return sessionUserName;
    }

    public void setSessionUserName(String sessionUserName) {
        this.sessionUserName = sessionUserName;
    }

    public boolean isUplink() {
        return isUplink;
    }

    public void setUplink(boolean isUplink) {
        this.isUplink = isUplink;
    }

    public int getSendType() {
        return sendType;
    }

    public void setSendType(int sendType) {
        this.sendType = sendType;
    }

    public String getAdminUserName() {
        return adminUserName;
    }

    public void setAdminUserName(String adminUserName) {
        this.adminUserName = adminUserName;
    }

    public String getSendTypeName() {
        return sendTypeName;
    }

    public void setSendTypeName(String sendTypeName) {
        this.sendTypeName = sendTypeName;
    }

    public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getEaName() {
		return eaName;
	}

	public void setEaName(String eaName) {
		this.eaName = eaName;
	}

	public String getEa() {
		return ea;
	}

	public void setEa(String ea) {
		this.ea = ea;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("msgId", msgId)
                .add("msgType", msgType)
                .add("content", content)
                .add("senderName", senderName)
                .add("receiverName", receiverName)
                .add("createTime", createTime)
                .add("sessionId", sessionId)
                .add("starMark", starMark)
                .add("sessionUserName", sessionUserName)
                .add("isUplink", isUplink)
                .add("sendType", sendType)
                .add("adminUserName", adminUserName)
                .add("sendTypeName", sendTypeName)
                .add("eaName", eaName)
                .add("ea", ea)
                .toString();
    }

    public String getCreateTimeDesc() {
        Instant specificTime = Instant.ofEpochMilli(createTime);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.ofInstant(specificTime, ZoneId.of("GMT+8"));

        return dateTime.format(formatter);
    }
}
