package com.facishare.open.msg.model;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/12/27.
 */
public class WechatExitSessionVO implements Serializable {
    private static final long serialVersionUID = 2725788908811533739L;

    private String enterpriseAccount;

    private String fromEmployee;//操作成员

    private String sessionId;

    public WechatExitSessionVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getFromEmployee() {
        return fromEmployee;
    }

    public void setFromEmployee(String fromEmployee) {
        this.fromEmployee = fromEmployee;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @Override
    public String toString() {
        return "WechatExitSessionVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", fromEmployee=" + fromEmployee +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }
}
