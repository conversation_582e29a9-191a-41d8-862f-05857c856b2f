package com.facishare.open.msg.constant;

/**
 * 媒体文件类型
 * <AUTHOR>
 */
public enum MeidaTypeEnum {
	    
	IMAGE(1),	
	VOICE(2),	
	VIDEO(3),
	FILE(4),
	;
	
	private Integer code;
	
	private MeidaTypeEnum(Integer code) {
	    this.code= code;	
	}

	public Integer getCode() {
        return code;
    }

    public static boolean isIllegal(Integer meidaType) {
		for(MeidaTypeEnum en :  MeidaTypeEnum.values()){
		    if( en.getCode().equals(meidaType) ){
		        return true;
			}			
		}
		return false;
	}
	
	/**
	 * 此方法用于识别第三方传入参数
	 * @param type
	 * @return
	 */
	public static MeidaTypeEnum getMeidaTypeEnum(Integer meidaType){
		for(MeidaTypeEnum en :  MeidaTypeEnum.values()){
			if( en.getCode().equals(meidaType) ){
			    return en;
			}
		}
		return null;
	}
}

