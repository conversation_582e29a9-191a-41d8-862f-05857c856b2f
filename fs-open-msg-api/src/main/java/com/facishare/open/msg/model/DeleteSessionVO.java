package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:50:30
 */
public class DeleteSessionVO implements Serializable {
	
	private static final long serialVersionUID = 4734657642650163630L;
		
	/**
	 * 用户ID
	 */
	private Integer userId;
	
	/**
	 * 是否删除所有消息
	 */
	private boolean deleteAllMessage;  
	
	/**
	 * 是否隐藏
	 */
	private boolean hide;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public boolean isDeleteAllMessage() {
		return deleteAllMessage;
	}

	public void setDeleteAllMessage(boolean deleteAllMessage) {
		this.deleteAllMessage = deleteAllMessage;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}
	
	@Override
    public String toString() {
        return new ToStringBuilder(this).append("userId ", userId)
                .append("hide", hide)
                .append("deleteAllMessage", deleteAllMessage)
                .toString();
    }
}

