package com.facishare.open.msg.model;

import java.io.Serializable;

import com.google.common.base.MoreObjects;

/**
 *
 * <AUTHOR>
 * @date 2016年3月3日
 */

public class UserSessionVO implements Serializable {

    private static final long serialVersionUID = 6942784087291007219L;

    /**
     * AppID
     */
    private String appId;

    /**
     * 消息的会话ID, 由"appId" + "-" + "fsUserId"组成
     */
    private String sessionId;

    /**
     * 用户ID，格式E.fs.123
     */
    private String fullFsUserId;


    /**
     * 用户昵称
     */
    private String userName;

    /**用户所在公司名*/
    private String eaName;
    
    /**用户所在公司**/
    private String ea;

    /**
     * 最新消息ID
     */
    private long lastMessageId;

    /**
     * 最新消息摘要
     */
    private String lastContentSummary;

    /**
     * 最新消息类型 文本T/位置L/图片I/图文IMAGE_TEXT/文档D
     */
    private String lastContentType;

    /**
     * 用户会话消息状态： 1表示未读，2表示已读未回复，3表示已读已回复
     */
    private int status;

    /**
     * 消息发送时间
     */
    private long datetime;

    /**
     * 头像.
     */
    private String profileImage;
    /**
     *用户状态。1.正常。2.已停用.
     */
    private Integer userStatus;

    public String getEaName() {
        return eaName;
    }

    public void setEaName(String eaName) {
        this.eaName = eaName;
    }

    public String getEa() {
		return ea;
	}

	public void setEa(String ea) {
		this.ea = ea;
	}

	public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getFullFsUserId() {
        return fullFsUserId;
    }

    public void setFullFsUserId(String fullFsUserId) {
        this.fullFsUserId = fullFsUserId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public long getLastMessageId() {
        return lastMessageId;
    }

    public void setLastMessageId(long lastMessageId) {
        this.lastMessageId = lastMessageId;
    }

    public String getLastContentSummary() {
        return lastContentSummary;
    }

    public void setLastContentSummary(String lastContentSummary) {
        this.lastContentSummary = lastContentSummary;
    }

    public String getLastContentType() {
        return lastContentType;
    }

    public void setLastContentType(String lastContentType) {
        this.lastContentType = lastContentType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getDatetime() {
        return datetime;
    }

    public void setDatetime(long datetime) {
        this.datetime = datetime;
    }

    public String getProfileImage() {
        return profileImage;
    }

    public void setProfileImage(String profileImage) {
        this.profileImage = profileImage;
    }

    public Integer getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(Integer userStatus) {
        this.userStatus = userStatus;
    }

    @Override
    public String toString() {
        return "UserSessionVO{" +
                "appId='" + appId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", fullFsUserId='" + fullFsUserId + '\'' +
                ", userName='" + userName + '\'' +
                ", eaName='" + eaName + '\'' +
                ", lastMessageId=" + lastMessageId +
                ", lastContentSummary='" + lastContentSummary + '\'' +
                ", lastContentType='" + lastContentType + '\'' +
                ", status=" + status +
                ", datetime=" + datetime +
                ", profileImage='" + profileImage + '\'' +
                ", userStatus=" + userStatus +
                ", eaName=" + eaName +
                ", ea=" + ea +
                '}';
    }

}
