package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:50:30
 */
public class DeleteSessionSingleEaVO implements Serializable {
	
	private static final long serialVersionUID = 4734657642650163630L;
	
	private String enterpriseAccount;
	
	private String appId;
 
	/**
	 * 是否删除所有消息
	 */
	private boolean deleteAllMessage;  
	
	/**
	 * 是否隐藏
	 */
	private boolean hide;
	
	 /**   0-多客服， 1-微客服,2-互联客服   */
    private int sessionType = 0;
 
	public boolean isDeleteAllMessage() {
		return deleteAllMessage;
	}

	public void setDeleteAllMessage(boolean deleteAllMessage) {
		this.deleteAllMessage = deleteAllMessage;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}
	
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public String getAppId() {
		return appId;
	}

	public int getSessionType() {
		return sessionType;
	}

	public void setSessionType(int sessionType) {
		this.sessionType = sessionType;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this).append("enterpriseAccount ", enterpriseAccount)
        		.append("appId", appId)
                .append("hide", hide)
                .append("deleteAllMessage", deleteAllMessage)
                .append("sessionType", sessionType)
                .toString();
    }
}

