package com.facishare.open.msg.model;

import io.protostuff.Tag;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import com.facishare.open.msg.model.base.ProtoBase;
import com.facishare.qixin.api.model.ParticipantTO;

public class OpenMessageSendEvent extends ProtoBase {

    private static final long serialVersionUID = -7770453572250848096L;
    
    @Tag(1)
    private String enterpriseAccount;
    @Tag(2)
    private String sessionId;
    @Tag(3)
    private String sessionType;
    @Tag(4)
    private String subCategory;
    @Tag(5)
    private String appId;
    @Tag(6)
    private String parentSessionId;
    @Tag(7)
    private String parentSessionType;
    @Tag(8)
    private long messageId;
    @Tag(9)
    private String messageType;
    @Tag(10)
    private int senderId;
    @Tag(11)
    private String fullSenderId;
    @Tag(12)
    private String content;
    @Tag(13)
    private long messageTimestamp;
    @Tag(14)
    private List<Integer> participants = new ArrayList<>();
    @Tag(15)
    private java.lang.String source;
    @Tag(16)
	private List<ParticipantTO> participantTOList = new ArrayList();
    @Tag(17)
	private String upEnterpriseAccount;
    @Tag(18)
    private boolean isPrivate;
    // locale和chineseContent
    @Tag(20)
    private String locale = Locale.CHINA.toLanguageTag();
    @Tag(21)
    private String chineseContent;


    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }
    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getSessionType() {
        return sessionType;
    }
    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }
    public String getSubCategory() {
        return subCategory;
    }
    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
    public String getAppId() {
        return appId;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }
    public String getParentSessionId() {
        return parentSessionId;
    }
    public void setParentSessionId(String parentSessionId) {
        this.parentSessionId = parentSessionId;
    }
    public String getParentSessionType() {
        return parentSessionType;
    }
    public void setParentSessionType(String parentSessionType) {
        this.parentSessionType = parentSessionType;
    }
    public long getMessageId() {
        return messageId;
    }
    public void setMessageId(long messageId) {
        this.messageId = messageId;
    }
    public String getMessageType() {
        return messageType;
    }
    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
    public int getSenderId() {
        return senderId;
    }
    public void setSenderId(int senderId) {
        this.senderId = senderId;
    }
    public String getFullSenderId() {
        return fullSenderId;
    }
    public void setFullSenderId(String fullSenderId) {
        this.fullSenderId = fullSenderId;
    }
    public String getContent() {
        return content;
    }
    public void setContent(String content) {
        this.content = content;
    }
    public long getMessageTimestamp() {
        return messageTimestamp;
    }
    public void setMessageTimestamp(long messageTimestamp) {
        this.messageTimestamp = messageTimestamp;
    }
    public List<Integer> getParticipants() {
        return participants;
    }
    public void setParticipants(List<Integer> participants) {
        this.participants = participants;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<ParticipantTO> getParticipantTOList() {
		return participantTOList;
	}
	public void setParticipantTOList(List<ParticipantTO> participantTOList) {
		this.participantTOList = participantTOList;
	}
	public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}
	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}
	
	public boolean isPrivate() {
		return isPrivate;
	}
	public void setPrivate(boolean isPrivate) {
		this.isPrivate = isPrivate;
	}

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getChineseContent() {
        return chineseContent;
    }

    public void setChineseContent(String chineseContent) {
        this.chineseContent = chineseContent;
    }

    @Override
    public String toString() {
        return "OpenMessageSendEvent{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", sessionType='" + sessionType + '\'' +
                ", subCategory='" + subCategory + '\'' +
                ", appId='" + appId + '\'' +
                ", parentSessionId='" + parentSessionId + '\'' +
                ", parentSessionType='" + parentSessionType + '\'' +
                ", messageId=" + messageId +
                ", messageType='" + messageType + '\'' +
                ", senderId=" + senderId +
                ", fullSenderId='" + fullSenderId + '\'' +
                ", content='" + content + '\'' +
                ", messageTimestamp=" + messageTimestamp +
                ", participants=" + participants +
                ", source='" + source + '\'' +
                ", participantTOList=" + participantTOList +
                ", upEnterpriseAccount='" + upEnterpriseAccount + '\'' +
                ", isPrivate=" + isPrivate +
                ", locale='" + locale + '\'' +
                ", chineseContent='" + chineseContent + '\'' +
                '}';
    }

}
