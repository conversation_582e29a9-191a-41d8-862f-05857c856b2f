package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;

import com.facishare.qixin.api.model.OSS1SessionDefinition.OSS1CustomMenuItme;
import com.google.gson.Gson;

/**
 * 自定义菜单 
 * <AUTHOR>
 * @date 2015年9月10日 下午3:30:39
 */
public class CustomMenuVO implements Serializable {

    private static final long serialVersionUID = 8058574767922454538L;

    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 菜单名称
     */
    private String name;
    
    /**
     * 一级菜单 列表
     * 传null删除菜单
     */
    private List<MenuVO> menuList;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MenuVO> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<MenuVO> menuList) {
        this.menuList = menuList;
    }
    
    @Override
    public String toString() {
        return  new ToStringBuilder(this)
                .append("name", name)
                .append("appId", appId)
                .append("menuList", menuList)
                .toString();
    }
    
    
    /**
     * 递归赋值
     */
    public ArrayList<OSS1CustomMenuItme> getMenuVO(List<MenuVO> menuList) {
        ArrayList<OSS1CustomMenuItme> list = new ArrayList();
        OSS1CustomMenuItme item = null;
        if (menuList != null){
            for (MenuVO vo : menuList) {
                item = new OSS1CustomMenuItme();
                item.action = vo.getType();
                item.actionParam = vo.getActionParam();
                item.displayName = vo.getName();
                if (vo.getSubMenuList()!=null && !vo.getSubMenuList().isEmpty()) {
                    item.children = getMenuVO(vo.getSubMenuList());
                }
                list.add(item);
            }
        }
        return list;
    }
    
    public static void main(String[] args) {
        List<MenuVO> menuList = new ArrayList();
        MenuVO vo = new MenuVO();
        vo.setName("parent");
        vo.setType(0);
        List<MenuVO> subMenuList = new ArrayList();
       
        
        MenuVO vo01 = new MenuVO();
        vo01.setName("children");
        vo01.setType(1);
        vo01.setActionParam("key");
        vo01.setSubMenuList(null);
        subMenuList.add(vo01);
        
        vo.setSubMenuList(subMenuList);
        menuList.add(vo);
        
        MenuVO vo2 = new MenuVO();
        vo2.setName("parant2");
        vo2.setActionParam("");
        vo2.setType(0);
        List<MenuVO> subMenuList02 = new ArrayList();
        MenuVO vo021 = new MenuVO();
        vo021.setName("children021");
        vo021.setActionParam("021");
        vo021.setType(1);
        vo021.setSubMenuList(null);
        subMenuList02.add(vo021);
        vo2.setSubMenuList(subMenuList02);
        menuList.add(vo2);
        System.out.println(new Gson().toJson(menuList));
        
        CustomMenuVO v = new CustomMenuVO();
        v.setAppId("FSAPPID_11");
        v.setName("test");
        v.setMenuList(menuList);
        ArrayList<OSS1CustomMenuItme> l = v.getMenuVO(v.getMenuList());
        System.out.println(new Gson().toJson(v));
        System.out.println(new Gson().toJson(l));
    }


    @Override
    public boolean equals(Object o) {
        try {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            CustomMenuVO that = (CustomMenuVO) o;

            if (appId != null ? !appId.equals(that.appId) : that.appId != null) return false;
            if (menuList != null ? !menuList.equals(that.menuList) : that.menuList != null) return false;
            if (name != null ? !name.equals(that.name) : that.name != null) return false;

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int hashCode() {
        int result = appId != null ? appId.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (menuList != null ? menuList.hashCode() : 0);
        return result;
    }
}
