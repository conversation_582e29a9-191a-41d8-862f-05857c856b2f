package com.facishare.open.msg.model;

import java.util.List;

import com.google.common.collect.Lists;

import lombok.Data;

@Data
public class MessageListVO {

	//去掉mq以后， 存储原来mq 中message的flag
	private int msgFlag;

	private String tempSendId;
	private String upstreamEa;
	private List<MessagePbVO> msgList;
	public void addMessage(MessagePbVO vo){
		if(msgList == null){
			msgList = Lists.newArrayList();
		}
		msgList.add(vo);
	}
}
