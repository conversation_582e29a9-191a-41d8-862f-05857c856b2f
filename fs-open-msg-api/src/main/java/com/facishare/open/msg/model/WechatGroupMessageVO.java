package com.facishare.open.msg.model;

import java.io.Serializable;

import com.facishare.qixin.api.constant.MessageType;

/**
 * Created by fengyh on 2016/12/27.
 */
public class WechatGroupMessageVO implements Serializable {

    private static final long serialVersionUID = 669559732505275053L;

    private String enterpriseAccount;
    private String fromEmployee;
    /**S 单聊,   D 群聊*/
    private String sessionType = "D";
    /**如果是单聊，这里不是群ID, 而是接收者的账号。*/
    private String wechatSessionID;

    /**
     * 请参考 com.facishare.qixin.api.constant.MessageType
     * */
    private String messageType;

    private String messageContent;
    /**用于消息去重*/
    private String postID;
    /**发送者名字*/
    private String senderName;

    public WechatGroupMessageVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getFromEmployee() {
        return fromEmployee;
    }

    public void setFromEmployee(String fromEmployee) {
        this.fromEmployee = fromEmployee;
    }

    public String getWechatSessionID() {
        return wechatSessionID;
    }

    public void setWechatSessionID(String wechatSessionID) {
        this.wechatSessionID = wechatSessionID;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getPostID() {
        return postID;
    }

    public void setPostID(String postID) {
        this.postID = postID;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    @Override
    public String toString() {
        return "WechatGroupMessageVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", fromEmployee='" + fromEmployee + '\'' +
                ", sessionType='" + sessionType + '\'' +
                ", wechatSessionID='" + wechatSessionID + '\'' +
                ", messageType='" + messageType + '\'' +
                ", messageContent='" + messageContent + '\'' +
                ", postID='" + postID + '\'' +
                ", senderName='" + senderName + '\'' +
                '}';
    }
}
