package com.facishare.open.msg.service;

import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.CustomerEvaluateResult;

/**
 * 客服评价相关接口
 * @author: huanghp
 * @date: 2017年8月22日
 */
public interface CustomerEvaluateService {
	
	/**
	 * 根据linkId 查询评价记录
	 * @param msgLinkId linkId
	 * @return 返回客服评价详情
	 */
	CustomerEvaluateResult<CustomerEvaluateDO> queryCustomerEvaluateByLinkId(String msgLinkId);

	/**
     * 根据id更新客服评价信息 
     * @param updateCustomerEvaluateArg 发送文本消息的内容
     * @param messageSendType 消息发送类型
     * @return 返回 错误码 错误描述
     */
	CustomerEvaluateResult<Boolean> updateCustomerEvaluate(UpdateCustomerEvaluateVO updateCustomerEvaluateVO);
	
}

