package com.facishare.open.msg.result;

import org.apache.commons.lang.builder.ToStringBuilder;

import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;

/**
 * 
 * <AUTHOR>
 * @date 2016年3月4日
 */

public class MessageExhibitionResult<T> extends MsgBaseResult {

    private static final long serialVersionUID = -5273252070601088263L;

    protected T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public MessageExhibitionResult() {
        super();
    }

    public MessageExhibitionResult(MsgCodeEnum resultCode) {
        super(resultCode);
    }

    public MessageExhibitionResult(T data) {
        super();
        this.data = data;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
        .append("errorCode", this.errorCode)
        .append("errormsg", this.errorMsg)
        .append("data", this.data)
        .toString();
    }

}
