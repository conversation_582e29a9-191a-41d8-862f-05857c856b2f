package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;

/**
 * 内部服务号部门统计 Created by huanghp on 2017/8/22.
 */
public class EnterpriseStatMsgVO implements Serializable {

	private static final long serialVersionUID = 5224778645154200896L;

	/**
	 * 企业Id
	 */
	private Integer enterpriseId;

	/**
	 * 企业名称
	 */
	private String enterpriseName;

	/**
	 * 反馈消息用户数
	 */
	private Integer upMsgUserCount;

	/**
	 * 反馈消息条数
	 */
	private Integer upMsgCount;

	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getEnterpriseName() {
		return enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}

	public Integer getUpMsgUserCount() {
		return upMsgUserCount;
	}

	public void setUpMsgUserCount(Integer upMsgUserCount) {
		this.upMsgUserCount = upMsgUserCount;
	}

	public Integer getUpMsgCount() {
		return upMsgCount;
	}

	public void setUpMsgCount(Integer upMsgCount) {
		this.upMsgCount = upMsgCount;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this).add("enterpriseId", enterpriseId)
				.add("enterpriseName", enterpriseName)
				.add("upMsgUserCount", upMsgUserCount)
				.add("upMsgCount", upMsgCount).toString();
	}
}
