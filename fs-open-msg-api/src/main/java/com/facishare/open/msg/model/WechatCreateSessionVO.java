package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/12/27.
 */
public class WechatCreateSessionVO implements Serializable {
    private static final long serialVersionUID = 944545534815514907L;

    private String enterpriseAccount;

    private String fromEmployee;//操作成员

    private List<String> participants;//不包括fromEmployee

    /**群聊时，传微信群ID。 单聊时，忽略*/
    private String wechatGroupID;

    private String sessionType;//S 单聊  D 群聊

    private String sessionName;

    public WechatCreateSessionVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getFromEmployee() {
        return fromEmployee;
    }

    public void setFromEmployee(String fromEmployee) {
        this.fromEmployee = fromEmployee;
    }

    public List<String> getParticipants() {
        return participants;
    }

    public void setParticipants(List<String> participants) {
        this.participants = participants;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public String getWechatGroupID() {
        return wechatGroupID;
    }

    public void setWechatGroupID(String wechatGroupID) {
        this.wechatGroupID = wechatGroupID;
    }

    @Override
    public String toString() {
        return "WechatCreateSessionVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", fromEmployee='" + fromEmployee + '\'' +
                ", participants=" + participants +
                ", wechatGroupID='" + wechatGroupID + '\'' +
                ", sessionType='" + sessionType + '\'' +
                ", sessionName='" + sessionName + '\'' +
                '}';
    }
}
