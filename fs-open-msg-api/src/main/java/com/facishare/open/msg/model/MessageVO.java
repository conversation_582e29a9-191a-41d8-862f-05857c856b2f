package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.util.StringUtils;

import com.facishare.open.msg.constant.MessageTypeEnum;

/**
 * 消息的基类
 * 
 * <AUTHOR>
 *
 */
public class MessageVO implements Serializable {

    private static final long serialVersionUID = 4752206487519220661L;

    /**
     * 消息接收者的企业账号 对应企信的 enterpriseAccount
     */
    protected String enterpriseAccount;

    /**
     * 接收消息的人(fsid)列表
     */
    protected List<Integer> toUserList;


    /**
     * 应用ID
     */
    protected String appId;

    /**
     * 消息内容
     */
    protected String content;



    /**
     * 消息类型 支持： text , image , template
     */
    protected MessageTypeEnum type;

    /**
     * 用于防止重复消息发送
     */
    protected String postId;

    /**
     * 仅当管理员回复时,记录管理员身份信息,格式为E.fs.123
     */
    protected String adminUserId;
    
    protected boolean lowPriority;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<Integer> getToUserList() {
        return toUserList;
    }

    public void setToUserList(List<Integer> toUserList) {
        this.toUserList = toUserList;
    }

    public MessageTypeEnum getType() {
        return type;
    }

    public void setType(MessageTypeEnum type) {
        this.type = type;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(String adminUserId) {
        this.adminUserId = adminUserId;
    }
 
	public boolean isLowPriority() {
		return lowPriority;
	}

	public void setLowPriority(boolean lowPriority) {
		this.lowPriority = lowPriority;
	}


    @Override
    public String toString() {
        return "MessageVO{" +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", toUserList=" + toUserList +
                ", appId='" + appId + '\'' +
                ", type=" + type +
                ", postId='" + postId + '\'' +
                ", adminUserId='" + adminUserId + '\'' +
                ", lowPriority=" + lowPriority +
                '}';
    }

    /**
     * 数据合法性校验 content的合法性不在该方法校验
     * 
     * @return
     */
    public boolean validateParam() {
        return !(getToUserList() == null || getToUserList().isEmpty()
                || StringUtils.isEmpty(this.getAppId())
                || StringUtils.isEmpty(this.getPostId())
                || StringUtils.isEmpty(this.getType())
                || StringUtils.isEmpty(this.getEnterpriseAccount()));
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        MessageVO other = (MessageVO) obj;
        return Objects.equals(appId, other.appId)
                && Objects.equals(enterpriseAccount, other.enterpriseAccount)
                && Objects.equals(content, other.content)
                && Objects.equals(toUserList, other.toUserList)
                && Objects.equals(type, other.type)
                && Objects.equals(postId, other.postId)
                && Objects.equals(lowPriority, other.lowPriority)
                && Objects.equals(adminUserId, other.adminUserId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enterpriseAccount, appId, content, toUserList, type, postId, adminUserId,lowPriority);
    }

}
