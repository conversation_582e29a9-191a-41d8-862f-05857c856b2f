package com.facishare.open.msg.constant;

/**
 * 消息类型2
 * <AUTHOR>
 *
 */
public enum MessageTypeEnum {

	TEXT("text"),	    
	IMAGE("I"),	
	VOICE("voice"),	
	VIDEO("video"),
	OFFICE("office"),	
	EVENT("event"),	
	TEMPLATE("template"),
	TEXT_TEMPLATE("text_template"),

	//下面这介个是企信定义的type.
	OT_MESSAGE("OT"),
	OPEN_MESSAGE("OpenMessage"),
	TEXT_QIXIN("T");
	
	private String type;
	
	private MessageTypeEnum(String type) {
	    this.type= type;	
	}
	
	public String getType(){
	    return this.type;
	}
	
	public static boolean isIllegal(String msgtype) {
		for(MessageTypeEnum en :  MessageTypeEnum.values()){
		    if( en.getType().equals(msgtype) ){
		        return true;
			}			
		}
		return false;
	}
	
	/**
	 * 此方法用于识别第三方传入参数
	 * @param type
	 * @return
	 */
	public static MessageTypeEnum getMessageTypeEnmu(String type ){
		for(MessageTypeEnum en :  MessageTypeEnum.values()){
			if( en.getType().equals(type) ){
			    return en;
			}
		}
		return null;
	}
}

