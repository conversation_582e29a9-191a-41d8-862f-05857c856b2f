package com.facishare.open.msg.service;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;

import java.util.List;


/**
 * 发送消息服务接口
 * @author: wangtao
 * @date: 2015年7月29日 下午4:07:13
 */
public interface SendMessageService {

	/**
	 * 发送文本消息 
	 * @param param 发送文本消息的内容
	 * @return 返回 错误码 错误描述
	 */
	@Deprecated
	MessageResult sendTextMessage(SendTextMessageVO param);

	/**
     * 发送文本消息  (增加下行消息发送类型) 
     * @param param 发送文本消息的内容
     * @param messageSendType 消息发送类型
     * @return 返回 错误码 错误描述
     */
    MessageResult sendTextMessage(SendTextMessageVO param, MessageSendTypeEnum messageSendType);
	
	/**
	 * 发送模板消息 
	 * @param param 模板消息对象
	 * @return 
	 */
	@Deprecated
	MessageResult sendTemplateMessage(SendTemplateMessageVO param);
	
	   /**
     * 发送模板消息 (增加下行消息发送类型)  
     * @param param 模板消息对象
     * @param messageSendType 消息发送类型
     * @return 
     */
	   @Deprecated
    MessageResult sendTemplateMessage(SendTemplateMessageVO param, MessageSendTypeEnum messageSendType);
    

    /**
     * 发送oa消息
     * @param param oa消息内容实体
     * @param messageSendType
     * @return
     */
    MessageResult sendOfficeMessage(SendOfficeMessageVO param, MessageSendTypeEnum messageSendType);
    
    /**
     * 发送内部消息
     * @param param
     * @param messageSendType
     * @return
     */
    MessageResult sendImageMessage(MessageVO param, MessageSendTypeEnum messageSendType);

 	/**
	 * 发送微客服消息，从微信来的企业外部用户消息上行到企信的工作台。
	 *
	 * @param wetchatMessageVO @see WechatMessageVO
	 * @return
	 */
	MessageResult sendWechatMessage(WechatMessageVO wetchatMessageVO);

	/**
	 * 微信百川项目
	 * 发送微信群消息 到 企信群。
	 * 也包括一对一聊。
	 *
	 * @param wechatGroupMessageVO @see WechatGroupMessageVO
	 * @return  @see MessageResult
	 */
	MessageResult sendWechatGroupMessage(WechatGroupMessageVO wechatGroupMessageVO);


	/**
	 * 发送微信百川通知窗口的消息到企信后台.
	 * 第一条消息会触发 一级session和通知窗口的创建。
     *
     * @param wechatBaichuanNotifyMessageVO ： @see WechatBaichuanNotifyMessageVO
     * @return  @see MessageResult
	 * */
	MessageResult sendWechatBaichuanNotify(WechatBaichuanNotifyMessageVO wechatBaichuanNotifyMessageVO);


	/**
	 * 发送系统提示消息到微客服工作台二级session
	 *
	 * @param appID ： 纷享服务号appid
	 * @param ea： 服务号所属企业的账号
	 * @param wxOpenID： 微信用户在微信开平的openID
	 * @param promptContent： 系统提示语文本内容
	 * @param receivers :  要接受这条系统提示消息的客服人员ID
	 *
	 * @return
	 */
	@Deprecated
	MessageResult sendWechatPromptTextMessage(String appID, String ea, String wxOpenID, String promptContent, List<Integer> receivers);

	/**
	 * 发送系统提示消息到微客服工作台二级session
	 * @param sendWechatPromptTextMessageArg @see SendWechatPromptTextMessageArg
	 * @return
	 */
	MessageResult sendWechatPromptTextMessage(SendWechatPromptTextMessageArg sendWechatPromptTextMessageArg);

	/**推消息到app端企业互联session下的 二级上游session.
	 *
	 * 此二级session下，app名后面 不 带上游企业名的session 推送消息。
	 *
     * 比如企业互联助手等
	 * */
	MsgBaseResult sendMsgToEaConnService(MessageVO msg);

    /**推消息到 app端企业互联下的 下游二级session.
	 *
	 * 该接口已经废弃，请使用 sendMsgToEaConnService(SendMsgToEaConnServiceVO arg);
     * */
	@Deprecated
    MsgBaseResult sendMsgToEaConnService(String upstreamEa, MessageVO msg);

	/**
	 **推消息到 app端企业互联下的 下游二级session 新接口， 支持批量下游。
	 *
	 * 往下游的 企业互联session下的二级session推送消息。
	 * 此二级session中，app名后面带了游企业名的session。
	 * */
	MsgBaseResult sendMsgToEaConnService(SendMsgToEaConnServiceVO arg);


	/**
	 * 服务号群发消息撤回。
	 * 该接口会立即返回，然后异步执行撤回消息的操作。
	 *
	 * @param revokeMsgVO @see RevokeMsgVO
	 * @return  @see MessageResult
	 */
	MessageResult revokeAppToCMsg(RevokeMsgVO revokeMsgVO);
}

