package com.facishare.open.msg.model;

import com.facishare.open.msg.constant.GroupMessageRange;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by liuyu123 on 2015/12/21.
 */
public class SendCToCMessageVO extends AbstractOpenMessageVO implements Serializable {

    /**
     * 消息发送者Id
     */
    private Integer userId;

    /**
     * 发送的到的session的id
     */
    private String sessionId;

    /**
     * 群消息的发送范围，单人消息无需填写
     * ALL：发送给所有人
     * PART: 发送给部分人，消息接收人在receiverList里指定
     */
    private GroupMessageRange groupMessageRange;

    /**
     * 消息接收人的ID列表，单人消息无需填写
     * 当groupMessageRange 为 PART的时候需填写，为ALL的时候无需填写
     */
    private List<Integer> receiverList;

    /**
     * 企信session列表里的显示文本, 即当此消息是最新一条消息时，在企信通话列表里显示的内容，特殊的Summary，
     * 需要不同的人看到不一样的summary需要关注此属性, 如果没有此需求，可不填
     */
    protected Map<Integer, String> specialSummary;

    private Boolean isNeedUpdateUnReadCount;

    private InternationalInfo contentInfo;
    private InternationalInfo summaryInfo;

    public InternationalInfo getContentInfo() {
        return contentInfo;
    }

    public void setContentInfo(InternationalInfo contentInfo) {
        this.contentInfo = contentInfo;
    }

    public InternationalInfo getSummaryInfo() {
        return summaryInfo;
    }

    public void setSummaryInfo(InternationalInfo summaryInfo) {
        this.summaryInfo = summaryInfo;
    }


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public GroupMessageRange getGroupMessageRange() {
        return groupMessageRange;
    }

    public void setGroupMessageRange(GroupMessageRange groupMessageRange) {
        this.groupMessageRange = groupMessageRange;
    }

    public List<Integer> getReceiverList() {
        return receiverList;
    }

    public void setReceiverList(List<Integer> receiverList) {
        this.receiverList = receiverList;
    }

    public Map<Integer, String> getSpecialSummary() {
        return specialSummary;
    }

    public void setSpecialSummary(Map<Integer, String> specialSummary) {
        this.specialSummary = specialSummary;
    }

    public Boolean getNeedUpdateUnReadCount() {
        return isNeedUpdateUnReadCount;
    }

    public void setNeedUpdateUnReadCount(Boolean needUpdateUnReadCount) {
        isNeedUpdateUnReadCount = needUpdateUnReadCount;
    }

    @Override
    public boolean validateParam() {
        return super.validateParam()&&
                userId != null &&
                StringUtils.isNotEmpty(sessionId);
    }

    @Override
    public String toString() {
        return "SendCToCMessageVO{" +
                "userId=" + userId +
                ", sessionId='" + sessionId + '\'' +
                ", groupMessageRange=" + groupMessageRange +
                ", receiverList=" + receiverList +
                ", specialSummary=" + specialSummary +
                ", isNeedUpdateUnReadCount=" + isNeedUpdateUnReadCount +
                "} " + super.toString();
    }
}
