package com.facishare.open.msg.constant;

/**
 * Created by fengyh on 2017/7/21.
 */
public enum CustomerServiceMsgType {

    INTENAL_CUSTOMER_SERVICE(0), //企业内部服务号
    EA_CONN_CUSTOMER_SERVICE(1), //互联服务号
    ;

    private Integer value;

    CustomerServiceMsgType(Integer value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static CustomerServiceMsgType findByType(int type) {
        for (CustomerServiceMsgType messageSendTypeEnum : CustomerServiceMsgType.values()) {
            if (messageSendTypeEnum.getValue() == type) {
                return messageSendTypeEnum;
            }
        }
        throw new IllegalArgumentException("type is invalid");
    }
}
