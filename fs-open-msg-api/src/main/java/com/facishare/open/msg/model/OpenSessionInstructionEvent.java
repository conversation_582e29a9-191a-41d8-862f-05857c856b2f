package com.facishare.open.msg.model;

import io.protostuff.Tag;

import com.facishare.open.msg.model.base.ProtoBase;
import com.google.common.base.MoreObjects;

public class OpenSessionInstructionEvent extends ProtoBase {
    
    private static final long serialVersionUID = 6605767049599610545L;

    @Tag(1)
    private int senderId; //事件发送者

    @Tag(2)
    private String appId;

    @Tag(3)
    private String eventType; //事件类型 subscribe, unsubscribe, click,

    @Tag(4)
    private long createTime = System.currentTimeMillis();

    @Tag(5)
    private String eventData; //json 格式

    @Tag(6)
    private String enterpriseAccount; //企业号
    
    @Tag(7)
    private String sessionId;
    
    @Tag(8)
    private String sessionType;
    
    @Tag(9)
	private String upEnterpriseAccount;

    public int getSenderId() {
        return senderId;
    }

    public void setSenderId(int senderId) {
        this.senderId = senderId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }
    
    public String getFullSenderId() {
        return "E." + enterpriseAccount + "." + senderId;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }
    
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("senderId", senderId)
                .add("appId", appId)
                .add("eventType", eventType)
                .add("createTime", createTime)
                .add("eventData", eventData)
                .add("enterpriseAccount", enterpriseAccount)
                .add("sessionId", sessionId)
                .add("sessionType", sessionType)
                .add("upEnterpriseAccount", upEnterpriseAccount)
                .toString();
    }
}
