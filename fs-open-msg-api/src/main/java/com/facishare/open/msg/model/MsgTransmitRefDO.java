package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

/**
 * @Description: 用于存储消息转发记录
 * <AUTHOR>
 * @date 2016/8/17
 */
@Entity(value = "msg_transmit_ref", noClassnameStored = true)
public class MsgTransmitRefDO implements Serializable {

    private static final long serialVersionUID = 3616768976546829L;

    @Id
    private ObjectId id;

	/**
	 * 应用ID
	 */
	private String appId;

	/**
	 * 企业号
	 */
	private String enterpriseAccount;
	
	private String targetEa;

	/**
	 * 原消息Id
	 */
	private Long srcMsgId;

	/**
	 * 原消息SessionId
	 */
	private String srcSessionId = "";

	/**
	 * 转发消息的消息Id
	 */
	private Long transmitMsgId;
	
	/**
	 * 转发消息的SessionId
	 */
	private String transmitSessionId = "";
	
	/**
	 * 消息来源 0：服务号上行 | 1：工作台回复 |2互联服务号上行| 3互联工作台回复
	 */
	private Integer msgSource;
	
	/**
	 * 创建时间
	 */
	private long createTime;

	/**
	 * 更新时间
	 */
	private long updateTime;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getSrcSessionId() {
        return srcSessionId;
    }

    public void setSrcSessionId(String srcSessionId) {
        this.srcSessionId = srcSessionId;
    }

    public String getTransmitSessionId() {
        return transmitSessionId;
    }

    public void setTransmitSessionId(String transmitSessionId) {
        this.transmitSessionId = transmitSessionId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
    
    public Integer getMsgSource() {
        return msgSource;
    }

    public void setMsgSource(Integer msgSource) {
        this.msgSource = msgSource;
    }

    public Long getSrcMsgId() {
        return srcMsgId;
    }

    public void setSrcMsgId(Long srcMsgId) {
        this.srcMsgId = srcMsgId;
    }

    public Long getTransmitMsgId() {
        return transmitMsgId;
    }

    public void setTransmitMsgId(Long transmitMsgId) {
        this.transmitMsgId = transmitMsgId;
    }
 
	public String getTargetEa() {
		return targetEa;
	}

	public void setTargetEa(String targetEa) {
		this.targetEa = targetEa;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("id", id)
            .append("enterpriseAccount", enterpriseAccount)
            .append("appId", appId)
            .append("srcMsgId", srcMsgId)
            .append("srcSessionId", srcSessionId)
            .append("transmitMsgId", transmitMsgId)
            .append("transmitSessionId", transmitSessionId)
            .append("msgSource", msgSource)
            .append("createTime", createTime)
            .append("updateTime", updateTime)
            .append("targetEa", targetEa)
            .toString();
    }

}
