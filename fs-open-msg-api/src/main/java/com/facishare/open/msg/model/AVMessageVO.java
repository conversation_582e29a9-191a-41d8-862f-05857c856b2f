package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2015.11.17
 */
public class AVMessageVO implements Serializable {

    private static final long serialVersionUID = -6941065463508571744L;

    String enterpriseAccount;

    Integer userId;

    String content;

    String sessionId;

    List<Integer> receiverIdList;

    String type;

    Boolean isInvitation = false;

    /**
     * 用于防止重复消息发送
     */
    protected String postId;


    @Override
    public String toString() {
        return "AVMessageVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", userId=" + userId +
                ", content='" + content + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", receiverIdList=" + receiverIdList +
                ", type='" + type + '\'' +
                ", isInvitation=" + isInvitation +
                ", postId='" + postId + '\'' +
                '}';
    }

    public Boolean getIsInvitation() {
        return isInvitation;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<Integer> getReceiverIdList() {
        return receiverIdList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setReceiverIdList(List<Integer> receiverIdList) {
        this.receiverIdList = receiverIdList;
    }

    public Boolean isInvitation() { return isInvitation; }

    public void setIsInvitation(Boolean invitation) {
        isInvitation = invitation;
    }

    public boolean validateParam() {
        return this.getUserId() != null && StringUtils.isNotEmpty(this.getEnterpriseAccount())
                && (this.getReceiverIdList() != null || StringUtils.isNotEmpty(this.getSessionId()));
    }
}
