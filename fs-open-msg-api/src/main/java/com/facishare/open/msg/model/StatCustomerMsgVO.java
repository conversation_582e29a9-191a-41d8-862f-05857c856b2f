package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;
import java.util.List;

/**
 * 客服统计统计消息
 * Created by 黄虎平 on 2017/08/22.
 */
public class StatCustomerMsgVO implements Serializable {
    
	private static final long serialVersionUID = 4255828732211454145L;

    /**
     * 企业账号
     */
    private String enterpriseAccount;
    
    /**
     * 客服回复消息数
     */
    private Integer totalReplyMsgCount;
    
    /**
     * 企业反馈消息数
     */
    private List<ReplyStatMsgVO> replyStatMsgs;
    
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public Integer getTotalReplyMsgCount() {
		return totalReplyMsgCount;
	}

	public void setTotalReplyMsgCount(Integer totalReplyMsgCount) {
		this.totalReplyMsgCount = totalReplyMsgCount;
	}

	public List<ReplyStatMsgVO> getReplyStatMsgs() {
		return replyStatMsgs;
	}

	public void setReplyStatMsgs(List<ReplyStatMsgVO> replyStatMsgs) {
		this.replyStatMsgs = replyStatMsgs;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("enterpriseAccount", enterpriseAccount)
                .add("totalReplyMsgCount", totalReplyMsgCount)
                .add("replyStatMsgs", replyStatMsgs)
                .toString();
    }
	
}
