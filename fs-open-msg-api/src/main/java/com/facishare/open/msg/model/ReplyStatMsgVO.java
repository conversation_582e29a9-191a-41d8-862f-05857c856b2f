package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;

/**
 * 客服回复统计 Created by huanghp on 2017/8/22.
 */
public class ReplyStatMsgVO implements Serializable {

	private static final long serialVersionUID = 5224778645154200896L;

	/**
	 * 客服人员Id
	 */
	private Integer employeeId;
	
    /**
     * 客服人员名称
     */
    private String employeeName;
    
    /**
     * 客服回复消息数
     */
    private Integer replyMsgCount;
    
    /**
     * 评价总分
     */
    private Integer evalTotalScore;
    
    /**
     * 评价邀请次数
     */
    private Integer evalSendNum;
    
    /**
     * 评价反馈次数
     */
    private Integer evalReplyNum;
    
    /**
     * 主部门名称
     */
    private String mainDeptName;

	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Integer getReplyMsgCount() {
		return replyMsgCount;
	}

	public void setReplyMsgCount(Integer replyMsgCount) {
		this.replyMsgCount = replyMsgCount;
	}

	public Integer getEvalTotalScore() {
		return evalTotalScore;
	}

	public void setEvalTotalScore(Integer evalTotalScore) {
		this.evalTotalScore = evalTotalScore;
	}

	public Integer getEvalSendNum() {
		return evalSendNum;
	}

	public void setEvalSendNum(Integer evalSendNum) {
		this.evalSendNum = evalSendNum;
	}

	public Integer getEvalReplyNum() {
		return evalReplyNum;
	}

	public void setEvalReplyNum(Integer evalReplyNum) {
		this.evalReplyNum = evalReplyNum;
	}

	public String getMainDeptName() {
		return mainDeptName;
	}

	public void setMainDeptName(String mainDeptName) {
		this.mainDeptName = mainDeptName;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this).add("employeeId", employeeId)
				.add("employeeName", employeeName)
				.add("replyMsgCount", replyMsgCount)
				.add("evalTotalScore", evalTotalScore)
				.add("evalSendNum", evalSendNum)
				.add("evalReplyNum", evalReplyNum)
				.add("mainDeptName", mainDeptName).toString();
	}
}
