package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 微客服发送消息参数实体
 * <AUTHOR>
 * @date 2016-6-21
 */
public class WechatMessageVO implements Serializable {

    private static final long serialVersionUID = 4637183600600193852L;
    
    /**
     * 企业账号
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 发送者Id, 微信用户的OpenID
     */
    private String senderOpenId;
    /**
     * 发送者昵称, 从openid拿到的微信用户昵称
     * */
    private String senderNickName;
    /**
     * 发送者头像url，从openid拿到的微信用户头像url
     * */
    private String senderPortraitUrl;
    
    /**
     * 该微信用户的客服人员ID列表, E.fs.xxx  最后这一部分
     */
    private List<Integer>  customerIDs;

    /**
     * 消息类型 参照 @see com.facishare.qixin.api.constant.MessageType
     */
    private String messageType;
    
    /**
     * 用于排重的唯一Id
     */
    private String postId;

    /**
     * 发送内容. 请使用工具类 WechatMsgConverter 构造这个字段。
     */
    private String content;
    
    /**
     * 接收客服人员及身份列表
     */
    private List<CustomerMembersVO> customerMembers;
    
    /**
     * 接收专家及身份列表
     */
    private List<CustomerMembersVO> expertMembers;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSenderOpenId() {
        return senderOpenId;
    }

    public void setSenderOpenId(String senderOpenId) {
        this.senderOpenId = senderOpenId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<Integer> getCustomerIDs() {
        return customerIDs;
    }

    public void setCustomerIDs(List<Integer> customerIDs) {
        this.customerIDs = customerIDs;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getSenderNickName() {
        return senderNickName;
    }

    public void setSenderNickName(String senderNickName) {
        this.senderNickName = senderNickName;
    }

    public String getSenderPortraitUrl() {
        return senderPortraitUrl;
    }

    public void setSenderPortraitUrl(String senderPortraitUrl) {
        this.senderPortraitUrl = senderPortraitUrl;
    }

	public List<CustomerMembersVO> getCustomerMembers() {
		return customerMembers;
	}

	public void setCustomerMembers(List<CustomerMembersVO> customerMembers) {
		this.customerMembers = customerMembers;
	}

	public List<CustomerMembersVO> getExpertMembers() {
		return expertMembers;
	}

	public void setExpertMembers(List<CustomerMembersVO> expertMembers) {
		this.expertMembers = expertMembers;
	}

	@Override
    public String toString() {
        return "WechatMessageVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", senderOpenId='" + senderOpenId + '\'' +
                ", senderNickName='" + senderNickName + '\'' +
                ", senderPortraitUrl='" + senderPortraitUrl + '\'' +
                ", receiverIds=" + customerIDs +
                ", messageType='" + messageType + '\'' +
                ", postId='" + postId + '\'' +
                ", content='" + content + '\'' +
                ", customerMembers='" + customerMembers + '\'' +
                ", expertMembers='" + expertMembers + '\'' +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
    }
    
}
