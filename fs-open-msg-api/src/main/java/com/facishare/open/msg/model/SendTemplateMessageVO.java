package com.facishare.open.msg.model;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:50:57
 */
public class SendTemplateMessageVO extends MessageVO {

    private static final long serialVersionUID = 1L;

    /**
     * url访问地址
     */
    private String url;

    /**
     * title的颜色
     */
    private String topColor;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板数据
     */
    private Map<String, Map<String, String>> data;

    public String getTopColor() {
        return topColor;
    }

    public void setTopColor(String topColor) {
        this.topColor = topColor;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, Map<String, String>> getData() {
        return data;
    }

    public void setData(Map<String, Map<String, String>> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("enterpriseAccount", enterpriseAccount)
                .append("appid", this.appId)
                .append("url", url)
                .append("templateId", templateId)
                .append("postId", postId)
                .append("adminUserId", adminUserId)
                .append("touser", toUserList)
                .toString();
    }

	/**
	 * 数据合法性校验
	 */
    @Override
    public boolean validateParam() {
        return (super.validateParam() && !StringUtils.isEmpty(templateId));
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        SendTemplateMessageVO other = (SendTemplateMessageVO) obj;
        return Objects.equals(appId, other.appId)
                && Objects.equals(enterpriseAccount, other.enterpriseAccount)
                && Objects.equals(content, other.content)
                && Objects.equals(toUserList, other.toUserList)
                && Objects.equals(type, other.type)
                && Objects.equals(postId, other.postId)
                && Objects.equals(adminUserId, other.adminUserId)
                && Objects.equals(url, other.url)
                && Objects.equals(topColor, other.topColor)
                && Objects.equals(templateId, other.templateId)
                && Objects.equals(data, other.data);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enterpriseAccount, appId, content, toUserList, type, postId, adminUserId, url, topColor,
                templateId, data);
    }

}
