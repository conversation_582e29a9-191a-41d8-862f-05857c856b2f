package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:50:47
 */
public class CreateSessionVO implements Serializable {

	
	private static final long serialVersionUID = -6176403990064264288L;

	/**
	 * 应用ID
	 */
	private String appId;
	
	/**
	 * 企业号
	 */
	private String enterpriseAccount;
	
	/**
	 * 员工号
	 */
	private Integer employeeNumber;
	
	public String getAppId() {
		return appId;
	}
	
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}
	
	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}
	
	public Integer getEmployeeNumber() {
		return employeeNumber;
	}
	
	public void setEmployeeNumber(Integer employeeNumber) {
		this.employeeNumber = employeeNumber;
	}
	
	@Override
    public String toString() {
		return new ToStringBuilder(this).append("appid ", appId)
				.append("enterpriseAccount", enterpriseAccount)
				.append("employeeNumber", employeeNumber).toString();
	}
}

