package com.facishare.open.msg.model;

import java.io.Serializable;

import com.google.common.base.MoreObjects;

/**
 * 
 * <AUTHOR>
 * @date 2016年5月23日
 */

public class StartMessagesVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private long messageId;

    /**
     * 发送者 为应用ID或者fsUserId（格式为E.fs.123）
     */
    private String senderId;

    /**
     * 发送者名称 为应用名称或者用户昵称
     */
    private String senderName;

    /**
     * 发送者状态 1.正常。2.已停用
     */
    private Integer senderState;

    /**
     * 应用图标或者用户图像地址
     */
    private String profileImageUrl;

    /**
     * 消息内容, UrlEncode编码
     */
    private String content;

    /**
     * 消息类型: 位置 L/音频 A/图片 I/文档 D/文本T 等
     */
    private String contentType;

    /**
     * 是否上行消息: senderId为fsUserId即上行消息
     */
    private boolean isUplink;

    /**
     * 消息发送时间
     */
    private long datetime;

    /**
     * 展示下行消息时用于区分管理员身份信息,格式为E.fs.123
     */
    private String adminUserId;
    
    /**
     * 消息发送类型: 规则：正数表示 App到人的下行消息；负数表示人到APP的上行消息；0表示人与人消息；@see MessageSendTypeEnum
     * 1表示第三方推送, 2表示管理员推送, 3表示默认自动回复, 4表示管理员的回复, 5表示关键字回复, 6表示系统提示
     * 0表示其他<包括音视频，红包>, 
     * -1表示上行普通消息, -2表示上行事件消息
     */
    private int sendType;
    
    /**
     * 是否管理员回复
     */
    private boolean isAdminReply;
    
    /**
     * 星标导出
     * 管理员回复时，回复者名称
     * 下行消息时（管理员，客服回复）人员名称
     */
    private String adminUserName;

    /**
     * 管理员回复时，回复者状态 1.正常。2.已停用
     */
    private Integer adminUserState;

    /**
     * 星标导出
     * 是否星标 0:非星标  1：星标
     */
    private int startMark;
    
    /**
     *  星标导出
     *  消息的会话ID, 由"appId" + "-" + "fsUserId"组成
     *  服务号会话消息将来会允许删除, 如果同步企信会话ID会导致不唯一
     */
    private String sessionId;

    /**
     *  星标导出
     *  接收者名称
     */
    private String receiveUserName;

    /**
     *  接收者名称 1.正常。2.已停用
     */
    private Integer receiveUserState;
    
    /**用户所在公司名*/
    private String eaName;

    /**用户所在公司**/
    private String ea;
    
    public long getMessageId() {
        return messageId;
    }

    public void setMessageId(long messageId) {
        this.messageId = messageId;
    }

    public String getSenderId() {
        return senderId;
    }

    public void setSenderId(String senderId) {
        this.senderId = senderId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public Integer getSenderState() {
        return senderState;
    }

    public void setSenderState(Integer senderState) {
        this.senderState = senderState;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public boolean isUplink() {
        return isUplink;
    }

    public void setUplink(boolean isUplink) {
        this.isUplink = isUplink;
    }

    public long getDatetime() {
        return datetime;
    }

    public void setDatetime(long datetime) {
        this.datetime = datetime;
    }

    public String getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(String adminUserId) {
        this.adminUserId = adminUserId;
    }

    public String getAdminUserName() {
        return adminUserName;
    }

    public void setAdminUserName(String adminUserName) {
        this.adminUserName = adminUserName;
    }

    public Integer getAdminUserState() {
        return adminUserState;
    }

    public void setAdminUserState(Integer adminUserState) {
        this.adminUserState = adminUserState;
    }

    public int getStartMark() {
        return startMark;
    }

    public void setStartMark(int startMark) {
        this.startMark = startMark;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public Integer getReceiveUserState() {
        return receiveUserState;
    }

    public void setReceiveUserState(Integer receiveUserState) {
        this.receiveUserState = receiveUserState;
    }

    public int getSendType() {
        return sendType;
    }

    public void setSendType(int sendType) {
        this.sendType = sendType;
    }

    public boolean isAdminReply() {
        return isAdminReply;
    }

    public String getEa() {
		return ea;
	}

	public void setEa(String ea) {
		this.ea = ea;
	}

	public String getEaName() {
		return eaName;
	}

	public void setEaName(String eaName) {
		this.eaName = eaName;
	}

	public void setAdminReply(boolean isAdminReply) {
        this.isAdminReply = isAdminReply;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("messageId", messageId)
                .add("senderId", senderId)
                .add("senderState", senderState)
                .add("senderName", senderName)
                .add("profileImageUrl", profileImageUrl)
                .add("content", content)
                .add("contentType", contentType)
                .add("isUplink", isUplink)
                .add("datetime", datetime)
                .add("adminUserId", adminUserId)
                .add("adminUserName", adminUserName)
                .add("adminUserState", adminUserState)
                .add("startMark", startMark)
                .add("sessionId", sessionId)
                .add("sendType", sendType)
                .add("receiveUserName", receiveUserName)
                .add("receiveUserState", receiveUserState)
                .add("isAdminReply", isAdminReply)
                .add("eaName", eaName)
                .add("ea", ea)
                .toString();
    }
}
