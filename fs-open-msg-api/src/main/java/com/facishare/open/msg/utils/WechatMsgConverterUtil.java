package com.facishare.open.msg.utils;

import com.facishare.qixin.api.model.message.content.Audio;
import com.facishare.qixin.api.model.message.content.Image;
import com.facishare.qixin.api.model.message.content.Location;
import com.google.gson.Gson;

/**
 * Created by fengyh on 2016/10/27.
 *
 * 为了统一开平消息系统投递消息的接口，
 * 用这个工具类把各种微信消息(文本，图片,语音等)转换成WechatMessageVO的content.
 *
 * 这样以后在扩展消息类型时，只需要更新这个工具类，不需要重新发布开平消息系统。
 */
public class WechatMsgConverterUtil {

    /**
     * 企信文本消息格式
     * */
    public String FromQixinTextMsgContent(String textMsg) {
        return textMsg;
    };

    /**
     * 企信图片消息格式
     * */
    public String FromQixinImgMsgContent(Image imgMsg) {
        return imgMsg.toJson();
    };

    /**
     * 企信语音消息格式
     * */
    public String FromQixinAudioMsgContent(Audio audioMsg) {
        return audioMsg.toJson();
    };

    /**
     * 企信地理位置消息格式
     * */
    public String FromQixinLocationMsgContent(Location locationMsg) {
       return locationMsg.toJson();
    }
}
