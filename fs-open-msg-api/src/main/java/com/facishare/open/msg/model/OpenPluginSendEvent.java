package com.facishare.open.msg.model;

import io.protostuff.Tag;

import com.facishare.open.msg.model.base.ProtoBase;

public class OpenPluginSendEvent extends ProtoBase {

	private static final long serialVersionUID = 154258717247288146L;

	@Tag(1)
    private String enterpriseAccount; //企业号

    @Tag(2)
    private int senderId; //事件发送者

    @Tag(3)
    private String sessionId;

    @Tag(4)
    private String sessionType;

    @Tag(5)
    private String subCategory;

    @Tag(6)
    private String parentSessionId;

    @Tag(7)
    private String parentSessionType;

    @Tag(8)
    private String instrument; //对应PluginInstrument下的所有指令

    @Tag(9)
    private String appId;

    @Tag(10)
    private long createTime = System.currentTimeMillis();

    @Tag(11)
    private String upEnterpriseAccount;

    @Tag(100)
    private String biz;
    
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public int getSenderId() {
		return senderId;
	}

	public void setSenderId(int senderId) {
		this.senderId = senderId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getSessionType() {
		return sessionType;
	}

	public void setSessionType(String sessionType) {
		this.sessionType = sessionType;
	}

	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	public String getParentSessionId() {
		return parentSessionId;
	}

	public void setParentSessionId(String parentSessionId) {
		this.parentSessionId = parentSessionId;
	}

	public String getParentSessionType() {
		return parentSessionType;
	}

	public void setParentSessionType(String parentSessionType) {
		this.parentSessionType = parentSessionType;
	}

	public String getInstrument() {
		return instrument;
	}

	public void setInstrument(String instrument) {
		this.instrument = instrument;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}

	public String getBiz() {
		return biz;
	}

	public void setBiz(String biz) {
		this.biz = biz;
	}

	@Override
    public String toString() {
        return "OpenMessageSendEvent{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", sessionType='" + sessionType + '\'' +
                ", appId='" + appId + '\'' +
                ", senderId=" + senderId +
                ", subCategory=" + subCategory +
                ", parentSessionId=" + parentSessionId +
                ", parentSessionType=" + parentSessionType +
                ", instrument=" + instrument +
                ", biz=" + biz +
                ", createTime=" + createTime +
                ", upEnterpriseAccount='" + upEnterpriseAccount + '\'' +
                '}';
    }

}
