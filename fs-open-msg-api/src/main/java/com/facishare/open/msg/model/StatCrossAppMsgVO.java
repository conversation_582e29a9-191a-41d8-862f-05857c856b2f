package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;
import java.util.List;

/**
 * 内部服务号统计消息
 * Created by 黄虎平 on 2017/08/22.
 */
public class StatCrossAppMsgVO implements Serializable {
    
	private static final long serialVersionUID = 4255828732211454145L;

	/**
	 * 反馈消息用户数
	 */
	private Integer upMsgUserCount;
    
    /**
     * 反馈消息条数
     */
    private Integer upMsgCount;
    
    /**
     * 客服回复消息数
     */
    private Integer customerReplyMsgCount;
    
    /**
     * 企业反馈消息数
     */
    private List<EnterpriseStatMsgVO> enterpriseStatMsgs;
    
	public Integer getUpMsgUserCount() {
		return upMsgUserCount;
	}

	public void setUpMsgUserCount(Integer upMsgUserCount) {
		this.upMsgUserCount = upMsgUserCount;
	}

	public Integer getUpMsgCount() {
		return upMsgCount;
	}

	public void setUpMsgCount(Integer upMsgCount) {
		this.upMsgCount = upMsgCount;
	}

	public Integer getCustomerReplyMsgCount() {
		return customerReplyMsgCount;
	}

	public void setCustomerReplyMsgCount(Integer customerReplyMsgCount) {
		this.customerReplyMsgCount = customerReplyMsgCount;
	}

	public List<EnterpriseStatMsgVO> getEnterpriseStatMsgs() {
		return enterpriseStatMsgs;
	}

	public void setEnterpriseStatMsgs(List<EnterpriseStatMsgVO> enterpriseStatMsgs) {
		this.enterpriseStatMsgs = enterpriseStatMsgs;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("upMsgUserCount", upMsgUserCount)
                .add("upMsgCount", upMsgCount)
                .add("customerReplyMsgCount", customerReplyMsgCount)
                .add("enterpriseStatMsgs", enterpriseStatMsgs)
                .toString();
    }
	
}
