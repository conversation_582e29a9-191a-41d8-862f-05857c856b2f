package com.facishare.open.msg.model;

import com.facishare.open.msg.constant.OpenMessageTypeEnum;
import com.google.common.base.Defaults;
import com.google.gson.annotations.SerializedName;

import java.util.Map;

/**
 * Created by liuyu123 on 2015/12/23.
 */
public class QixinMessageContentVO {

    @SerializedName("Type")
    private OpenMessageTypeEnum type;

    @SerializedName("Title")
    private String title;

    @SerializedName("DefaultSummary")
    private String defaultSummary;

    @SerializedName("SpecialSummary")
    private Map<Integer, String> specialSummary;

    @SerializedName("MessageContent")
    private Map<String, Object> param;

    public OpenMessageTypeEnum getType() {
        return type;
    }

    public void setType(OpenMessageTypeEnum type) {
        this.type = type;
    }

    public Map<String, Object> getParam() {
        return param;
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    public String getDefaultSummary() {
        return defaultSummary;
    }

    public void setDefaultSummary(String defaultSummary) {
        this.defaultSummary = defaultSummary;
    }

    public Map<Integer, String> getSpecialSummary() {
        return specialSummary;
    }

    public void setSpecialSummary(Map<Integer, String> specialSummary) {
        this.specialSummary = specialSummary;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "QixinMessageContentVO{" +
                "type=" + type +
                ", title='" + title + '\'' +
                ", defaultSummary='" + defaultSummary + '\'' +
                ", specialSummary=" + specialSummary +
                ", param=" + param +
                '}';
    }
}
