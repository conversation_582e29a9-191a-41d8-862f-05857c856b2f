package com.facishare.open.msg.model;

import java.io.Serializable;

import com.google.common.base.MoreObjects;

/**
 * 上行消息用户数统计参数
 */
public class CountUpMsgUserNumByAppIdArg implements Serializable {

	private static final long serialVersionUID = -6789948482830053007L;

	/**
	 *企业账号 
	 */
	private String ea;
	
	/**
	 * 服务号id
	 */
	private String appId;

	/**
	 * 开始时间
	 */
	private Long beginTime;

	/**
	 * 结束时间
	 */
	private Long endTime;

	public String getEa() {
		return ea;
	}

	public void setEa(String ea) {
		this.ea = ea;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Long getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Long beginTime) {
		this.beginTime = beginTime;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}
	
	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this).add("ea", ea)
				.add("appId", appId)
				.add("beginTime", beginTime)
				.add("endTime", endTime).toString();
	}
}
