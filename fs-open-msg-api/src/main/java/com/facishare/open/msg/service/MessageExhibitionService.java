package com.facishare.open.msg.service;

import java.util.List;
import java.util.Map;

import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageExhibitionResult;

/**
 *
 * <AUTHOR>
 * @date 2016年3月4日
 *
 * history：
 * 2017.8.8 hardy补充注释
 */

public interface MessageExhibitionService {

    /**
     * 查询指定企业下，一个服务号的所有用户消息会话。
     * 返回结果 会按照用户分类, web端可以展示有哪些用户上行了消息以及最后一条消息摘要, 用户会话的状态是否是已回复等。
     * */
    @Deprecated
    MessageExhibitionResult<Pager<UserSessionVO>> queryAllUserSessions(int currentPage, int pageSize,
            Map<String, Object> paramMap);

    MessageExhibitionResult<Pager<UserSessionVO>> queryAllUserSessions(QueryAllUserSessionVO arg);

    /**
     * 查询指定企业下，一个服务号下，指定用户的消息会话。
     * 返回该用户相关的消息列表，包括 用户上行的消息和客服的回复。
     * */
    @Deprecated
    MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> querySingleUserSessionMessages(int currentPage,
            int pageSize, Map<String, Object> paramMap);

    MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> querySingleUserSessionMessages(QuerySingleUserSessionVO arg);

    /**
     * 设置服务号中某个用户会话的状态。
     *
     * 在web端 应用->服务号工作台->服务号->用户消息 下，选中某个用户会话点进去，
     * 消息系统这里会发生两个调用，一个是updateSingleUserSessionStatus(状态为未回复)。
     * 另一个是querySingleUserSessionMessages()查用户会话下面的消息列表。
     * */
    @Deprecated
    MessageExhibitionResult<Void> updateSingleUserSessionStatus(String ea, String sessionId, long lastMessageId,
            int status);

    MessageExhibitionResult<Void> updateSingleUserSessionStatus(UpdateSingleUserSessionVO arg);


    @Deprecated
    MessageExhibitionResult<Boolean> existUnReadUserSessions(String ea, String appId, long lastMessageId);

    MessageExhibitionResult<Boolean> existUnReadUserSessions(ExistUnReadUserSessionVO arg);


    @Deprecated
    MessageExhibitionResult<Long> countSingleUserSessionUnReadMessages(String ea, String sessionId, long lastMessageId);

    MessageExhibitionResult<Long> countSingleUserSessionUnReadMessages(CountSingleUserSessionUnReadMessageVO arg);


    /**
     *
     * 查询appid是不是有 未读未回复的消息存在。
     *
     * 返回 Map<appid, 有/无  未读未回复消息存在>
     * */
    @Deprecated
    MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessionsByAppIds(String ea, List<String> appIds);

    MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessionsByAppIds(QueryUnReadUserSessionsByAppIdVO arg);

    /**
     * 查询应用是否有未读session。用于做列表级的红点显示.
     *
     * 场景：web端 应用->服务号工作台下面的服务号，每次刷新页面时，判断每个服务号上是否加个红点，
     * 就会用到这个接口。
     *
     * @param ea
     * @param appIds
     * @return
     */
    @Deprecated
    MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessions(String ea, List<String> appIds);

    MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessions(QueryUnReadUserSessionVO arg);

    /**
     * 重置应用未读session。用于做列表级的红点显示.
     *
     * 场景：web端 应用->服务号工作台下面的服务号，选中一个服务号点击，
     * 用到这个接口消除这个服务号在 web端 应用->服务号工作台->用户消息 上展示的红点。
     *
     * 这里的红点消除，并没有意味着所有用户消息都已经被处理了，而只能表明有客服已经进到用户消息会话中了。
     *
     * 那么红点什么时候被设置呢？当有新的用户消息上来的时候。
     *
     *
     * 如果服务号下的红点除了 用户消息以外，还有别的子类也有红点，那么
     * web端 应用->服务号工作台->服务号上的红点 不会消除，该红点由应用中心控制。
     *
     *
     * @param ea
     * @param appIds
     * @return
     */
    @Deprecated
    MessageExhibitionResult<Void> resetUnReadUserSessions(String ea, List<String> appIds);

    MessageExhibitionResult<Void> resetUnReadUserSessions(ResetUnReadUserSessionVO arg);

    /**
     * @param ea
     * @param messageId
     * @param markUserId 标记人employeeId 格式 123
     * @param starStatus 0:未星标 1：已星标
     * @return
     */
    MessageExhibitionResult<Void> updateMsgStarStatus(String ea, long messageId, String markUserId, int starStatus);

    MessageExhibitionResult<Void> updateMsgStarStatus(UpdateMsgStarStatusVO arg);

    /**
     * 获取所有星标消息
     * @param currentPage
     * @param pageSize
     * @param paramMap
     * ea  企业账号
     * appId 应用Id
     * @return
     */
    MessageExhibitionResult<Pager<StartMessagesVO>> queryAllStarMessages(int currentPage,
            int pageSize, Map<String, Object> paramMap);

    MessageExhibitionResult<Pager<StartMessagesVO>> queryAllStarMessages(QueryAllStarMessageVO arg);

    /**
     * 获取所有导出消息
     * @param beginTime 可为null 表示不限
     * @param endTime 可为null 表示不限
     * @param paramMap
     * ea  企业账号
     * appId 应用Id
     * starMark 不传表示所有 是否星标 0:未标星 1：星标
     * @return
     */
    MessageExhibitionResult<List<ExportMsgVO>> queryAllExportMessages(Long beginTime,Long endTime, Map<String, Object> paramMap);
    
    MessageExhibitionResult<List<ExportMsgVO>> queryAllCrossExportMessages(Long beginTime, Long endTime, Map<String, Object> paramMap);

    /**
     * 获取所有导出消息数量
     * @param beginTime 可为null 表示不限
     * @param endTime 可为null 表示不限
     * @param paramMap
     * ea  企业账号
     * appId 应用Id
     * starMark 不传表示所有 是否星标 0:未标星 1：星标
     * @return
     */
    MessageExhibitionResult<Long> countExportMessages(Long beginTime,
            Long endTime, Map<String, Object> paramMap);


    /**
     * 查询群发消息次数
     * @param appId
     * @param MessageSendTypeEnum
     * @return
     */
    MessageExhibitionResult<Long> countSendNumByAppId(String ea, String appId, MessageSendTypeEnum messageSendTypeEnum);

    /**
     * 查询消息上行条数
     * @param appId: 服务号appID，开放平台派发
     * @param ea :服务号所属企业账号
     * @return:  到目前为止，这个服务号收到的消息记录总数
     */
    MessageExhibitionResult<Long> countReceiveNumByAppId(String ea, String appId);

    /**
     * 查询用户上行消息条数
     * @param CountReceiveMsgNumByAppIdArg
     * @return:  时间段内这个服务号收到的消息记录总数
     */
    MessageExhibitionResult<Long> countReceiveMsgNumByAppId(CountReceiveMsgNumByAppIdArg countReceiveMsgNumByAppIdArg);
    
    /**
     * 查询上行消息用户数
     * @param CountUpMsgUserNumByAppIdArg
     * @return:  时间段内这个服务号收到的上行消息用户数
     */
    MessageExhibitionResult<Long> countUpMsgUserNumByAppId(CountUpMsgUserNumByAppIdArg countUpMsgUserNumByAppIdArg);
    
    /**
     * 查询客服回复消息条数
     * @param CountReplyMsgNumByAppIdArg
     * @return:  时间段内这个服务号回复的消息记录总数
     */
    MessageExhibitionResult<Long> countReplyMsgNumByAppId(CountReplyMsgNumByAppIdArg countReplyMsgNumByAppIdArg);
    
    /**
     * 查询微信用户openID或者企业的内部用户到目前为止上行的消息条数
     * @param appId : 纷享服务号的appId, 注意，这里不要用微信服务号的appId.
     * @param ea: 发送者对应的客服人员所在企业的账号
     * @param sender:可以是上行消息的微信用户openID, 或者是企业内部userid
     *
     * @return:   <用户, 上行消息的条数>的map. 如果用户没有上行过消息，则条数为0. 查询该用户失败，则条数为-1.
     */
    MessageExhibitionResult<Map<String, Long>> countSendNumBySender(String appId, String ea, List<String> sender);

    /**
     * 查询微信用户openID的最后一条上行消息时间
     * @param appId : 纷享服务号的appId, 注意，这里不要用微信服务号的appId.
     * @param ea: 发送者对应的客服人员所在企业的账号
     * @param sender:可以是上行消息的微信用户openID
     *
     * @return:  最后上行消息的时间，以秒为单位。 查询该用户失败，则条数为-1.
     */
    MessageExhibitionResult<Long>  getLastUpMsgTime(String appId, String ea, String sender);



    /**
     * 根据时间段，获取不同消息类型(自动回复，人工回复等)服务号的appID列表
     * @param startTime: 查询的开始时间
     * @param endTime :查询的结束时间
     * @param sendTypeEnumList : @see MessageSendTypeEnum, 可以同时传入过个sendtype.
     *比如要统计自动回复，就包括 关键字自动回复和默认自动回复，那么同时传入 3和5即可。
     *要统计人工回复，传4就可以。
     *
     * @return:  服务号的appID列表
     */
    MessageExhibitionResult<List<String>> getAppIdList(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList);


    /**
     * 根据时间段，获取不同消息类型(自动回复，人工回复等)回复消息的数量
     * @param startTime: 查询的开始时间
     * @param endTime :查询的结束时间
     * @param sendTypeEnumList : @see MessageSendTypeEnum, 可以同时传入过个sendtype.
     *比如要统计自动回复，就包括 关键字自动回复和默认自动回复，那么同时传入 3和5即可。
     *要统计人工回复，传4就可以。
     *
     * @return:  消息总数
     */
    MessageExhibitionResult<Long> countReplyMsgNum(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList);
    
    /**
     * 根据时间段统计内部服务号的消息情况
     * @param startTime: 查询的开始时间
     * @param endTime :查询的结束时间
     * @param appId : 服务号Id
     *
     * @return:  @see StatInternalAppMsgVO
     */
    MessageExhibitionResult<StatInternalAppMsgVO> statInternalAppMsg(Long startTime, Long endTime, String appId, String ea, Boolean needDetail);
    
    /**
     * 根据时间段统计互联服务号的消息情况
     * @param startTime: 查询的开始时间
     * @param endTime :查询的结束时间
     * @param appId : 互联服务号Id
     *
     * @return:  @see StatCrossAppMsgVO
     */
    MessageExhibitionResult<StatCrossAppMsgVO> statCrossAppMsg(Long startTime, Long endTime, String appId, String ea, Boolean needDetail);
    
    /**
     * 根据时间段统计客服人员反馈消息数
     * @param startTime: 查询的开始时间
     * @param endTime :查询的结束时间
     * @param appId : 服务号Id
     *
     * @return:  @see StatCustomerMsgVO
     */
    MessageExhibitionResult<StatCustomerMsgVO> statCustomerMsg(Long startTime, Long endTime, String appId, String ea);
}
