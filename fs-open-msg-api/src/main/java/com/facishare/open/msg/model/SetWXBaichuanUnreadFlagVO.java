package com.facishare.open.msg.model;

import com.facishare.qixin.api.model.open.WorkbenchType;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/12/29.
 */
public class SetWXBaichuanUnreadFlagVO implements Serializable {
    private static final long serialVersionUID = -4023786574818119851L;

    private String enterpriseAccount;

    /**
     * 当前只支持
     * 微信外联 通知窗口  WEIXIN_BC_NOTICE(6);
     * */
    private WorkbenchType type;
    /**E.fs.xxx 最后这一部分*/
    private int employeeId;
    /**漂数*/
    private Integer notReadCount;
    /**红点*/
    private Boolean notReadFlag;

    public SetWXBaichuanUnreadFlagVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public WorkbenchType getType() {
        return type;
    }

    public void setType(WorkbenchType type) {
        this.type = type;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

    public Integer getNotReadCount() {
        return notReadCount;
    }

    public void setNotReadCount(Integer notReadCount) {
        this.notReadCount = notReadCount;
    }

    public Boolean getNotReadFlag() {
        return notReadFlag;
    }

    public void setNotReadFlag(Boolean notReadFlag) {
        this.notReadFlag = notReadFlag;
    }

    @Override
    public String toString() {
        return "SetWXBaichuanUnreadFlagVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", type=" + type +
                ", employeeId=" + employeeId +
                ", notReadCount=" + notReadCount +
                ", notReadFlag=" + notReadFlag +
                '}';
    }
}
