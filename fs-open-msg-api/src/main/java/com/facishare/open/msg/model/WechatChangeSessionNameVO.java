package com.facishare.open.msg.model;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/12/27.
 *
 * 修改企信群会话名称
 */
public class WechatChangeSessionNameVO implements Serializable {

    private static final long serialVersionUID = 2494240698978292734L;

    private String enterpriseAccount;
    /**操作者账号， E.fs.xxx或者W.fs.xxx 这种格式  */
    private String fromEmployee;
    /**微信群ID*/
    private String wechatSessionID;
    /**微信群 新名*/
    private String sessionNewName;

    public WechatChangeSessionNameVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getFromEmployee() {
        return fromEmployee;
    }

    public void setFromEmployee(String fromEmployee) {
        this.fromEmployee = fromEmployee;
    }

    public String getWechatSessionID() {
        return wechatSessionID;
    }

    public void setWechatSessionID(String wechatSessionID) {
        this.wechatSessionID = wechatSessionID;
    }

    public String getSessionNewName() {
        return sessionNewName;
    }

    public void setSessionNewName(String sessionNewName) {
        this.sessionNewName = sessionNewName;
    }

    @Override
    public String toString() {
        return "WechatChangeSessionNameVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", fromEmployee='" + fromEmployee + '\'' +
                ", wechatSessionID='" + wechatSessionID + '\'' +
                ", sessionNewName='" + sessionNewName + '\'' +
                '}';
    }
}
