package com.facishare.open.msg.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * Created by huanghp on 2018/01/15.
 */
@Data
@Setter
@Getter
public class UpdateEaConnRemindSessionInfo implements Serializable {
    private static final long serialVersionUID = 2935394221087197629L;

    /**
     * 更新类型 1：session 配置  2：摘要   3：未读数
     */
    private UpdateTypeEnum updateType;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 上游企业账号
     */
    private String upStreamEa;
    
    /**
     * updateType=1 时必传，session配置信息
     */
    private UpdateSessionConfigVo updateSessionConfigVo;
    
    /**
     * updateType=2 时必传，发送摘要信息参数
     */
    private SendSummaryMessageVo sendSummaryMessageVo;
    
    /**
     * 更新未度数信息参数
     */
    private UpdateUnReadNumVo updateUnReadNumVo;
    
    @Data
    @Setter
    @Getter
    public static class UpdateSessionConfigVo implements Serializable {
        private String title;
        private String portrait;
        //点击session后跳转的url地址
        private String forwardURL;  
        private List<String> downStreamEas;
    }
    
    @Data
    @Setter
    @Getter
    public static class SendSummaryMessageVo implements Serializable {
        //ea和usreid是必填字段
        private String ea;
        private Integer userId;
        
        private String summary;
    }
    
    @Data
    @Setter
    @Getter
    public static class UpdateUnReadNumVo implements Serializable {
        //ea和usreid是必填字段
        private String ea;
        private Integer userId;
        
        //未处理数
        private Integer notDealCount;
        
        //未读取数
        private Integer notReadCount;
        
        //未读取数
        private Boolean notReadFlag;
    }
    

    @Getter
    public static enum UpdateTypeEnum {
    	UPDATESESSION(1),UPDATESUMMARY(2),UPDATESTATUS(3),
    	;
    	
    	UpdateTypeEnum(Integer type) {
    		this.type = type;
    	}
    	
    	private Integer type;
    }
    
}
