package com.facishare.open.msg.model;

import java.io.Serializable;

import com.google.common.base.MoreObjects;

/**
 * 
 * <AUTHOR>
 * @date 2016年9月8日
 */

public class UpdateSessionStatusVO implements Serializable {
    
    private static final long serialVersionUID = -1553199112963206727L;

    /**
     * 企业账号
     */
    protected String enterpriseAccount;
    
    /**
     * 应用Id
     */
    protected String appId;
    
    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 漂数
     */
    private Integer notReadCount;//漂数

    /**
     * 红点标示
     */
    private Boolean notReadFlag;//红点标示

    /**
     * 需要调整飘数或者红点的用户的userid.
     * 服务号的 飘数和红点控制，这个参数必传。
     * */
    private int userId;
    
    /**
     * 是否免打扰 true 表示免打扰
     */
    private Boolean notStrongNotification;

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getNotReadCount() {
        return notReadCount;
    }

    public void setNotReadCount(Integer notReadCount) {
        this.notReadCount = notReadCount;
    }

    public Boolean getNotReadFlag() {
        return notReadFlag;
    }

    public void setNotReadFlag(Boolean notReadFlag) {
        this.notReadFlag = notReadFlag;
    }

    public Boolean getNotStrongNotification() {
		return notStrongNotification;
	}

	public void setNotStrongNotification(Boolean notStrongNotification) {
		this.notStrongNotification = notStrongNotification;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("appId", appId)
                .add("sessionId", sessionId)
                .add("enterpriseAccount", enterpriseAccount)
                .add("notReadCount", notReadCount)
                .add("notReadFlag", notReadFlag)
                .add("notStrongNotification", notStrongNotification)
                .toString();
    }

}
