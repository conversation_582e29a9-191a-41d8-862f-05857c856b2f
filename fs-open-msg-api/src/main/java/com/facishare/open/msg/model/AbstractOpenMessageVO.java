package com.facishare.open.msg.model;

import com.facishare.open.msg.constant.OpenMessageTypeEnum;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by liuyu123 on 2015/12/23.
 */
public abstract class AbstractOpenMessageVO implements Serializable {

    private static final long serialVersionUID = 7958749342304380307L;
    /**
     * 消息接收者的企业账号 对应企信的 enterpriseAccount
     */
    protected String enterpriseAccount;

    /**
     * 消息内容

     */
    protected Map<String, Object> param;

    /**
     * 消息推送的显示文本
     */
    protected String title;

    /**
     * 消息是否需要推送
     */
    protected Boolean isNeedThirdPartyPush;

    /**
     * 企信session列表里的显示文本, 即当此消息是最新一条消息时，在企信通话列表里显示的内容，默认的Summary
     */
    protected String defaultSummary;

    /**
     * 消息类型 支持： lucky_money, image_text
     */
    protected OpenMessageTypeEnum type;

    /**
     * 用于防止重复消息发送
     */
    protected String postId;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public Map<String, Object> getParam() {
        return param;
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    public OpenMessageTypeEnum getType() {
        return type;
    }

    public void setType(OpenMessageTypeEnum type) {
        this.type = type;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDefaultSummary() {
        return defaultSummary;
    }

    public void setDefaultSummary(String defaultSummary) {
        this.defaultSummary = defaultSummary;
    }


    public Boolean getNeedThirdPartyPush() {
        return isNeedThirdPartyPush;
    }

    public void setNeedThirdPartyPush(Boolean needPush) {
        isNeedThirdPartyPush = needPush;
    }


    public boolean validateParam() {
        return !(StringUtils.isEmpty(this.getTitle())
                || StringUtils.isEmpty(this.getDefaultSummary())
                || StringUtils.isEmpty(this.getPostId())
                || StringUtils.isEmpty(this.getType())
                || StringUtils.isEmpty(this.getEnterpriseAccount())
                || StringUtils.isEmpty(this.getNeedThirdPartyPush()));
    }

    @Override
    public String toString() {
        return "AbstractOpenMessageVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", title='" + title + '\'' +
                ", isNeedThirdPartyPush=" + isNeedThirdPartyPush +
                ", defaultSummary='" + defaultSummary + '\'' +
                ", type=" + type +
                ", postId='" + postId + '\'' +
                '}';
    }
}
