package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import com.google.common.base.MoreObjects;

/**
 * Created by huanghp on 2017/9/25.
 */
public class SendWechatPromptTextMessageArg implements Serializable {
 
	private static final long serialVersionUID = 3857566787384992555L;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 企业账号
     */
    private String ea;
    
    /**
     * 微信端用户openId
     */
    private String wxOpenId;
    
    /**
     * 提醒内容
     */
    private String promptContent;
    
    /**
     * 接收客服人员列表
     */
    private List<Integer> receivers;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 是否改变排序
     */
    private boolean updateOrder;

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getEa() {
		return ea;
	}

	public void setEa(String ea) {
		this.ea = ea;
	}

	public String getWxOpenId() {
		return wxOpenId;
	}

	public void setWxOpenId(String wxOpenId) {
		this.wxOpenId = wxOpenId;
	}

	public String getPromptContent() {
		return promptContent;
	}

	public void setPromptContent(String promptContent) {
		this.promptContent = promptContent;
	}

	public List<Integer> getReceivers() {
		return receivers;
	}

	public void setReceivers(List<Integer> receivers) {
		this.receivers = receivers;
	}

	public boolean isUpdateOrder() {
		return updateOrder;
	}

	public void setUpdateOrder(boolean updateOrder) {
		this.updateOrder = updateOrder;
	}
    
    public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	@Override
    public String toString() {
		return MoreObjects.toStringHelper(this).add("appId", appId)
				.add("ea", ea).add("wxOpenId", wxOpenId)
				.add("promptContent", promptContent)
				.add("messageType", messageType)
				.add("receivers", receivers).add("updateOrder", updateOrder)
				.toString();
    }
}
