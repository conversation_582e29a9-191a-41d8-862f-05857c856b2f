package com.facishare.open.msg.model;

import java.io.Serializable;

import com.google.common.base.MoreObjects;

/**
 * 
 * <AUTHOR>
 * @date 2016年3月4日
 */

public class SingleUserSessionMessagesVO implements Serializable {

    private static final long serialVersionUID = -557136835590158137L;

    /**
     * 消息ID
     */
    private long messageId;

    /**
     * 发送者 为应用ID或者fsUserId（格式为E.fs.123）
     */
    private String senderId;

    /**
     * 发送者名称 为应用名称或者用户昵称
     */
    private String senderName;

    /**
     * 发送者状态 1.正常。2.已停用
     */
    private Integer senderState;

    /**
     * 发送类型
     * @see com.facishare.open.msg.constant.MessageSendTypeEnum
     */
    private Integer sendType;
    
    /**用户所在公司名*/
    private String eaName;

    /**
     * 应用图标或者用户图像地址
     */
    private String profileImageUrl;

    /**
     * 消息内容, UrlEncode编码
     */
    private String content;

    /**
     * 消息类型: 位置 L/音频 A/图片 I/文档 D/文本T 等
     */
    private String contentType;

    /**
     * 是否上行消息: senderId为fsUserId即上行消息
     */
    private boolean isUplink;

    /**
     * 消息发送时间
     */
    private long datetime;

    /**
     * 展示下行消息时用于区分管理员身份信息,格式为E.fs.123
     */
    private String adminUserId;
    
    /**
     * 星标导出
     * 管理员回复时，管理员名称
     */
    private String adminUserName;

    /**
     * 管理员回复，管理员状态 1.正常。2.已停用.
     */
    private Integer adminUserState;

    /**
     * 星标导出
     * 是否星标 0:非星标  1：星标
     */
    private int startMark;
    
    /**
     *  星标导出
     *  消息的会话ID, 由"appId" + "-" + "fsUserId"组成
     *  服务号会话消息将来会允许删除, 如果同步企信会话ID会导致不唯一
     */
    private String sessionId;
    
    /**
     *  星标导出
     *  接收者名称
     */
    private String receiveUserName;

    public long getMessageId() {
        return messageId;
    }

    public void setMessageId(long messageId) {
        this.messageId = messageId;
    }

    public String getSenderId() {
        return senderId;
    }

    public void setSenderId(String senderId) {
        this.senderId = senderId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public Integer getSenderState() {
        return senderState;
    }

    public void setSenderState(Integer senderState) {
        this.senderState = senderState;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public boolean isUplink() {
        return isUplink;
    }

    public void setUplink(boolean isUplink) {
        this.isUplink = isUplink;
    }

    public long getDatetime() {
        return datetime;
    }

    public void setDatetime(long datetime) {
        this.datetime = datetime;
    }

    public String getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(String adminUserId) {
        this.adminUserId = adminUserId;
    }

    public String getAdminUserName() {
        return adminUserName;
    }

    public void setAdminUserName(String adminUserName) {
        this.adminUserName = adminUserName;
    }

    public Integer getAdminUserState() {
        return adminUserState;
    }

    public void setAdminUserState(Integer adminUserState) {
        this.adminUserState = adminUserState;
    }

    public int getStartMark() {
        return startMark;
    }

    public void setStartMark(int startMark) {
        this.startMark = startMark;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public String getEaName() {
		return eaName;
	}

	public void setEaName(String eaName) {
		this.eaName = eaName;
	}

	public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    @Override
    public String toString() {
        return "SingleUserSessionMessagesVO{" +
                "messageId=" + messageId +
                ", senderId='" + senderId + '\'' +
                ", senderName='" + senderName + '\'' +
                ", senderState=" + senderState +
                ", sendType=" + sendType +
                ", profileImageUrl='" + profileImageUrl + '\'' +
                ", content='" + content + '\'' +
                ", contentType='" + contentType + '\'' +
                ", isUplink=" + isUplink +
                ", datetime=" + datetime +
                ", adminUserId='" + adminUserId + '\'' +
                ", adminUserName='" + adminUserName + '\'' +
                ", adminUserState=" + adminUserState +
                ", startMark=" + startMark +
                ", sessionId='" + sessionId + '\'' +
                ", receiveUserName='" + receiveUserName + '\'' +
                ", eaName='" + eaName + '\'' +
                '}';
    }
}
