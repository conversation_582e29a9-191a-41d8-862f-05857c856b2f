package com.facishare.open.msg.model;


import io.protostuff.Tag;

import com.facishare.open.msg.model.base.ProtoBase;
import com.google.common.base.MoreObjects;

public class WLServiceEvaluteNotify extends ProtoBase {
    
    private static final long serialVersionUID = 4931624838072167933L;

    /**
     * 服务号id.
     */
    @Tag(1)
    private String appId;

    /**
     * 评价链接Id
     */
    @Tag(2)
    private String msgLinkId;
    
    /**
     * openId
     */
    @Tag(3)
    private String wxOpenId;
    
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMsgLinkId() {
		return msgLinkId;
	}

	public void setMsgLinkId(String msgLinkId) {
		this.msgLinkId = msgLinkId;
	}

	public String getWxOpenId() {
		return wxOpenId;
	}

	public void setWxOpenId(String wxOpenId) {
		this.wxOpenId = wxOpenId;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this).add("appId", appId)
				.add("msgLinkId", msgLinkId).add("wxOpenId", wxOpenId)
				.toString();
	}
}
