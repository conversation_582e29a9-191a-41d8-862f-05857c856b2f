package com.facishare.open.msg.model;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import lombok.Data;
import org.bson.BsonDateTime;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.converters.DateConverter;
import org.mongodb.morphia.utils.IndexType;

import com.google.common.base.MoreObjects;

/**
 * 消息实体类:包括下行消息与上行消息
 * 
 * <AUTHOR>
 * @date 2016年3月1日
 */

@Data
@Entity(value = "open_msg", noClassnameStored = true)
public class OpenMsgDO implements Serializable {

    private static final long serialVersionUID = -8932001766192805390L;

    @Id
    private ObjectId id;

    /**
     *  企业帐号
     */
    private String enterpriseAccount;
    
    /**
     *  上游企业帐号
     */
    private String upEnterpriseAccount;

    /**
     * 消息ID 
     */
    private long msgId;

    /**
     *  消息类型    文本T/位置L/图片I/图文IMAGE_TEXT/文档D/表情E/ 日程IGT / 任务 IGT /群通知 LWN /投票LWV
     */
    private String msgType;

    /**
     *  消息内容
     */
    private String content;

    /**
     * 消息发送者:APP或人
     */
    private String sender;

    /**
     * 消息接受者:人或APP
     */
    private String receiver;

    /**
     *  消息发送的状态:1表示操作成功，2表示操作失败 3表示撤销
     */
    private int status;

    /**
     * 消息创建时间
     */
    private long createTime;

    /**
     * 企信创建消息时间
     */
    private long messageTime;

    /**
     * 消息修改的时间
     */
    private long updateTime;

    /**
     *  消息的会话ID, 一般由"appId" + "-" + "fsUserId"组成
     *  但如果是互联服务号的消息，会由"appId" + "-" + "enterpriseAccount" + "fsUserId"组成
     *  服务号会话消息将来会允许删除, 如果同步企信会话ID会导致不唯一
     */
    private String sessionId;

    /**
     *  下行消息的消息唯一的ID,用于消息去重
     */
    private String postId;

    /**
     *  企信对下行消息的返回状态
     */
    private String response;

    /**
     * 消息发送类型: 规则：正数表示 App到人的下行消息；负数表示人到APP的上行消息；0表示人与人消息；@see MessageSendTypeEnum
     * 1表示第三方推送, 2表示管理员推送, 3表示默认自动回复, 4表示管理员的回复, 5表示关键字回复, 6表示系统提示
     * 0表示其他<包括音视频，红包>, 
     * -1表示上行普通消息, -2表示上行事件消息
     * -4外联消息用户到客服,-5外联消息客服回复
     */
    private int sendType;

    /**
     * 消息处理状态: 
     * 1表示上行消息未读未回复,2表示上行消息已读未回复,3表示上行消息已读已回复,
     * 另外下行消息(默认自动回复)当作上行消息未读未回复状态1,下行消息(排除默认自动回复)当作上行消息已读已回复3
     */
    private int processStatus = 0;

    /**
     * 仅当管理员回复时,区分管理员身份信息,格式为E.fs.123
     */
    private String adminUserId;
    
    /**
     * 星标标记 0：未标记  1：表示已标记
     */
    private int starMark;
    
    /**
     * 标记人员Id,格式为E.fs.123
     */
    private String markUserId;

    //已经有一个时间戳字段的creatime了。为了不改动代码逻辑，又要支持自动过期索引，这里在加一个日期类型字段。
    private Date createTimeForExpire;

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("id", id)
                .add("enterpriseAccount", enterpriseAccount)
                .add("upEnterpriseAccount", upEnterpriseAccount)
                .add("msgId", msgId)
                .add("msgType", msgType)
                .add("content", content)
                .add("sender", sender)
                .add("receiver", receiver)
                .add("status", status)
                .add("createTime", createTime)
                .add("messageTime", messageTime)
                .add("updateTime", updateTime)
                .add("sessionId", sessionId)
                .add("postId", postId)
                .add("sendType", sendType)
                .add("processStatus", processStatus)
                .add("adminUserId", adminUserId)
                .add("starMark", starMark)
                .add("markUserId", markUserId)
                .toString();
    }

}
