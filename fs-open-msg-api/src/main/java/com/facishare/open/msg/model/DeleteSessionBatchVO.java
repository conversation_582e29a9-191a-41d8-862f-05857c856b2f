package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:50:36
 */
public class DeleteSessionBatchVO implements Serializable {

	private static final long serialVersionUID = 7526717974824163307L;

	/**
	 * 应用ID
	 */
	private String appId;

	/**
	 * 企业号
	 */
	private String enterpriseAccount;

	/**
	 * 是否删除所有消息
	 */
	private boolean deleteAllMessage;

	/**
	 * 是否隐藏
	 */
	private boolean hide;
	
	/**
	 * user列表
	 */
	private List<UserVO> userList;

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public boolean isDeleteAllMessage() {
		return deleteAllMessage;
	}

	public void setDeleteAllMessage(boolean deleteAllMessage) {
		this.deleteAllMessage = deleteAllMessage;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}

	public List<UserVO> getUserList() {
		return userList;
	}

	public void setUserList(List<UserVO> userList) {
		this.userList = userList;
	}
	
	/** 参数校验，如果参数没有问题，返回true
	 * @return
	 */
	public boolean validateParam() {
		if (StringUtils.isNotBlank(this.getAppId())
				&& StringUtils.isNotBlank(this.getEnterpriseAccount())
				&& this.getUserList() != null 
				&& !this.getUserList().isEmpty()){
			return true ;
		}
		return false ;
	}
	
	public String toJson () {
		
		return new Gson().toJson(this);
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this).append("appid ", appId)
                .append("enterpriseAccount", enterpriseAccount)
                .append("deleteAllMessage", deleteAllMessage)
                .append("hide", hide)
                .append("userList", userList)
                .toString();
	}

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((appId == null) ? 0 : appId.hashCode());
        result = prime * result + (deleteAllMessage ? 1231 : 1237);
        result = prime
                * result
                + ((enterpriseAccount == null) ? 0 : enterpriseAccount
                        .hashCode());
        result = prime * result + (hide ? 1231 : 1237);
        result = prime * result
                + ((userList == null) ? 0 : userList.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        DeleteSessionBatchVO other = (DeleteSessionBatchVO) obj;
        if (appId == null) {
            if (other.appId != null)
                return false;
        } else if (!appId.equals(other.appId))
            return false;
        if (deleteAllMessage != other.deleteAllMessage)
            return false;
        if (enterpriseAccount == null) {
            if (other.enterpriseAccount != null)
                return false;
        } else if (!enterpriseAccount.equals(other.enterpriseAccount))
            return false;
        if (hide != other.hide)
            return false;
        if (userList == null) {
            if (other.userList != null)
                return false;
        } else if (!userList.equals(other.userList))
            return false;
        return true;
    }
}

