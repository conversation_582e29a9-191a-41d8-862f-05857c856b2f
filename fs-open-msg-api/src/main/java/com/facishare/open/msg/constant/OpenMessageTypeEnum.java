package com.facishare.open.msg.constant;

import com.facishare.qixin.api.constant.MessageType;

/**
 * Created by liuyu123 on 2015/12/22.
 */
public enum OpenMessageTypeEnum {

    LUCKY_MONEY("lucky_money"),

    IMAGE_TEXT("image_text"),

    QY_LUCK_MONEY("qy_luck_money")
    ;

    private String description;

    private OpenMessageTypeEnum(String description) {

        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static OpenMessageTypeEnum valueOf(int ordinal) {
        if (ordinal < 0 || ordinal >= values().length) {
            throw new IndexOutOfBoundsException("Invalid ordinal");
        }
        return values()[ordinal];
    }
}
