package com.facishare.open.msg.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by liuyu123 on 2015/12/22.
 */
public class MessageContentVO {

    @SerializedName("SenderId")
    Integer senderId;

    @SerializedName("SenderSummary")
    String senderSummary;

    @SerializedName("ReceiverSummary")
    String receiverSummary;

    @SerializedName("Title")
    String title;

    /**
     * 消息的发送范围，ALL为群里所有人都能看到此消息，PART为群里部分人才能看到此消息
     * 只对群消息有效，如果是单人消息，此属性为空
     */
    @SerializedName("MessageRange")
    String messageRange;

    /**
     * 消息接收列表，元素为用户Id
     * 只对群消息有效，如果是单人消息，此属性为空
     */
    @SerializedName("ReceiverList")
    List<Integer> receiverList;

    /**
     * 是否需要第三方推送
     * Json text is "ThirdPartPush" in order to follow the naming convention of Qixin,
     * but the correct spelling should be "ThirdPartyPush".
     */
    @SerializedName("ThirdPartPush")
    Boolean isNeedThirdPartyPush;

    /**
     * 从企信发到客户端上消息内容，由客户端直接解析
     */
    @SerializedName("QixinContent")
    QixinMessageContentVO qixinMessageContentVO;

    public String getSenderSummary() {
        return senderSummary;
    }

    public void setSenderSummary(String senderSummary) {
        this.senderSummary = senderSummary;
    }

    public String getReceiverSummary() {
        return receiverSummary;
    }

    public void setReceiverSummary(String receiverSummary) {
        this.receiverSummary = receiverSummary;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Boolean getNeedThirdPartyPush() {
        return isNeedThirdPartyPush;
    }

    public void setNeedThirdPartyPush(Boolean needPush) {
        isNeedThirdPartyPush = needPush;
    }

    public QixinMessageContentVO getQixinMessageContentVO() {
        return qixinMessageContentVO;
    }

    public void setQixinMessageContentVO(QixinMessageContentVO qixinMessageContentVO) {
        this.qixinMessageContentVO = qixinMessageContentVO;
    }

    public Integer getSenderId() {
        return senderId;
    }

    public void setSenderId(Integer senderId) {
        this.senderId = senderId;
    }

    public String getMessageRange() {
        return messageRange;
    }

    public void setMessageRange(String messageRange) {
        this.messageRange = messageRange;
    }

    public List<Integer> getReceiverList() {
        return receiverList;
    }

    public void setReceiverList(List<Integer> receiverList) {
        this.receiverList = receiverList;
    }

    @Override
    public String toString() {
        return "MessageContentVO{" +
                "senderId=" + senderId +
                ", defaultSummary='" + senderSummary + '\'' +
                ", receiverSummary='" + receiverSummary + '\'' +
                ", title='" + title + '\'' +
                ", messageRange='" + messageRange + '\'' +
                ", receiverList=" + receiverList +
                ", isNeedThirdPartyPush=" + isNeedThirdPartyPush +
                ", qixinMessageContentVO=" + qixinMessageContentVO +
                '}';
    }
}
