package com.facishare.open.msg.model;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2017/7/25.
 */
@Data
public class SendMsgToEaConnServiceVO implements Serializable {
    private static final long serialVersionUID = 3857566787384992555L;

    /**上游企业*/
    private String upstreamEa;
    /**放接收者和消息*/
    private List<MessageVO> msgList;
    /**
     * web端消息中心管理员回复，使用  CROSS_ADMINISTRATOR_REPLY，
     * 普通的消息推送，使用 THIRD_PARTY_PUSH。
     * */
    private MessageSendTypeEnum messageSendType;
    
    private boolean lowPriority;
}
