package com.facishare.open.msg.model;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.util.StringUtils;

/**
 * 发送文本消息
 * @author: wangtao
 * @date: 2015年7月23日 上午9:58:38
 */
public class SendTextMessageVO extends MessageVO {

	
	private static final long serialVersionUID = 1L;

    private InternationalInfo contentInfo;
    private InternationalInfo summaryInfo;

    public InternationalInfo getContentInfo() {
        return contentInfo;
    }

    public void setContentInfo(InternationalInfo contentInfo) {
        this.contentInfo = contentInfo;
    }

    public InternationalInfo getSummaryInfo() {
        return summaryInfo;
    }

    public void setSummaryInfo(InternationalInfo summaryInfo) {
        this.summaryInfo = summaryInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("enterpriseAccount", enterpriseAccount)
                .append("appId", appId)
                .append("type", type)
                .append("postId", postId)
                .append("toUser", toUserList)
                .append("adminUserId", adminUserId)
                .toString();
    }

	/**
	 * 数据合法性校验
	 */
	@Override
	public boolean validateParam() {
        return !(getToUserList() == null || getToUserList().isEmpty()
                || StringUtils.isEmpty(this.getAppId())
                || StringUtils.isEmpty(this.getPostId())
                || StringUtils.isEmpty(this.getType())
                || StringUtils.isEmpty(this.getEnterpriseAccount())
                || StringUtils.isEmpty(this.content)
                || this.content.length() > 2000);
    }

}
