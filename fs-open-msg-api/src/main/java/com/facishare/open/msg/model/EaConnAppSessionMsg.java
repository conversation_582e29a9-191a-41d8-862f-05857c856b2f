package com.facishare.open.msg.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2017/4/17.
 */
@Setter
@Getter
@ToString
public class EaConnAppSessionMsg implements Serializable {
    private static final long serialVersionUID = 5893754556410627835L;

    /**该应用在开放平台分配到的appid*/
    private String appId;
    /**消息接收者所在企业账号。 E.fs.xxx 中间这部分 */
    private String enterpriseAccount;
    /**消息接收者列表.  E.fs.xxx 最后这部分 */
    private List<Integer> receivers;
    /**上游企业*/
    private String upstreamEnterprise;
    /**应用session消息摘要*/
    private String lastSummary;
}
