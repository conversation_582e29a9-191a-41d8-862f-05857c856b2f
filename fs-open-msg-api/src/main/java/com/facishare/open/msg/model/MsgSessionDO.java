package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

/**
 * @Description: 用于保存session操作记录
 * <AUTHOR>
 * @date 2015年8月25日 上午9:51:59
 */
@Entity(value = "msg_session", noClassnameStored = true)
public class MsgSessionDO implements Serializable {

    private static final long serialVersionUID = 3616710447185769829L;

    @Id
    private ObjectId id;

	/**
	 * 应用ID
	 */
	private String appId;

	/**
	 * 企业号
	 */
	private String enterpriseAccount;

	/**
	 * 用户ID
	 */
	private Integer userId;

	/**
	 * 会话ID 1. 创建查找session 2 批量 创建session 3. 删除session 4 .批量删除session
	 */
	private String sessionId = "";

	/**
	 * 创建时间
	 */
	private long createTime;

	/**
	 * 更新时间
	 */
	private long updateTime;

	/**
	 * 动作 1. 创建查找session 2. 删除session
	 */
	private int action;

	/**
	 * 请求内容
	 */
	private String request;

	/**
	 * 响应内容
	 */
	private String response;

	/**
	 * 响应状态 响应的状态1 成功 2 失败
	 */
	private int responseStatus;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public int getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(int responseStatus) {
        this.responseStatus = responseStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("id", id)
            .append("enterpriseAccount", enterpriseAccount)
            .append("appId", appId)
            .append("sessionId", sessionId)
            .append("userId", userId)
            .append("createTime", createTime)
            .append("action", action)
            .append("response", response)
            .append("messageTime", updateTime)
            .toString();
    }

}
