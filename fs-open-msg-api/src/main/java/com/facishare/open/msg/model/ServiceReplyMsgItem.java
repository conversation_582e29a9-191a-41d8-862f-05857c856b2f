package com.facishare.open.msg.model;


import io.protostuff.Tag;

import com.facishare.open.msg.model.base.ProtoBase;
import com.google.common.base.MoreObjects;

public class ServiceReplyMsgItem extends ProtoBase {
    
    private static final long serialVersionUID = 4931624838072167933L;

    /**
     * 服务号id.
     */
    @Tag(1)
    private String appId;

    /**
     * 企业账户
     */
    @Tag(2)
    private String enterpriseAccount;

    /**
     * 用户ID.
     */
    @Tag(3)
    private Integer userId;
    
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("appId", appId).add("enterpriseAccount", enterpriseAccount)
                .add("userId", userId).toString();
    }
}
