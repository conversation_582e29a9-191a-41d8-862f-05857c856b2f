package com.facishare.open.msg.model;

import com.google.common.base.MoreObjects;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:51:06
 */
public class SessionUserResult implements Serializable {

	private static final long serialVersionUID = 4259099410704913628L;

	/**
	 * 会话ID
	 */
	private String sessionId;
	
	/**
	 * 员工号
	 */
	private Integer employeeNumber;

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public Integer getEmployeeNumber() {
		return employeeNumber;
	}

	public void setEmployeeNumber(Integer employeeNumber) {
		this.employeeNumber = employeeNumber;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
				.add("sessionId", sessionId)
				.add("employeeNumber", employeeNumber)
				.toString();
	}
	
}

