package com.facishare.open.msg.model;

import com.facishare.qixin.api.model.OSS1SessionDefinition;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2017/4/17.
 */
@Setter
@Getter
@ToString
public class EaConnAppSessionConfig implements Serializable {

    private static final long serialVersionUID = 427512523535674661L;

    /**该应用在开放平台分配到的appid*/
    private String appId;
    /**上游企业*/
    private String upstreamEnterprise;
    /**下游企业列表。
     * 如果 downstreamEnterpriseList不空，那么回主动通知downstreamEnterpriseList 企业中的人来拉更新的配置。
     * 已经创建好应用以后，又修改了应用信息时，这个字段必填。 */
    private List<String> downstreamEnterpriseList;
    /**下游用户看到的是  应用名(上游企业名)，  这里的name, 传的是应用名。  */
    private String name;
    /**头像*/
    private String portraitPath;
    /**服务号描述*/
    private String description;
    /**消息免打扰开关是否显示*/
    private Boolean showBlockMsgSwitch;

    /**如果不需要指定自定义菜单，则传null
     * 如果要删除菜单，传空list*/
    private List<OSS1SessionDefinition.OSS1CustomMenuItme> customMenu;
    
    /**是否停用**/
    private boolean disable;
}
