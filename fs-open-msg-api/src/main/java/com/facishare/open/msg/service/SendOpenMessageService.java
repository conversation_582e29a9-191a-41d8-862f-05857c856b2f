package com.facishare.open.msg.service;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.SendAppToCMessageVO;
import com.facishare.open.msg.model.SendCToCMessageVO;
import com.facishare.open.msg.model.WechatMessageVO;
import com.facishare.open.msg.result.MessageResult;

/**
 * Created by liuyu123 on 2015/12/21.
 */
public interface SendOpenMessageService {

    /**
     * 发送人对人消息
     * @param param
     * @return
     */
    MessageResult sendCtoCMessage(SendCToCMessageVO param);

    /**
     * 发送APP对人消息
     * @param param
     * @return
     */
    MessageResult sendAppToCMessage(SendAppToCMessageVO param);

    /**
     * 发送APP对人消息 
     * @param param
     * @param messageSendType
     * @return
     */
    MessageResult sendAppToCMessage(SendAppToCMessageVO param, MessageSendTypeEnum messageSendType);



}
