package com.facishare.open.msg.result;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 发送模板消息返回参数
 * @author: wangtao
 * @date: 2015年7月21日 下午5:19:27
 */
public class TemplateMessageResult extends MsgBaseResult {
	
	private static final long serialVersionUID = 1L;

	public TemplateMessageResult(){
		super();
	}
	
	public TemplateMessageResult(MsgCodeEnum errCode ) {
		super(errCode);
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this)
		    .append("errorCode", this.errorCode)
		    .append("errorMsg", this.errorMsg)
		    .toString();
	}
}

