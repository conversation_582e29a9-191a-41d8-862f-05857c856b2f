package com.facishare.open.msg.constant;

/**
 * Created by liuyu123 on 2016/1/8.
 */
public enum GroupMessageRange {

    ALL("ALL"),
    PART("PART");

    private String description;

    private GroupMessageRange(String description) {

        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static GroupMessageRange valueOf(int ordinal) {
        if (ordinal < 0 || ordinal >= values().length) {
            throw new IndexOutOfBoundsException("Invalid ordinal");
        }
        return values()[ordinal];
    }
}
