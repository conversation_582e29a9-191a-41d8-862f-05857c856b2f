package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;


/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:51:11
 */
public class TemplateContentVO implements Serializable {

	private static final long serialVersionUID = 1L;
	
	public TemplateContentVO() {
		super();
	}
	
	public TemplateContentVO(String templeteId, String fillUrl, Map<String, String> params) {
		super();
		this.templeteId = templeteId;
		this.fillUrl = fillUrl;
		this.params = params;
	}
	
	/**
	 * 模板ID
	 */
	private String templeteId;
	
	/**
	 * 填充模板的地址（Http访问）
	 */
	private String fillUrl;
	
	/**
	 * 模板参数
	 */
	private Map<String , String> params ;
	
	public String getTempleteId() {
		return templeteId;
	}
	
	public void setTempleteId(String templeteId) {
		this.templeteId = templeteId;
	}
	
	public String getFillUrl() {
		return fillUrl;
	}
	
	public void setFillUrl(String fillUrl) {
		this.fillUrl = fillUrl;
	}
	
	public Map<String, String> getParams() {
		return params;
	}
	
	public void setParams(Map<String, String> params) {
		this.params = params;
	}
	
	public String toJson() {
		Map<String ,Object> allMap = new HashMap<String , Object>();
		allMap.put("TID", templeteId) ;//templeteId
		allMap.put("URL", fillUrl) ;//fillUrl
		Map<String, String> mapTemp = templateKeyTrans(params);
		allMap.put("P", mapTemp) ;//params
		return new Gson().toJson(allMap) ;
	}
	
	 
	/**  
	 * 转换成简写
	 * @param params
	 * @return
	 */
	private Map<String , String> templateKeyTrans(Map<String, String> params) {
		Map<String , String> mapTemp = new HashMap<String , String>();
		for(Entry<String,String> entry : params.entrySet()){
			if(StringUtils.isNotBlank( entry.getKey() )){
				String key = entry.getKey().replace("remark.DATA", "R")
						.replace("first.DATA", "F")
						.replace("url.DATA", "U")
						.replace("keyword1.DATA", "K1")
						.replace("keyword2.DATA", "K2")
						.replace("keyword3.DATA", "K3")
						.replace("keyword4.DATA", "K4")
						.replace("keyword5.DATA", "K5");
				mapTemp.put(key, entry.getValue());
			}
		}
		return mapTemp ;
	}

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((fillUrl == null) ? 0 : fillUrl.hashCode());
        result = prime * result + ((params == null) ? 0 : params.hashCode());
        result = prime * result
                + ((templeteId == null) ? 0 : templeteId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
			return true;
		}
        if (obj == null) {
			return false;
		}
        if (getClass() != obj.getClass()) {
			return false;
		}
        TemplateContentVO other = (TemplateContentVO) obj;
        if (fillUrl == null) {
            if (other.fillUrl != null) {
				return false;
			}
        } else if (!fillUrl.equals(other.fillUrl)) {
			return false;
		}
        if (params == null) {
            if (other.params != null) {
				return false;
			}
        } else if (!params.equals(other.params)) {
			return false;
		}
        if (templeteId == null) {
            if (other.templeteId != null) {
				return false;
			}
        } else if (!templeteId.equals(other.templeteId)) {
			return false;
		}
        return true;
    }
}

