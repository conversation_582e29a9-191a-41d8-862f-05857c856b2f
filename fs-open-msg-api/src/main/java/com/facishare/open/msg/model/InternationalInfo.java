package com.facishare.open.msg.model;

import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class InternationalInfo implements Serializable {

    private static final long serialVersionUID = -5491355970634003149L;

    @Tag(1)
    private String internationalKey;

    @Tag(2)
    private List<String> internationalParameters;

    private java.lang.String contentFrame;

    private java.util.List<com.facishare.qixin.api.model.session.InternationalInfo> contentParameters;

    public InternationalInfo(){
    }

    public InternationalInfo(String internationalKey, String ... parameters){
        this.internationalKey = internationalKey;
        if (parameters != null){
            this.internationalParameters = new ArrayList<>();
            for (String parameter : parameters){
                this.internationalParameters.add(parameter);
            }
        }
    }
}