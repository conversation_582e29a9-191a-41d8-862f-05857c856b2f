package com.facishare.open.msg.model;

import io.protostuff.Tag;

import com.facishare.open.msg.model.base.ProtoBase;
import com.google.common.base.MoreObjects;

public class OpenMessageRevokeEvent extends ProtoBase {

    private static final long serialVersionUID = 5739234983424745538L;
    
    @Tag(1)
    private String enterpriseAccount;
    
    @Tag(2)
    private String sessionId;
    
    @Tag(3)
    private String sessionType;
    
    @Tag(4)
    private String subCategory;
    
    @Tag(5)
    private String appId;
    
    @Tag(6)
    private String parentSessionId;
    
    @Tag(7)
    private String parentSessionType;
    
    @Tag(8)
    private long messageId;
    
    @Tag(9)
	private String upEnterpriseAccount;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getParentSessionId() {
        return parentSessionId;
    }

    public void setParentSessionId(String parentSessionId) {
        this.parentSessionId = parentSessionId;
    }

    public String getParentSessionType() {
        return parentSessionType;
    }

    public void setParentSessionType(String parentSessionType) {
        this.parentSessionType = parentSessionType;
    }

    public long getMessageId() {
        return messageId;
    }

    public void setMessageId(long messageId) {
        this.messageId = messageId;
    }

    public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("enterpriseAccount", enterpriseAccount)
                .add("sessionId", sessionId)
                .add("sessionType", sessionType)
                .add("subCategory", subCategory)
                .add("appId", appId)
                .add("parentSessionId", parentSessionId)
                .add("parentSessionType", parentSessionType)
                .add("messageId", messageId)
                .add("upEnterpriseAccount", upEnterpriseAccount)
                .toString();
    }
    
}
