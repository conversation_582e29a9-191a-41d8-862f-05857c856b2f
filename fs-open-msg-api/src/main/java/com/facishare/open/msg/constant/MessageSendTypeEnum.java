package com.facishare.open.msg.constant;

/**
 * 
 * <AUTHOR>
 * @date 2016年3月2日
 */

public enum MessageSendTypeEnum {

    THIRD_PARTY_PUSH(1, "app2c, the message pushed by third-party app"),
    ADMINISTRATOR_PUSH(2, "app2c, the message pushed by administrator"),
    DEFAULT_AUTO_REPLY(3, "app2c, default auto reply"),
    ADMINISTRATOR_REPLY(4,"app2c, the message relpied by administrator"),
    KEYWORDS_AUTO_REPLY(5, "app2c, keywords auto reply"),
    SYSTEM_PROMPT(6, "app2c, system prompt"),
    CROSS_ADMINISTRATOR_REPLY(7, "app2c, cross administrator reply"),
    CROSS_DEFAULT_AUTO_REPLY(8, "app2c, cross default auto reply"),
    CROSS_KEYWORDS_AUTO_REPLY(9, "app2c, cross default auto reply"),
    WETCHAT_ADMINISTRATOR_REPLY(10,"app2c, wetchat administrator reply"),
    OTHERS(0, "c2c, include <PERSON><PERSON>, <PERSON><PERSON><PERSON>"),
    UPLINK_MESSAGE_NORMAL(-1, "c2app, normal uplink message"),
    UPLINK_MESSAGE_EVENT(-2, "c2app, event uplink message"),
    WECHAT_MESSAGE_NORMAL(-3, "c2App, wechat user uplink msg"),
	CROSS_UPLINK_MESSAGE_NORMAL(-4, "c2App, cross user uplink msg"),;
	

    private int type;
    private String description;

    private MessageSendTypeEnum(int type, String description ) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static MessageSendTypeEnum findByType(int type) {
        for (MessageSendTypeEnum messageSendTypeEnum : MessageSendTypeEnum.values()) {
            if (messageSendTypeEnum.getType() == type) {
                return messageSendTypeEnum;
            }
        }
        throw new IllegalArgumentException("type is invalid");
    }

}
