package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import com.google.common.base.MoreObjects;

/**
 * 
 * <AUTHOR>
 * @date 2017年8月22日
 */

public class UpdateCustomerEvaluateVO implements Serializable {

	private static final long serialVersionUID = 5402910844090888718L;

	/**
     * 评价id
     */
    private String msgLinkId;

    /**
     * 评价得分
     */
    private Integer score;

    /**
     * 评价说明
     */
    private List<String> remarks;
    
    /**
     * 评论提交者Id E.fs.1000格式,微联服务号传微信用户名称
     */
    private String evaluatorId;
    
    /**
     * 评价结果
     */
    private String evaluateResult;

    public String getMsgLinkId() {
		return msgLinkId;
	}

	public void setMsgLinkId(String msgLinkId) {
		this.msgLinkId = msgLinkId;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}
	
	public List<String> getRemarks() {
		return remarks;
	}

	public void setRemarks(List<String> remarks) {
		this.remarks = remarks;
	}

	public String getEvaluatorId() {
		return evaluatorId;
	}

	public void setEvaluatorId(String evaluatorId) {
		this.evaluatorId = evaluatorId;
	}

	public String getEvaluateResult() {
		return evaluateResult;
	}

	public void setEvaluateResult(String evaluateResult) {
		this.evaluateResult = evaluateResult;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("msgLinkId", msgLinkId)
                .add("score", score)
                .add("remarks", remarks)
                .add("evaluatorId", evaluatorId)
                .add("evaluateResult", evaluateResult)
                .toString();
    }
}
