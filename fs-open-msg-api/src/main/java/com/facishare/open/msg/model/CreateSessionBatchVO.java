package com.facishare.open.msg.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 
 * <AUTHOR>
 * @date 2015年8月28日 下午2:49:56
 */
public class CreateSessionBatchVO implements Serializable {

	
	private static final long serialVersionUID = -4070633222187245116L;

	/**
	 * 应用ID
	 */
	private String appId;
	
	/**
	 * 企业帐号
	 */
	private String enterpriseAccount;
	
	/**
	 * 员工列表
	 */
	private List<UserVO> userList;

	public String getAppId() {
	    return appId;
	}

	public void setAppId(String appId) {
	    this.appId = appId;
	}

	public String getEnterpriseAccount() {
	    return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
	    this.enterpriseAccount = enterpriseAccount;
	}

	public List<UserVO> getUserList() {
	    return userList;
	}

	public void setUserList(List<UserVO> userList) {
	    this.userList = userList;
	}
	
	public boolean validateParams() {
		if(StringUtils.isNotBlank(this.getAppId())
		        && StringUtils.isNotBlank(this.getEnterpriseAccount())
				&& this.getUserList() != null ) {
		    return true;
		}
		return false ;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this).append("appId ", appId)
				.append("enterpriseAccount", enterpriseAccount)
				.append("userList", userList).toString();
	}

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((appId == null) ? 0 : appId.hashCode());
        result = prime
                * result
                + ((enterpriseAccount == null) ? 0 : enterpriseAccount
                        .hashCode());
        result = prime * result
                + ((userList == null) ? 0 : userList.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CreateSessionBatchVO other = (CreateSessionBatchVO) obj;
        if (appId == null) {
            if (other.appId != null)
                return false;
        } else if (!appId.equals(other.appId))
            return false;
        if (enterpriseAccount == null) {
            if (other.enterpriseAccount != null)
                return false;
        } else if (!enterpriseAccount.equals(other.enterpriseAccount))
            return false;
        if (userList == null) {
            if (other.userList != null)
                return false;
        } else if (!userList.equals(other.userList))
            return false;
        return true;
    }
}

