package com.facishare.open.msg.model;

import java.util.List;
import java.util.Objects;

import lombok.Data;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.util.StringUtils;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.base.ProtoBase;

/**
 * 异步消息队列参数实体
 * <AUTHOR>
 * @date 2016-06-23
 */

@Data
public class MessagePbVO {

    private static final long serialVersionUID = 2898329741916983981L;

    /**
     * 企业账号 对应企信的 enterpriseAccount
     */
    protected String enterpriseAccount;

    /**
     * 应用ID
     */
    protected String appId;

    /**
     * 消息内容
     */
    protected String content;

    /**
     * 接收消息的人(fsid)列表
     */
    protected List<Integer> toUserList;

    /**
     * 消息类型 支持： text , image , template
     */
    protected MessageTypeEnum type;

    /**
     * 用于防止重复消息发送
     */
    protected String postId;

    /**
     * 仅当管理员回复时,记录管理员身份信息,格式为E.fs.123
     */
    protected String adminUserId;
    
    /**
     * 企信发送需要类型
     */
    private String qxType;
    
    private MessageSendTypeEnum messageSendType;

    /**
     * 国际化
     */
    private InternationalInfo contentInfo;
    private InternationalInfo summaryInfo;
 

    @Override
    public String toString() {
        return "MessagePbVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", content='" + content + '\'' +
                ", toUserList=" + toUserList +
                ", type=" + type +
                ", postId='" + postId + '\'' +
                ", adminUserId='" + adminUserId + '\'' +
                ", qxType='" + qxType + '\'' +
                ", messageSendType=" + messageSendType +
                ", contentInfo=" + contentInfo +
                ", summaryInfo=" + summaryInfo +
                '}';
    }

    /**
     * 数据合法性校验 content的合法性不在该方法校验
     * 
     * @return
     */
    protected boolean validateParam() {
        return !(getToUserList() == null || getToUserList().isEmpty()
                || StringUtils.isEmpty(this.getAppId())
                || StringUtils.isEmpty(this.getPostId())
                || StringUtils.isEmpty(this.getType())
                || StringUtils.isEmpty(this.getEnterpriseAccount()));
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        MessagePbVO other = (MessagePbVO) obj;
        return Objects.equals(appId, other.appId)
                && Objects.equals(enterpriseAccount, other.enterpriseAccount)
                && Objects.equals(content, other.content)
                && Objects.equals(toUserList, other.toUserList)
                && Objects.equals(type, other.type)
                && Objects.equals(postId, other.postId)
                && Objects.equals(adminUserId, other.adminUserId)
                && Objects.equals(qxType, other.qxType)
                && Objects.equals(messageSendType, other.messageSendType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enterpriseAccount,qxType, appId, content, toUserList, type, postId, adminUserId, messageSendType);
    }
    
}
