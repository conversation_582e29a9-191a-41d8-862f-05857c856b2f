package com.facishare.open.msg.result;

import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;

public class SessionInfoResult extends MsgBaseResult{

	private static final long serialVersionUID = -8847586792959705360L;

	private List<Integer> participants;
	
	public List<Integer> getParticipants() {
		return participants;
	}

	public void setParticipants(List<Integer> participants) {
		this.participants = participants;
	}

	public SessionInfoResult () {
		super();
	}

	public SessionInfoResult(MsgCodeEnum errCode) {
		super(errCode);
	}

	public SessionInfoResult(MsgCodeEnum errCode , List<Integer> participants) {
		super(errCode);
		this.participants = participants;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("errorCode", this.errorCode)
            .append("errormsg", this.errorMsg)
            .append("participants", this.participants)
            .toString();
    }

}
