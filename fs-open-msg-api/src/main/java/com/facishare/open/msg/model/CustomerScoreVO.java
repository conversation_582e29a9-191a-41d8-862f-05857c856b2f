package com.facishare.open.msg.model;

import java.io.Serializable;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * @Description: 用于存储客服得分统计
 * <AUTHOR>
 * @date 2017/8/22
 */
public class CustomerScoreVO implements Serializable {
	
	private static final long serialVersionUID = 178420537334696683L;

	/**
	 * 评价总得分
	 */
	private int totalScore;
	
	/**
	 * sender次数
	 */
	private int evalSendNum;
	
	/**
	 * reply 次数
	 */
	private int evalReplyNum;

	public int getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(int totalScore) {
		this.totalScore = totalScore;
	}

	public int getEvalSendNum() {
		return evalSendNum;
	}

	public void setEvalSendNum(int evalSendNum) {
		this.evalSendNum = evalSendNum;
	}

	public int getEvalReplyNum() {
		return evalReplyNum;
	}

	public void setEvalReplyNum(int evalReplyNum) {
		this.evalReplyNum = evalReplyNum;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("totalScore", totalScore)
            .append("evalSendNum", evalSendNum)
            .append("evalReplyNum", evalReplyNum)
            .toString();
    }

}
