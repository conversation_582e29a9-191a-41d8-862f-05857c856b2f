package com.facishare.open.msg.result;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:51:35
 */
public class SessionResult extends MsgBaseResult {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 会话ID  企信定义的
	 */
	private String sessionId;

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public SessionResult () {
		super();
	}
	
	public SessionResult(MsgCodeEnum errCode) {
		super(errCode);
	}
	
	public SessionResult(MsgCodeEnum errCode , String sessionId ) {
		super(errCode);
		this.sessionId = sessionId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("errorCode", this.errorCode)
            .append("errormsg", this.errorMsg)
            .append("sessionId", this.sessionId)
            .toString();
    }
}

