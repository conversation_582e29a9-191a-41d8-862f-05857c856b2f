package com.facishare.open.msg.result;

import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;

import com.facishare.open.msg.model.SessionUserResult;

/**
 * <AUTHOR>
 * @date 2015年8月28日 下午2:51:23
 */
public class BatchSessionResult  extends MsgBaseResult {

	private static final long serialVersionUID = -7822611761243647236L;


	public BatchSessionResult(MsgCodeEnum resultCode) {
		this.errorCode = resultCode.getErrorCode();
		this.errorMsg = resultCode.getErrorMsg();
	}
	
	public BatchSessionResult() {
		super();
	}

	/**
	 * 应用ID
	 */
	private String appId;
	
	/**
	 * 企业号
	 */
	private String enterpriseAccount;
	
	/**
	 * sessionId 列表
	 */
	private List<SessionUserResult> sessionUserList;


	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public List<SessionUserResult> getSessionUserList() {
		return sessionUserList;
	}

	public void setSessionUserList(List<SessionUserResult> sessionUserList) {
		this.sessionUserList = sessionUserList;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this)
		    .append("appId", appId)
		    .append("enterpriseAccount", enterpriseAccount)
		    .append("sessionUserList", sessionUserList)
		    .toString();
	}
}

