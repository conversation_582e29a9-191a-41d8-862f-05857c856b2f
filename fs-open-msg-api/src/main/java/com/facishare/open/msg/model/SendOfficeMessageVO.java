package com.facishare.open.msg.model;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.util.StringUtils;

import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @date 2016年4月28日 下午2:50:57
 */
public class SendOfficeMessageVO extends MessageVO {

    private static final long serialVersionUID = 1L;

    private InternationalInfo contentInfo;
    private InternationalInfo summaryInfo;

    public InternationalInfo getContentInfo() {
        return contentInfo;
    }

    public void setContentInfo(InternationalInfo contentInfo) {
        this.contentInfo = contentInfo;
    }

    public InternationalInfo getSummaryInfo() {
        return summaryInfo;
    }

    public void setSummaryInfo(InternationalInfo summaryInfo) {
        this.summaryInfo = summaryInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("enterpriseAccount", enterpriseAccount)
                .append("postId", postId)
                .append("adminUserId", adminUserId)
                .append("touser", toUserList)
                .append("contentInfo", contentInfo)
                .append("summaryInfo", summaryInfo)
                .toString();
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        SendOfficeMessageVO other = (SendOfficeMessageVO) obj;
        return Objects.equals(appId, other.appId)
                && Objects.equals(enterpriseAccount, other.enterpriseAccount)
                && Objects.equals(content, other.content)
                && Objects.equals(toUserList, other.toUserList)
                && Objects.equals(type, other.type)
                && Objects.equals(postId, other.postId)
                && Objects.equals(adminUserId, other.adminUserId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enterpriseAccount, appId, content, toUserList, type, postId, adminUserId);
    }

    /**
     * 数据合法性校验
     */
    @Override
    public boolean validateParam() {
        
        if (super.validateParam() && !StringUtils.isEmpty(content)) {
            try {
                Gson gson = new Gson();
                gson.fromJson(content, Map.class);
                return true;
            } catch (Exception e) {
                return false;
            }
        } else {
            return false;
        }
        
    }
    
}
