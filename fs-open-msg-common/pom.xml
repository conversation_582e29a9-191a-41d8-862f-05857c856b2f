<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-msg</artifactId>
    <version>2.0.0</version>
  </parent>

  <artifactId>fs-open-msg-common</artifactId>
  <version>2.0.0-SNAPSHOT</version>
  
  <dependencies>
       <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-result</artifactId>
            <version>0.0.5</version>
        </dependency>
      <dependency>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
      </dependency>
      <dependency>
          <groupId>commons-lang</groupId>
          <artifactId>commons-lang</artifactId>
      </dependency>
  </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>