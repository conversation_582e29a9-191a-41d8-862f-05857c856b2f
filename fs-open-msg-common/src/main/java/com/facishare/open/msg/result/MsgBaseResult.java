package com.facishare.open.msg.result;

import java.io.Serializable;

import com.google.common.base.MoreObjects;
import com.facishare.open.common.result.Result;

/**
 * 返回类基类
 * <AUTHOR>
 * @date 2015/7/27
 */

public class MsgBaseResult implements Serializable, Result {
	
	private static final long serialVersionUID = 5530057869359918900L;

	/**
	 * 返回码，默认值为0
	 */
	protected int errorCode = MsgCodeEnum.SUCCESS.getErrorCode();
	
	/**
	 * 返回描叙，默认值为success
	 */
	protected String errorMsg = MsgCodeEnum.SUCCESS.getErrorMsg();
	
	/**
	 * 默认构造
	 */
	public MsgBaseResult() {
		
	}

	/**
	 * 构造方法(返回码, 返回描叙)
	 * @param errCode
	 * @param errMsg
	 */
	public MsgBaseResult(int errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}	
	
	/**
	 * 构造方法(返回码Enum)
	 * @param resultCode
	 */
	public MsgBaseResult(MsgCodeEnum resultCode) {
		this.errorCode = resultCode.getErrorCode();
		this.errorMsg = resultCode.getErrorMsg();
	}
	
	public int getErrorCode() {
		return errorCode;
	}

	public void setErrCode(int errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public void setResultCodeEnum(MsgCodeEnum resultCode) {
		this.errorCode = resultCode.getErrorCode();
		this.errorMsg = resultCode.getErrorMsg();
	}
	
	@Override
	public boolean isSuccess(){
		return this.errorCode == MsgCodeEnum.SUCCESS.getErrorCode();
	}
	
	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
				.add("errorCode", errorCode)
				.add("errorMsg", errorMsg)
				.toString();
	}

    @Override
    public int getErrCode() {
        return errorCode;
    }

    @Override
    public String getErrMessage() {
        return errorMsg;
    }

    @Override
    public String getErrDescription() {
        return errorMsg;
    }
    
    /**
     * 错误码是否属于参数非法
     * @return
     */
    public boolean isIllegalParam(int errorCode) {
        if (errorCode >= 40003 && errorCode <= 40013) {
            return true;
        } else {
           return false;  
        }
    }
    
    /**
     * 服务错误 
     * @param errorCode
     * @return 返回true 错误码属于服务错误  返回false 则不是服务错误
     */
    public boolean isServiceError(int errorCode) {
        if (errorCode >= 40030 || errorCode < 0) {
            return true;
        } else {
            return false;
        }
    }
    
}
