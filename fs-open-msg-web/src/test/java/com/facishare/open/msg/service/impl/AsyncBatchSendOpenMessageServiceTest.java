package com.facishare.open.msg.service.impl;

//import org.apache.rocketmq.common.message.Message;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.MessagePbVO;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AsyncBatchSendOpenMessageServiceTest extends AbstractTest{

    //@Autowired
    //private AsyncBatchSendOpenMessageServiceImpl asyncBatchSendOpenMessageService;


    @SneakyThrows
    @Test
    public void normalTest(){

        while (true){
            MessagePbVO messagePbVO = new MessagePbVO();

            messagePbVO.setAdminUserId("1000");
            messagePbVO.setAppId("FSAID_9897f5");
            messagePbVO.setContent("消息乱测"+System.currentTimeMillis());
            messagePbVO.setEnterpriseAccount("84801");
            messagePbVO.setPostId(System.currentTimeMillis()+""+Math.round(Math.random()*10));
            messagePbVO.setQxType("OpenMessage");
            messagePbVO.setType(MessageTypeEnum.TEXT_QIXIN);
            messagePbVO.setToUserList(Lists.newArrayList(1000));
            messagePbVO.setMessageSendType(MessageSendTypeEnum.ADMINISTRATOR_REPLY);

            //Message message = new Message();
            //message.setFlag(9001);
           // message.setBody(messagePbVO.toProto());

          //  asyncBatchSendOpenMessageService.asyncBatchSendMessage(message);
            Thread.sleep(1000*30);

        }

    }

    @SneakyThrows
    @Test
    public void typeTest(){

        while (true){
            for (MessageTypeEnum value : MessageTypeEnum.values()) {

                MessagePbVO messagePbVO = new MessagePbVO();

                messagePbVO.setAdminUserId("1000");
                messagePbVO.setAppId("FSAID_9897f5");
                messagePbVO.setContent("消息乱测"+value.name()+System.currentTimeMillis());
                messagePbVO.setEnterpriseAccount("84801");
                messagePbVO.setPostId(System.currentTimeMillis()+""+Math.round(Math.random()*10));
                messagePbVO.setQxType("OpenMessage");
                //OT/OpenMessage/T
                messagePbVO.setType(MessageTypeEnum.OPEN_MESSAGE);
                messagePbVO.setToUserList(Lists.newArrayList(1000));
                messagePbVO.setMessageSendType(MessageSendTypeEnum.ADMINISTRATOR_REPLY);

                //Message message = new Message();
                //message.setFlag(9001);
               // message.setBody(messagePbVO.toProto());

               // asyncBatchSendOpenMessageService.asyncBatchSendMessage(message);
                Thread.sleep(1000*20);
            }

        }

    }



    @SneakyThrows
    @Test
    public void eaTest(){

        while (true){
            MessagePbVO messagePbVO = new MessagePbVO();

            messagePbVO.setAdminUserId("1000");
            messagePbVO.setAppId("FSAID_9897f5");
            messagePbVO.setContent("消息乱测"+System.currentTimeMillis());
            messagePbVO.setEnterpriseAccount("84801");
            messagePbVO.setPostId(System.currentTimeMillis()+""+Math.round(Math.random()*10));
            messagePbVO.setQxType("OpenMessage");
            messagePbVO.setType(MessageTypeEnum.TEXT_QIXIN);
            messagePbVO.setToUserList(Lists.newArrayList(1000));
            messagePbVO.setMessageSendType(MessageSendTypeEnum.ADMINISTRATOR_REPLY);

            //Message message = new Message();
            //message.setFlag(9002);
           // message.setBody(messagePbVO.toProto());

          //  asyncBatchSendOpenMessageService.asyncBatchSendMessage(message);
            Thread.sleep(1000*30);

        }

    }


}
