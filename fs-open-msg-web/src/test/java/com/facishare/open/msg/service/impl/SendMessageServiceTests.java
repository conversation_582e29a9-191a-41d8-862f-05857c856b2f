package com.facishare.open.msg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.MessageVO;
import com.facishare.open.msg.model.SendTextMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.SendMessageService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class SendMessageServiceTests extends AbstractTest{


    @Autowired
    private SendMessageService sendMessageService;

    @SneakyThrows
    @Test
    public void batchSendMessageTest(){
        int count=2;
        while (true){
            if (count>0){

                SendTextMessageVO sendTextMessageVO=new SendTextMessageVO();

                sendTextMessageVO.setEnterpriseAccount("84801");
                sendTextMessageVO.setAppId("FSAID_9897f5");
                sendTextMessageVO.setType(MessageTypeEnum.TEXT_QIXIN);
                sendTextMessageVO.setPostId(System.currentTimeMillis()+"");
                sendTextMessageVO.setLowPriority(false);
                sendTextMessageVO.setContent("hardyasync...."+System.currentTimeMillis());
                sendTextMessageVO.setToUserList(Lists.newArrayList(1008, 1011, 1000, 1007));
                MessageResult messageResult =
                  sendMessageService.sendTextMessage(sendTextMessageVO, MessageSendTypeEnum.THIRD_PARTY_PUSH);
                log.info("messageResult:{}", JSONObject.toJSONString(messageResult));
                count--;
                if (count<=0){
                    System.out.println("hhhh");
                    //Thread.sleep();
                }
            }
            Thread.sleep(1000*5);

        }

    }

    @SneakyThrows
    @Test
    public void batchSendMessageImg(){
        int count=30;
        while (true){
            if (count>0){

                SendTextMessageVO sendTextMessageVO=new SendTextMessageVO();

                sendTextMessageVO.setEnterpriseAccount("84801");
                sendTextMessageVO.setAppId("FSAID_9897f5");
                sendTextMessageVO.setType(MessageTypeEnum.TEXT_QIXIN);
                sendTextMessageVO.setPostId(System.currentTimeMillis()+"");
                sendTextMessageVO.setLowPriority(false);
                sendTextMessageVO.setContent("测试测试测试...."+System.currentTimeMillis());
                sendTextMessageVO.setToUserList(Lists.newArrayList(1008, 1011, 1000, 1007));
                MessageResult messageResult =
                  sendMessageService.sendTextMessage(sendTextMessageVO, MessageSendTypeEnum.THIRD_PARTY_PUSH);
                log.info("messageResult:{}", JSONObject.toJSONString(messageResult));
                count--;
                if (count<=0){
                    System.out.println("hhhh");
                    Thread.sleep(1000*60);
                }
            }
            Thread.sleep(1000*5);

        }

    }

    @SneakyThrows
    @Test
    public void sendNormalMessageTest2(){
        while (true){

            SendTextMessageVO sendTextMessageVO=new SendTextMessageVO();

            sendTextMessageVO.setEnterpriseAccount("84801");
            sendTextMessageVO.setAppId("FSAID_9897f5");
            sendTextMessageVO.setType(MessageTypeEnum.TEXT_QIXIN);
            sendTextMessageVO.setPostId(System.currentTimeMillis()+"");
            sendTextMessageVO.setLowPriority(false);
            sendTextMessageVO.setContent("测试测试测试...."+System.currentTimeMillis());
            sendTextMessageVO.setToUserList(Lists.newArrayList(1008, 1011, 1000, 1007));
            MessageResult messageResult =
              sendMessageService.sendTextMessage(sendTextMessageVO, MessageSendTypeEnum.THIRD_PARTY_PUSH);
            log.info("messageResult:{}", JSONObject.toJSONString(messageResult));

            Thread.sleep(1000L*60);
        }

    }


    @SneakyThrows
    @Test
    public void sendNormalMessageTest(){
        while (true){
            SendTextMessageVO sendTextMessageVO=new SendTextMessageVO();
            sendTextMessageVO.setEnterpriseAccount("84801");
            sendTextMessageVO.setAppId("FSAID_bebd4c5");
            sendTextMessageVO.setType(MessageTypeEnum.TEXT);
            sendTextMessageVO.setPostId(System.currentTimeMillis()+""+Math.round(Math.random()*10));
            sendTextMessageVO.setLowPriority(false);
            sendTextMessageVO.setContent("测试测试测试自动回复...."+System.currentTimeMillis());
            sendTextMessageVO.setToUserList(Lists.newArrayList(1000));

            MessageResult messageResult =
              sendMessageService.sendTextMessage(sendTextMessageVO, MessageSendTypeEnum.KEYWORDS_AUTO_REPLY);
            log.info("messageResult:{}", JSONObject.toJSONString(messageResult));

            Thread.sleep(1000L*20);

        }

    }






    @SneakyThrows
    @Test
    public void testSendEaConnText() {
        //82334 ***********   a123456789

        while (true){
            MessageVO msg = new MessageVO();
            String appId = "FSAID_11490c81";
            List<Integer> users = new ArrayList<>();
            users.add(1000);
            msg.setAppId(appId);
            msg.setEnterpriseAccount("81376");
            msg.setToUserList(users);
            msg.setAdminUserId(null);
            msg.setPostId(UUID.randomUUID().toString());
            msg.setType(MessageTypeEnum.TEXT_QIXIN);
            msg.setContent("发送企业互联文本消息"+System.currentTimeMillis());
            MsgBaseResult result = sendMessageService.sendMsgToEaConnService(msg);
            System.out.println("ret:" +result);
            Thread.sleep(1000*10);
        }
    }

    @SneakyThrows
    @Test
    public void testSendEaConnImg() {
        //82334 ***********   a123456789

        SecureRandom secureRandom=SecureRandom.getInstanceStrong();

        while (true){
            // SendAppToCMessageVO 中的消息内容param字段拼装格式如下示例所示（消息内容为Map格式）。

            // 企业互联中的MessageVO中content字段为json格式
            MessageVO msg = new MessageVO();
            msg.setAppId("FSAID_11490c81");
            msg.setEnterpriseAccount("81376");
            msg.setAdminUserId(null);
            msg.setPostId(UUID.randomUUID().toString());
            List<Integer> toUsers = new ArrayList<>();
            toUsers.add(1000);
            msg.setToUserList(toUsers);
            msg.setType(MessageTypeEnum.OPEN_MESSAGE);
            Gson gson = new Gson();
            Map<String, Object> data = new HashMap<>();
            data.put("Type", "IMAGE_TEXT");
            data.put("Title", "测试图文Title");
            data.put("DefaultSummary", "测试图文");
            Map<String, Object> MessageContentMap = new HashMap<>();
            MessageContentMap.put("createTime", "4月24日");
            //messageId 传个uuid即可，在终端保证这条消息是唯一的
            MessageContentMap.put("messageId", System.currentTimeMillis());
            List<Map<String, String>> imageTextListMap = new ArrayList<>();
            Map<String, String> imageTextMap = new HashMap<>();
            imageTextMap.put("summary", "演示拼装图文消息。..");
            imageTextMap.put("contentUrl", "https://crm.ceshi112.com/FSC/EM/File/DownloadByPath?path=A_202209_29_f570b8366f034b839a0fcbb7fd432b4e.png&name=20222621794183.png&traceId=S-E.81376.1000-55379634");
            imageTextMap.put("thirdPartyUrl", "");
            imageTextMap.put("imageUrl", "https://crm.ceshi112.com/FSC/EM/File/DownloadByPath?path=A_202209_29_f570b8366f034b839a0fcbb7fd432b4e.png&name=20222621794183.png&traceId=S-E.81376.1000-55379634");
            imageTextMap.put("title", "测试图文Title2");
            imageTextMap.put("buttonUrl", "http://www.baidu.com");
            imageTextMap.put("buttonText", "图文详情");
            imageTextMap.put("contentType", "1");
            imageTextListMap.add(imageTextMap);
            MessageContentMap.put("imageTextList", imageTextListMap);
            data.put("MessageContent", MessageContentMap);
            String content = gson.toJson(data);
            msg.setContent(content);
            MsgBaseResult result = sendMessageService.sendMsgToEaConnService(msg);
            System.out.println("sendMsgToEaConnService ret: " + result);

            Thread.sleep(1000*20);
        }
    }





}
