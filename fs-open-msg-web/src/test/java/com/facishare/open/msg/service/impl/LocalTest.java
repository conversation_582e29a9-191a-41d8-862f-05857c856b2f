package com.facishare.open.msg.service.impl;

import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.msg.dao.OpenMsgDAO;
import com.facishare.open.msg.model.OpenMsgDO;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchAsyncArg;
import com.facishare.qixin.api.open.OpenMessageBatchService;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by fengyh on 2019/5/15.
 */

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:local-test.xml"})
public class LocalTest  extends AbstractJUnit4SpringContextTests {
    @Resource
    //private OpenMsgDAO openMsgDAO;

    @Test
    public void Test() {
        String ea = "57255";
        List<String> appids = Lists.newArrayList("FSAID_bebd28f","FSAID_bebd269");
        Map<String, Boolean> result = null;//openMsgDAO.queryUnReadUserSessionsByAppIds(ea, appids);
        System.out.println("---------------------------------------------------");
        System.out.println("trace result: "+result);
        System.out.println("---------------------------------------------------");

    }

    @Test
    public void Test2() {
        for(int i=0; i<5; i++) {
            OpenMsgDO openMsgDO = new OpenMsgDO();
            openMsgDO.setEnterpriseAccount("84801");
            openMsgDO.setContent("hardy_unit_test"+i);
            boolean result = false;//openMsgDAO.addOpenMsg(openMsgDO);
            System.out.println("---------------------------------------------------");
            System.out.println("trace result: " + result);
            System.out.println("---------------------------------------------------");
            try {
                Thread.sleep(3 * 1000);
            }catch (Exception e) {}
        }
    }
}
