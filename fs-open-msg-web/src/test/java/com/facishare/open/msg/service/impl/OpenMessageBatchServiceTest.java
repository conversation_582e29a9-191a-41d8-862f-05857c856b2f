package com.facishare.open.msg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchAsyncArg;
import com.facishare.qixin.api.open.OpenMessageBatchService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OpenMessageBatchServiceTest extends AbstractTest{


    @Autowired
    private OpenMessageBatchService openMessageBatchService;

    /**
     * 批量接口测试
     */
    @Test
    public void Test2() throws InterruptedException {
        int count=1;

        while (true){

            OpenSendMessageBatchAsyncArg sendMessageBatchAsyncArg=new OpenSendMessageBatchAsyncArg();
            sendMessageBatchAsyncArg.setMessageContent("testddtestest"+System.currentTimeMillis());
            sendMessageBatchAsyncArg.setAppId("FSAID_9897f5");
            sendMessageBatchAsyncArg.setPostId(System.currentTimeMillis()+"");
            sendMessageBatchAsyncArg.setToUserList(Lists.newArrayList(1026,1027,1034,1036,1040));
            sendMessageBatchAsyncArg.setEnterpriseAccount("81243");
            sendMessageBatchAsyncArg.setMessageType("T");
            sendMessageBatchAsyncArg.setNeedCallBackEvent(true);
            boolean b = openMessageBatchService.sendMessageBatchAsyncV2(sendMessageBatchAsyncArg);
            log.info("message:{},result:{}", JSONObject.toJSONString(sendMessageBatchAsyncArg),String.valueOf(b));

            if (count>4){
                break;
            }
            Thread.sleep(1000*60);

        }

    }

    /**
     * 批量接口测试
     */
    @Test
    public void TestSingle() {
        OpenSendMessageBatchAsyncArg sendMessageBatchAsyncArg=new OpenSendMessageBatchAsyncArg();
        sendMessageBatchAsyncArg.setMessageContent("testtestest");
        sendMessageBatchAsyncArg.setAppId("FSAID_9897f5");
        sendMessageBatchAsyncArg.setPostId("812059719013662723");
        sendMessageBatchAsyncArg.setToUserList(Lists.newArrayList(1026,1027,1034,1036,1040,1052,1053,1054,1055,1056,1058,1059,1061,1062,1063,1064,1000,1065,1066,1067,1068,1005,1069,1070,1071,1072,1073,1011,1075,1077,1018));
        sendMessageBatchAsyncArg.setEnterpriseAccount("81243");
        sendMessageBatchAsyncArg.setMessageType("T");
        boolean b = openMessageBatchService.sendMessageBatchAsyncV2(sendMessageBatchAsyncArg);
        log.info("message:{},result:{}", JSONObject.toJSONString(sendMessageBatchAsyncArg),String.valueOf(b));
    }


    /**
     * 企业互联接口测试
     */
    @Test
    public void Test3() {
        /**
         * 				OpenSendOSS1MessageArg arg = new OpenSendOSS1MessageArg();
         * 				arg.setAppId(msgVo.getAppId());
         * 				arg.setEmployeeId(toUserList.get(0));
         * 				arg.setEnterpriseAccount(msgVo.getEnterpriseAccount());
         * 				arg.setMessageContent(msgVo.getContent());
         * 				arg.setMessageType(msgVo.getType().getType());
         * 				arg.setPostId(msgVo.getPostId());
         * 				arg.setSource(AuthSourceType.system);
         * 				arg.setUpstreamEnterprise(upstreamEa);
         *
         * 				OpenSendOSS1MessageResult openSendOSS1MessageResult = openCrossMessageService.sendOSS1Message(arg);
         */

        OpenSendMessageBatchAsyncArg sendMessageBatchAsyncArg=new OpenSendMessageBatchAsyncArg();
        sendMessageBatchAsyncArg.setAppId("FSAID_1312d0a9");
        sendMessageBatchAsyncArg.setToUserList(Lists.newArrayList(1000));
        sendMessageBatchAsyncArg.setEnterpriseAccount("81243");
        sendMessageBatchAsyncArg.setMessageContent("zzzzzzzzzzzxxddddddxxxxx");
        sendMessageBatchAsyncArg.setMessageType("T");
        sendMessageBatchAsyncArg.setPostId("812059719033662736");
        sendMessageBatchAsyncArg.setSource("qixin");
        sendMessageBatchAsyncArg.setUpstreamEnterprise("81243");
        boolean b = openMessageBatchService.sendMessageBatchAsyncV2(sendMessageBatchAsyncArg);
        System.out.println(b);
    }

    /**
     * 企业互联国际化接口测试
     */
    @Test
    public void Test4() {
        /**
         * 	      OpenSendOSS1MessageMultiArg openSendOSS1MessageMultiArg = new OpenSendOSS1MessageMultiArg();
         * 				openSendOSS1MessageMultiArg.setUpstreamEnterprise(upstreamEa);
         * 				openSendOSS1MessageMultiArg.setAppId(msgVo.getAppId());
         * 				openSendOSS1MessageMultiArg.setSource(AuthSourceType.system);
         * 				openSendOSS1MessageMultiArg.setPostId(msgVo.getPostId());
         * 				openSendOSS1MessageMultiArg.setMessageType(msgVo.getType().getType());
         * 				openSendOSS1MessageMultiArg.setMessageContent(msgVo.getContent());
         */

        OpenSendMessageBatchAsyncArg sendMessageBatchAsyncArg=new OpenSendMessageBatchAsyncArg();
        sendMessageBatchAsyncArg.setUpstreamEnterprise("81243");
        sendMessageBatchAsyncArg.setAppId("FSAID_1312d0a9");
        sendMessageBatchAsyncArg.setSource("qixin");
        sendMessageBatchAsyncArg.setPostId("812059719033662736");
        sendMessageBatchAsyncArg.setMessageType("T");
        sendMessageBatchAsyncArg.setMessageContent("zzzzzzzzzzzxxddddddxxxxx");
        sendMessageBatchAsyncArg.setToUserList(Lists.newArrayList(1000));
        boolean b = openMessageBatchService.sendMessageBatchAsyncV2(sendMessageBatchAsyncArg);
        System.out.println(b);
    }


}
