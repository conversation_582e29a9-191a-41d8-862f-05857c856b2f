<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
        xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/context
	   http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:application name="fs-open-msg-test" />
    <dubbo:registry address="zookeeper://***********:2181?backup=***********:2181,***********:2181" protocol="dubbo"/>


    <!-- 企信服务 -->
    <dubbo:reference id="openSessionService" interface="com.facishare.qixin.api.open.OpenSessionService" protocol="dubbo"/>

    <dubbo:reference id="openMessageService" interface="com.facishare.qixin.api.open.OpenMessageService" protocol="dubbo"/>

    <dubbo:reference id="avMessageService" interface="com.facishare.qixin.api.open.AvMessageService" protocol="dubbo" />

    <dubbo:reference id="openMessageC2CService" interface="com.facishare.qixin.api.open.OpenMessageC2CService" protocol="dubbo" />

<!--    <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" protocol="dubbo" version="1.0"/>-->

    <dubbo:reference id="msgSessionService" interface="com.facishare.open.msg.service.MsgSessionService" protocol="dubbo" version="1.0"/>

    <dubbo:reference id="openCustomService" interface="com.facishare.qixin.api.open.OpenCustomService" protocol="dubbo" />
    
    <dubbo:reference id="openCrossCustomerService" interface="com.facishare.qixin.api.open.OpenCrossCustomerService" protocol="dubbo" timeout="5000" check="false" />
    
    <dubbo:reference id="openCrossMessageService" interface="com.facishare.qixin.api.open.OpenCrossMessageService" protocol="dubbo" timeout="20000"/>
    
    <dubbo:reference id="customerEvaluateService" interface="com.facishare.open.msg.service.CustomerEvaluateService" protocol="dubbo" timeout="20000" version="1.0"/>
     
    <dubbo:reference id="departmentProviderService"
                     interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo"
                     version="5.7"/>
</beans>