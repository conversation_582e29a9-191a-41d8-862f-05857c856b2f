<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.base}/logs/bizlog/warn.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%class:%line]- %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/bizlog/warn.%d{yyyy-MM-dd}.log.zip</FileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>


    <appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.base}/logs/bizlog/all.log</File>
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%class:%line]- %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/bizlog/all.%d{yyyy-MM-dd}.log.zip</FileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%class:%line]- %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.github" level="OFF" additivity="false">
        <appender-ref ref="ALL"/>
    </logger>

    <logger name="com.facishare.open" level="${open.logback.level}" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="WARN"/>
        <appender-ref ref="ALL"/>
    </logger>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="WARN"/>
        <appender-ref ref="ALL"/>
    </root>
</configuration>