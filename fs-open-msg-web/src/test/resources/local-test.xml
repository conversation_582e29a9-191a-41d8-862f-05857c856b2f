<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/context
	   http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.facishare.open.msg.service.impl"/>

    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:application name="fs-oauth-base-provider" />

    <dubbo:registry address="zookeeper://***********:2181?backup=***********:2181,***********:2181" protocol="dubbo"/>

    <dubbo:registry address="zookeeper://***********:2181?backup=***********:2181,***********:2181"  />

    <!-- basic start -->
<!--    <dubbo:reference  interface="com.facishare.open.msg.service.SendMessageService" id="sendMessageService"
                      version="1.0"
                      timeout="5000"
                      check="false" url="dubbo://localhost:29201"/>-->

    <dubbo:reference  interface="com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService" id="enterpriseRelationService"
                      version="1.0"
                      timeout="5000"
                      check="false" url="dubbo://localhost:29201"/>


    <bean id="openMsgDAO" class="com.facishare.open.msg.dao.impl.OpenMsgDAOImpl"/>

    <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" protocol="dubbo" version="1.0"/>

</beans>