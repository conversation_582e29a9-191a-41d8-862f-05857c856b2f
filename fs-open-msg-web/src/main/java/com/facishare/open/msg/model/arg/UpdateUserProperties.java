package com.facishare.open.msg.model.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: max
 * @date: 2019-04-25 17:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateUserProperties implements Serializable {

    private static final long serialVersionUID = 3827281977335028181L;

    @ApiModelProperty(required = true)
    private String appId;

    @ApiModelProperty(required = true)
    private String enterpriseAccount;

    @ApiModelProperty(required = true)
    private Integer userId;

    @ApiModelProperty(required = true, notes = "需要更新的属性key")
    private Integer key;

    @ApiModelProperty(required = true, notes = "需要更新的属性value 可以使字符串形式的json")
    private String value;
}
