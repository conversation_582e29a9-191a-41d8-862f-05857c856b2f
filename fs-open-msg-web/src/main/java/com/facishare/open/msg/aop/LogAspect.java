package com.facishare.open.msg.aop;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date
 */
public class LogAspect {
    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    private static final int PRINT_LENGTH = 1024;

    public Object around(ProceedingJoinPoint point) throws Throwable {
        String className = point.getTarget().getClass().getSimpleName();
        String methodName = point.getSignature().getName();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        Object result = null;
        try {
            result = point.proceed();
        } catch (Exception e) {
            logger.error("className:{}, methodName:{}, args:{}, exception, ", className, methodName, point.getArgs(), e);
        }
        stopWatch.stop();
        String printResult = Objects.isNull(result) ? "" : result.toString();
        if (printResult.length() > PRINT_LENGTH) {
            printResult = StringUtils.substring(printResult, 0, PRINT_LENGTH) + "...more...";
        }
        logger.debug("className:{}, methodName:{}, args:{}, result:{}, timeCost:{}",
                className, methodName, point.getArgs(), printResult, stopWatch.getTotalTimeMillis());

        return result;
    }
}
