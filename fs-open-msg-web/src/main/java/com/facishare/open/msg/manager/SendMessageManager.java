package com.facishare.open.msg.manager;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.SendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 18:38
 **/
@Slf4j
@Component
public class SendMessageManager {

    @Autowired
    private SendMessageService sendMessageService;

    @Deprecated
    public MessageResult sendTextMessageWithOutSendType(SendTextMessageVO arg) {
        MessageResult result = sendMessageService.sendTextMessage(arg);
        log.info("sendTextMessage success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MessageResult sendTextMessage(SendTextMessageVO textMessageVO, MessageSendTypeEnum messageSendType) {
        MessageResult result = sendMessageService.sendTextMessage(textMessageVO, messageSendType);
        log.info("sendTextMessage success. textMessageVO:{}, messageSendType:{}, result:{}", textMessageVO,
                messageSendType, result);
        return result;
    }

    public MessageResult sendOfficeMessage(SendOfficeMessageVO officeMessageVO, MessageSendTypeEnum messageSendType) {
        MessageResult result = sendMessageService.sendOfficeMessage(officeMessageVO, messageSendType);
        log.info("sendOfficeMessage success. officeMessageVO:{}, messageSendType:{}, result:{}", officeMessageVO,
                messageSendType, result);
        return result;
    }

    public MsgBaseResult sendMsgToEaConnService(MessageVO msgVo) {
        MsgBaseResult result = sendMessageService.sendMsgToEaConnService(msgVo);
        log.info("sendMsgToEaConnService success. msgVo:{}, result:{}", msgVo, result);
        return result;
    }

    @Deprecated
    public MsgBaseResult sendMsgToEaConnService(String upstreamEa, MessageVO msgVo) {
        MsgBaseResult result = sendMessageService.sendMsgToEaConnService(upstreamEa, msgVo);
        log.info("sendMsgToEaConnService success. msgVo:{}, upstreamEa:{}, result:{}", msgVo, upstreamEa, result);
        return result;
    }

    public MsgBaseResult sendMsgToEaConnService(SendMsgToEaConnServiceVO arg) {
        MsgBaseResult result = sendMessageService.sendMsgToEaConnService(arg);
        log.info("sendMsgToEaConnService success. arg:{}, result:{}", arg, result);
        return result;
    }

    @Deprecated
    public MsgBaseResult sendTemplateMessage(SendTemplateMessageVO arg) {
        MsgBaseResult result = sendMessageService.sendTemplateMessage(arg);
        log.info("sendTemplateMessage success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MessageResult sendWechatMessage(WechatMessageVO arg) {
        MessageResult result = sendMessageService.sendWechatMessage(arg);
        log.info("sendWechatMessage success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MessageResult sendWechatPromptTextMessage(SendWechatPromptTextMessageArg arg) {
        MessageResult result = sendMessageService.sendWechatPromptTextMessage(arg);
        log.info("sendWechatPromptTextMessage success. arg:{}, result:{}", arg, result);
        return result;
    }
}
