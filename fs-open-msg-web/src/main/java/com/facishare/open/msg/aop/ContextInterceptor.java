package com.facishare.open.msg.aop;


import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 拦截器
 * @author: max
 * @date: 2019-04-11 19:59
 **/
@Slf4j
public class ContextInterceptor extends HandlerInterceptorAdapter {

    private  void initTraceID(String newTraceId) {
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = newTraceId;
            context.setTraceId(traceId);
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // TODO: 2019/4/12 安全性校验
        String traceId = request.getHeader("X-fs-Trace-Id");
        initTraceID(traceId);
        log.info("preHandle request:{} , header names:{} ", request, request.getHeaderNames());
        return true;
    }
}