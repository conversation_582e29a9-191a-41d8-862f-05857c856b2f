package com.facishare.open.msg.controller;

import com.facishare.open.msg.manager.MsgSessionManager;
import com.facishare.open.msg.model.CustomMenuVO;
import com.facishare.open.msg.model.EaConnAppSessionConfig;
import com.facishare.open.msg.model.EaConnRemindSessionConfigVO;
import com.facishare.open.msg.model.PublicAppServiceConfig;
import com.facishare.open.msg.model.arg.FindEaConnSession;
import com.facishare.open.msg.model.arg.UpdateSessionInfo;
import com.facishare.open.msg.model.arg.UpdateUserProperties;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.SessionResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 15:59
 **/
// IgnoreI18nFile
@Api(tags = {"session服务接口[MsgSessionService]"})
@Slf4j
@Controller
@RequestMapping("fs-open-msg/session")
public class MsgSessionController {

    @Autowired
    private MsgSessionManager msgSessionManager;

    @ApiOperation(value = "查找企业互联下互联服务号sesssion")
    @RequestMapping(value = "/findEaConnSession", method = RequestMethod.POST)
    @ResponseBody
    public SessionResult findEaConnSession(@RequestBody FindEaConnSession findEaConnSession) {
        return msgSessionManager.findEaConnSession(findEaConnSession);
    }

    @ApiOperation(value = "更新企业互联下的应用session配置信息, 该配置信息用来控制创建session时获取基本信息",
            notes = "session的消息由上游推给下游。发消息时，企信后台检查如果session还没有创建，会取出配置信息来创建session.")
    @RequestMapping(value = "/updateEaConnAppSessionConfig", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult updateEaConnAppSessionConfig(@RequestBody EaConnAppSessionConfig eaConnAppSessionConfig) {
        return msgSessionManager.updateEaConnAppSessionConfig(eaConnAppSessionConfig);
    }

    @ApiOperation(value = "修改服务号session的消息", notes = "头像,sessionName")
    @RequestMapping(value = "/updatePublicAppServiceConfig", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult updatePublicAppServiceConfig(@RequestBody PublicAppServiceConfig publicAppServiceConfig) {
        return msgSessionManager.updatePublicAppServiceConfig(publicAppServiceConfig);
    }

    @ApiOperation(value = "修改自定义菜单", notes = "服务端不会主动通知客户端来拉取新菜单")
    @RequestMapping(value = "/updateCustomMenu", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult updateCustomMenu(@RequestBody CustomMenuVO customMenu) {
        return msgSessionManager.updateCustomMenu(customMenu);
    }

    @ApiOperation(value = "配置企业互联下的审批提醒session的title和头像", notes = "更新摘要会引起未读数变化")
    @RequestMapping(value = "/updateEaConnRemindSessionConfig", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult updateEaConnRemindSessionConfig(@RequestBody EaConnRemindSessionConfigVO eaConnRemindSessionConfigVO) {
        return msgSessionManager.updateEaConnRemindSessionConfig(eaConnRemindSessionConfigVO);
    }

    @ApiOperation(value = "更新session的描述,是否隐藏输入框等")
    @RequestMapping(value = "/updateSessionInfo", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult updateSessionInfo(@RequestBody UpdateSessionInfo updateSessionInfo) {
        return msgSessionManager.updateSessionInfo(updateSessionInfo);
    }

    @ApiOperation(value = "往客户端推送数据（提醒客户端更新app列表）")
    @RequestMapping(value = "/updateUserProperties", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult updateUserProperties(@RequestBody UpdateUserProperties updateUserProperties) {
        return msgSessionManager.updateUserProperties(updateUserProperties);
    }
}