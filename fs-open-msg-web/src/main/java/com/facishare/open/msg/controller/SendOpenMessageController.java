package com.facishare.open.msg.controller;

import com.facishare.open.msg.manager.SendOpenMessageManager;
import com.facishare.open.msg.model.SendAppToCMessageVO;
import com.facishare.open.msg.model.SendCToCMessageVO;
import com.facishare.open.msg.model.arg.SendAppToCMessageWithSendType;
import com.facishare.open.msg.result.MessageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @description:
 * @author: max
 * @date: 2019-04-25 18:03
 **/
@Api(tags = {"发送对人消息[sendOpenMessageService]"})
@Slf4j
@Controller
@RequestMapping("fs-open-msg/send/open")
public class SendOpenMessageController {

    @Autowired
    private SendOpenMessageManager sendOpenMessageManager;

    @ApiOperation(value = "发送APP对人消息")
    @RequestMapping(value = "/sendCtoCMessage", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendCtoCMessage(@RequestBody SendCToCMessageVO sendAppToCMessageVO) {
        return sendOpenMessageManager.sendCtoCMessage(sendAppToCMessageVO);
    }

    @ApiOperation(value = "发送APP对人消息")
    @RequestMapping(value = "/sendAppToCMessage", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendAppToCMessage(@RequestBody SendAppToCMessageVO sendAppToCMessageVO) {
        return sendOpenMessageManager.sendAppToCMessage(sendAppToCMessageVO);
    }

    @ApiOperation(value = "发送APP对人消息")
    @RequestMapping(value = "/sendAppToCMessageWithSendType", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendAppToCMessageWithSendType(@RequestBody SendAppToCMessageWithSendType sendAppToCMessageWithSendType) {
        return sendOpenMessageManager.sendAppToCMessageWithSendType(sendAppToCMessageWithSendType);
    }
}
