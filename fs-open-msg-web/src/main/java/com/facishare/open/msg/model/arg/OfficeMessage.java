package com.facishare.open.msg.model.arg;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.SendOfficeMessageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 19:34
 **/
@Data
public class OfficeMessage {
    @ApiModelProperty(required = true)
    private SendOfficeMessageVO officeMessageVO;
    @ApiModelProperty(required = true)
    private MessageSendTypeEnum messageSendType;
}
