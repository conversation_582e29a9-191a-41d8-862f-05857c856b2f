package com.facishare.open.msg.model.arg;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.SendTextMessageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 19:32
 **/
@Data
public class TextMessage {
    @ApiModelProperty(required = true)
    private SendTextMessageVO textMessageVO;
    @ApiModelProperty(required = true)
    private MessageSendTypeEnum messageSendType;
}
