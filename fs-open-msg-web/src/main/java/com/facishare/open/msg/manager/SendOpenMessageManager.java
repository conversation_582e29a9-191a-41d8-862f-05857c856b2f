package com.facishare.open.msg.manager;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.SendAppToCMessageVO;
import com.facishare.open.msg.model.SendCToCMessageVO;
import com.facishare.open.msg.model.arg.SendAppToCMessageWithSendType;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.SendOpenMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: max
 * @date: 2019-04-25 17:59
 **/
@Component
@Slf4j
public class SendOpenMessageManager {

    @Autowired
    private SendOpenMessageService sendOpenMessageService;

    public MessageResult sendCtoCMessage(SendCToCMessageVO arg) {
        MessageResult result = sendOpenMessageService.sendCtoCMessage(arg);
        log.info("sendCtoCMessage success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MessageResult sendAppToCMessage(SendAppToCMessageVO arg) {
        MessageResult result = sendOpenMessageService.sendAppToCMessage(arg);
        log.info("sendAppToCMessage success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MessageResult sendAppToCMessageWithSendType(SendAppToCMessageWithSendType sendAppToCMessageWithSendType) {
        SendAppToCMessageVO param = sendAppToCMessageWithSendType.getSendAppToCMessageVO();
        MessageSendTypeEnum messageSendType = sendAppToCMessageWithSendType.getMessageSendType();

        MessageResult result = sendOpenMessageService.sendAppToCMessage(param, messageSendType);
        log.info("sendAppToCMessageWithSendType success. param:{}, messageSendType:{}, result:{}", param, messageSendType, result);
        return result;
    }
}
