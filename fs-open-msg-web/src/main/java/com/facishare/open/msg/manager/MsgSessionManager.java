package com.facishare.open.msg.manager;

import com.facishare.open.msg.model.CustomMenuVO;
import com.facishare.open.msg.model.EaConnAppSessionConfig;
import com.facishare.open.msg.model.EaConnRemindSessionConfigVO;
import com.facishare.open.msg.model.PublicAppServiceConfig;
import com.facishare.open.msg.model.arg.FindEaConnSession;
import com.facishare.open.msg.model.arg.UpdateSessionInfo;
import com.facishare.open.msg.model.arg.UpdateUserProperties;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.SessionResult;
import com.facishare.open.msg.service.MsgSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 16:03
 **/
@Component
@Slf4j
public class MsgSessionManager {

    @Autowired
    private MsgSessionService msgSessionService;

    public SessionResult findEaConnSession(FindEaConnSession arg) {
        String appID = arg.getAppID(), upEa = arg.getUpEa(), downEa = arg.getDownEa();
        Integer userId = arg.getUserId();
        SessionResult result = msgSessionService.findEaConnSession(appID, upEa, downEa, userId);
        log.info("MsgSessionManager success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MsgBaseResult updateEaConnAppSessionConfig(EaConnAppSessionConfig eaConnAppSessionConfig) {
        MsgBaseResult result = msgSessionService.updateEaConnAppSessionConfig(eaConnAppSessionConfig);
        log.info("updateEaConnAppSessionConfig success. arg:{}, result:{}", eaConnAppSessionConfig, result);
        return result;
    }

    public MessageResult updatePublicAppServiceConfig(PublicAppServiceConfig publicAppServiceConfig) {
        MessageResult result = msgSessionService.updatePublicAppServiceConfig(publicAppServiceConfig);
        log.info("updatePublicAppServiceConfig success. arg:{}, result:{}", publicAppServiceConfig, result);
        return result;
    }

    public MessageResult updateCustomMenu(CustomMenuVO arg) {
        MessageResult result = msgSessionService.updateCustomMenu(arg);
        log.info("updateCustomMenu success. arg:{}, result:{}", arg, result);
        return result;
    }

    public MsgBaseResult updateEaConnRemindSessionConfig(EaConnRemindSessionConfigVO eaConnRemindSessionConfigVO) {
        MsgBaseResult result = msgSessionService.updateEaConnRemindSessionConfig(eaConnRemindSessionConfigVO);
        log.info("updateEaConnRemindSessionConfig success. eaConnRemindSessionConfigVO:{}, result:{}", eaConnRemindSessionConfigVO, result);
        return result;
    }

    public MessageResult updateSessionInfo(UpdateSessionInfo updateSessionInfo) {
        String appId = updateSessionInfo.getAppId();
        String description = updateSessionInfo.getDescription();
        Boolean hideInputPanel = updateSessionInfo.getHideInputPanel();
        Boolean showSwitch = updateSessionInfo.getShowSwitch();
        Set<String> sendMessgeTypes = updateSessionInfo.getSendMessgeTypes();
        MessageResult result = msgSessionService.updateSessionInfo(appId, description, hideInputPanel, showSwitch, sendMessgeTypes);
        log.info("updateSessionInfo success. arg:{}, result:{}", updateSessionInfo, result);
        return result;
    }

    public MessageResult updateUserProperties(UpdateUserProperties updateUserProperties) {
        String appId = updateUserProperties.getAppId();
        String enterpriseAccount = updateUserProperties.getEnterpriseAccount();
        Integer userId = updateUserProperties.getUserId();
        Integer key = updateUserProperties.getKey();
        String value = updateUserProperties.getValue();
        MessageResult result = msgSessionService.updateUserProperties(appId, enterpriseAccount, userId, key, value);
        log.info("updateUserProperties success. arg:{}, result:{}", updateUserProperties, result);
        return result;
    }
}
