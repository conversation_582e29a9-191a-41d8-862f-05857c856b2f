package com.facishare.open.msg.model.arg;

import com.facishare.open.msg.model.MessageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 19:59
 **/
@Data
public class MsgToEaConn {

    @ApiModelProperty(required = true)
    private String upstreamEa;
    @ApiModelProperty(required = true)
    private MessageVO messageVO;
}
