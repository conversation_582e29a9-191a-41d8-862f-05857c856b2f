package com.facishare.open.msg.model.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 16:58
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindEaConnSession implements Serializable {

    private static final long serialVersionUID = -6018711649807971909L;

    @NonNull
    private String appID;
    @NonNull
    private String upEa;
    @NonNull
    private String downEa;
    @NonNull
    private Integer userId;
}
