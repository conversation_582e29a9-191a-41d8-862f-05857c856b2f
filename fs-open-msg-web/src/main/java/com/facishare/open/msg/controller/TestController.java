package com.facishare.open.msg.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @description: test
 * @author: max
 * @date: 2019/04/11
 **/
@Slf4j
@Controller
@RequestMapping
public class TestController {

    @RequestMapping(value = "/ping")
    @ResponseBody
    public String test(@RequestParam Map<String, String> param, HttpServletRequest request) {
        log.info("open-msg-web param test, arg:[{}], method:[{}]", param, request.getMethod());
        return "ok";
    }

    @RequestMapping(value = "/param/test", method = RequestMethod.POST)
    @ResponseBody
    public String test1(@RequestBody Map<Object, Object> param) {
        log.info("open-msg-web param, hangupMsg:[{}]", param);
        return "200";
    }

    @RequestMapping(value = "/hello")
    @ResponseBody
    public String hello() {
        log.info("open-msg-web get hello cmd");
        return "hello, world";
    }
}