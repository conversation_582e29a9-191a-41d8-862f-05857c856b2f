package com.facishare.open.msg.utils;

import com.facishare.open.msg.controller.TestController;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
// IgnoreI18nFile
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @ReloadableProperty("swagger.show")
    private boolean swaggerShow = false;

    @Bean
    public Docket createRestApi() {

        Predicate<RequestHandler> predicate = input -> {
            Class<?> declaringClass = input.declaringClass();
            if (declaringClass == TestController.class) {
                return false;
            }
            // 被注解的类
            if (declaringClass.isAnnotationPresent(Controller.class)) {
                return true;
            }
            // 被注解的方法
            return input.isAnnotatedWith(ResponseBody.class);
        };

        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerShow)
                .apiInfo(apiInfo())
                .useDefaultResponseMessages(false)
                .select()
                .apis(predicate::test)
                //过滤的接口
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        // 大标题
        return new ApiInfoBuilder().title("消息系统接口服务")
                .description("需要提供更多接口请联系 马旭。" +
                        "113环境测试地址：http://**************:42230/xxx(http://open.ceshi112.com/restmessage/xxx)。" +
                        "112环境测试地址：http://**************:44659/xxx(http://open.ceshi112.com/restmessage/xxx)。" +
                        "生产环境地址：http://open.fxiaoke.com/restmessage/xxx。" +
                        "专属云地址：http://api.fxiaokeglobal.local/restmessage/xxx")
                .contact(new Contact("max", "", "<EMAIL>"))
                .version("1.0")
                .build();
    }
}