package com.facishare.open.msg.controller;

import com.facishare.open.msg.manager.SendMessageManager;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.model.arg.MsgToEaConn;
import com.facishare.open.msg.model.arg.OfficeMessage;
import com.facishare.open.msg.model.arg.TextMessage;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @description:
 * @author: max
 * @date: 2019-04-11 18:38
 **/
// IgnoreI18nFile
@Api(tags = {"发送消息服务接口[SendMessageService]"})
@Slf4j
@Controller
@RequestMapping("fs-open-msg/send")
public class SendMessageController {

    @Autowired
    private SendMessageManager sendMessageManager;

    @ApiOperation(value = "[Deprecated]发送文本消息", notes = "请使用sendTextMessage(TextMessage textMessage)")
    @RequestMapping(value = "/sendTextMessageWithOutSendType", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendTexMessageWithOutSendType(@RequestBody SendTextMessageVO sendTextMessageVO) {
        return sendMessageManager.sendTextMessageWithOutSendType(sendTextMessageVO);
    }

    @ApiOperation(value = "发送文本消息", notes = "增加下行消息发送类型")
    @RequestMapping(value = "/sendTextMessage", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendTextMessage(@RequestBody TextMessage textMessage) {
        return sendMessageManager.sendTextMessage(textMessage.getTextMessageVO(), textMessage.getMessageSendType());
    }

    @ApiOperation(value = "发送oa消息")
    @RequestMapping(value = "/sendOfficeMessage", method = RequestMethod.POST)
    @ResponseBody
    public MessageResult sendOfficeMessage(@RequestBody OfficeMessage officeMessage) {
        return sendMessageManager.sendOfficeMessage(officeMessage.getOfficeMessageVO(), officeMessage.getMessageSendType());
    }

    @ApiOperation(value = "推消息到app端企业互联session下的 二级上游session",
            notes = " 此二级session下，app名后面 不 带上游企业名的session 推送消息。比如企业互联助手等")
    @RequestMapping(value = "/sendMsgToEaConnWithoutUpEa", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendMsgToEaConnWithoutUpEa(@RequestBody MessageVO messageVO) {
        return sendMessageManager.sendMsgToEaConnService(messageVO);
    }

    @ApiOperation(value = "[新]推消息到app端企业互联下的下游二级session新接口,支持批量下游",
            notes = "往下游的 企业互联session下的二级session推送消息。此二级session中，app名后面带了游企业名的session。")
    @RequestMapping(value = "/sendMsgToEaConnServiceNew", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendMsgToEaConnServiceNew(@RequestBody SendMsgToEaConnServiceVO msgToEaConnArg) {
        return sendMessageManager.sendMsgToEaConnService(msgToEaConnArg);
    }

    @ApiOperation(value = "[Deprecated]推消息到app端企业互联下的下游二级session",
            notes = "该接口已经废弃,请使用 sendMsgToEaConnService(SendMsgToEaConnServiceVO arg)")
    @RequestMapping(value = "/sendMsgToEaConnService", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendMsgToEaConnService(@RequestBody MsgToEaConn msgToEaConnArg) {
        return sendMessageManager.sendMsgToEaConnService(msgToEaConnArg.getUpstreamEa(), msgToEaConnArg.getMessageVO());
    }

    @ApiOperation(value = "发送模板消息")
    @RequestMapping(value = "/sendTemplateMessage", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendTemplateMessage(@RequestBody SendTemplateMessageVO sendTemplateMessageVO) {
        return sendMessageManager.sendTemplateMessage(sendTemplateMessageVO);
    }

    @ApiOperation(value = "发送微客服消息,从微信来的企业外部用户消息上行到企信的工作台。")
    @RequestMapping(value = "/sendWechatMessage", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendWechatMessage(@RequestBody WechatMessageVO wechatMessageVO) {
        return sendMessageManager.sendWechatMessage(wechatMessageVO);
    }

    @ApiOperation(value = "发送系统提示消息到微客服工作台二级session")
    @RequestMapping(value = "/sendWechatPromptTextMessage", method = RequestMethod.POST)
    @ResponseBody
    public MsgBaseResult sendWechatPromptTextMessage(@RequestBody SendWechatPromptTextMessageArg sendWechatPromptTextMessageArg) {
        return sendMessageManager.sendWechatPromptTextMessage(sendWechatPromptTextMessageArg);
    }
}
