package com.facishare.open.msg.model.arg;

import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.model.SendAppToCMessageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: max
 * @date: 2019-04-25 18:06
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendAppToCMessageWithSendType implements Serializable {

    private static final long serialVersionUID = -3559983533657201412L;

    private SendAppToCMessageVO sendAppToCMessageVO;
    private MessageSendTypeEnum messageSendType;
}