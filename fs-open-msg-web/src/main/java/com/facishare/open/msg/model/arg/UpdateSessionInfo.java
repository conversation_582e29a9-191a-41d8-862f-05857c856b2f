package com.facishare.open.msg.model.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * @description:
 * @author: max
 * @date: 2019-04-25 17:37
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSessionInfo implements Serializable {
    private static final long serialVersionUID = 8209865283729792606L;

    private String appId;
    private String description;
    private Boolean hideInputPanel;
    private Boolean showSwitch;
    private Set<String> sendMessgeTypes;
}