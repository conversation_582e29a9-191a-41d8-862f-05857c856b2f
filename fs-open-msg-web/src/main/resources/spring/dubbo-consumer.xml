<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       ">

    <dubbo:reference interface="com.facishare.open.msg.service.MsgSessionService" id="msgSessionService"
                   protocol="dubbo" version="1.0"/>

    <dubbo:reference interface="com.facishare.open.msg.service.SendMessageService" id="sendMessageService"
                   protocol="dubbo" version="1.0" retries="2"/>

    <dubbo:reference interface="com.facishare.open.msg.service.SendOpenMessageService"
                   id="sendOpenMessageService" protocol="dubbo" version="2.0"/>
</beans>