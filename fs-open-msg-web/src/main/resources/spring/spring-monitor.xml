<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!--监控及日志中心-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--蜂眼监控-->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround" expression="
            execution(* com.facishare.open.msg.controller..*.*(..)) or
            execution(* com.facishare.open.msg.manager..*.*(..))"/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>
</beans>
