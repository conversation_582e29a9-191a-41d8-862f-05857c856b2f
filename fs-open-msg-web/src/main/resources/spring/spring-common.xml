<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <import resource="classpath:spring/spring-cms.xml"/>
    <import resource="classpath:spring/spring-dubbo-msg-config.xml"/>
    <import resource="classpath:spring/dubbo-consumer.xml"/>
    <import resource="classpath:spring/spring-monitor.xml"/>

    <!--Log Aop-->
    <bean id="logAspect" class="com.facishare.open.msg.aop.LogAspect"/>
    <aop:config>
        <aop:aspect id="logMonitor" ref="logAspect" order="0">
            <aop:pointcut id="monitor"
                          expression="execution(* com.facishare.open.msg..*.*(..))"/>
            <aop:around method="around" pointcut-ref="monitor"/>
        </aop:aspect>
    </aop:config>

    <bean id="datastore" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="open-msg-mongo"/>
    <bean id="jedisSupport" class="com.github.jedis.support.JedisFactoryBean" p:configName="redis-open-msg"/>
    <bean id="redisFactory" class="com.facishare.open.common.storage.redis.RedisTemplate">
        <property name="jedisCmd" ref="jedisSupport"/>
    </bean>
</beans>