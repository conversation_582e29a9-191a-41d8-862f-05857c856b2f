<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
    <!--annotation configuration -->

    <context:annotation-config/>
    <aop:aspectj-autoproxy/>
    <context:component-scan base-package="com.facishare.open.msg"/>


    <import resource="classpath:spring/spring-common.xml"/>
    <import resource="classpath:springprovider/applicationContext.xml"/>
    <import resource="classpath:springreply/applicationContext.xml"/>
    <import resource="classpath:springexport/applicationContext.xml"/>

    <!--<mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.facishare.open.msg.aop.ContextInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>-->

    <mvc:default-servlet-handler/>
    <bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping">
        <property name="order" value="2"/>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="10485760000"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>

    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter"/>

    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/WEB-INF/jsp/"/>
        <property name="suffix" value=".jsp"/>
    </bean>
    <bean id="utf8charset" class="java.nio.charset.Charset" factory-method="forName">
        <constructor-arg value="UTF-8"/>
    </bean>
<!--    <mvc:annotation-driven>-->
<!--        <mvc:message-converters>-->
<!--            <bean class="org.springframework.http.converter.StringHttpMessageConverter">-->
<!--                <constructor-arg ref="utf8charset"/>-->
<!--                <property name="writeAcceptCharset" value="false"/>-->
<!--            </bean>-->
<!--            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">-->
<!--                <property name="objectMapper">-->
<!--                    <bean class="com.fasterxml.jackson.databind.ObjectMapper">-->
<!--                        <property name="serializationInclusion">-->
<!--                            <value type="com.fasterxml.jackson.annotation.JsonInclude.Include">NON_NULL</value>-->
<!--                        </property>-->
<!--                    </bean>-->
<!--                </property>-->
<!--            </bean>-->
<!--        </mvc:message-converters>-->
<!--    </mvc:annotation-driven>-->
    <mvc:annotation-driven/>
</beans>