<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/aop
			http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
			http://www.springframework.org/schema/tx
			http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
			http://www.springframework.org/schema/mvc
			http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.autoreplymsg"/>

    <bean id="msg_dataSource1" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="db-open-msg"/>
    </bean>


    <bean id="msg_dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
    <property name="driverClassName" value="com.mysql.jdbc.Driver" />
    <property name="url" value="*****************************************" />
    <property name="username" value="fte2" />
    <property name="password" value="pp00--[[" />
    </bean>

    <bean id="msg_sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="msg_dataSource" />
        <property name="typeAliasesPackage" value="com.facishare.open.storage.model" />
        <property name="mapperLocations" value="classpath:mapper/*.xml" />
        <property name="configLocation" value="classpath:spring/spring-mybatis-test.xml" />
    </bean>

    <bean id="msg_jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource">
            <ref bean="msg_dataSource" />
        </property>
    </bean>

    <!-- 事务管理器   -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="msg_dataSource" />
    </bean>
    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />

</beans>