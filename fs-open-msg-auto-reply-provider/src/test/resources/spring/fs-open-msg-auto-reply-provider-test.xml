<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


    <!-- dubbo config  -->
    <dubbo:application name="open-msg-service-autoreply" />
    <dubbo:registry protocol="zookeeper"   address="msg.dubbo.registry.address=zookeeper://vlnx113029001.x.firstshare.cn:2181?backup=vlnx113029002.x.firstshare.cn:2181,vlnx113029003.x.firstshare.cn:2181"/>
    <dubbo:protocol name="dubbo" port="29201" />



 </beans>