<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd"
       default-lazy-init="false">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.autoreplymsg"/>


    <import resource="classpath:/spring/fs-open-msg-auto-reply-provider-test.xml"/>
    <import resource="classpath:/spring/spring-cms-test.xml"/>
    <import resource="classpath:/spring/spring-db-test.xml"/>
    <import resource="classpath:/spring/spring-dubbo-auto-reply-config-test.xml"/>

</beans>
