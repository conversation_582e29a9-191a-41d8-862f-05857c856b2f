package com.facishare.open.autoreplymsg.service;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/8.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/applicationContext.xml")

/**
 * Created by fengyh on 2016/6/26.
 */
public class MsgAutoReplyServiceTest extends AbstractJUnit4SpringContextTests {

    /**
    @Resource
    private MsgAutoReplyService msgAutoReplyService;
    private String test_enterprise_account = "fsfte2a";
*/
	@Resource
	private MsgAutoReplyService msgAutoReplyService;
    @Test
    public void test1() {
        int x = 5;
        System.out.println("xval: " + x);
    }
    
    @Test
	public void replyTest(){
    	try{
    		GetAutoReplyResult result = msgAutoReplyService.getCrossAutoReply("2", "E.55263.1040", "FSAID_11e1a302", "自动回复");
        	System.out.println(result);
    	}catch(Exception e){
    		e.printStackTrace();
    	}
	}
    
    @Test
	public void setCustomServiceReplySwitchWithTypeTest(){
    	try{
    		MsgBaseResult result = msgAutoReplyService.setCustomServiceReplySwitchWithType("2", "FSAID_11e1a445", 0, 2);
        	System.out.println(result);
    	}catch(Exception e){
    		e.printStackTrace();
    	}
	}


    /**
    @Test
    public void testGetCustomServiceAppListBySwitch() {

        GetCustomServiceAppListResult getCustomServiceAppListResult = msgAutoReplyService.getCustomServiceAppListBySwitch(test_enterprise_account, 1);
        System.out.println("getCustomServiceAppListResult for " + test_enterprise_account + "in open status : \n" + getCustomServiceAppListResult.toString());

        if (getCustomServiceAppListResult.getAppList().size() <= 0) {
            System.out.println("\ngetCustomServiceAppListResult.getAppList() not get enough appids for test");
            return;
        }

        int appListSizeBeforeDisable = getCustomServiceAppListResult.getAppList().size();

        //**disable the first appid, and getapplist again

        String appID = getCustomServiceAppListResult.getAppList().get(0);
        System.out.println("appid: " + appID + " is selected for test ");
        CommonResult commonResult = eaAuthService.stopEaAuth(null, null, test_enterprise_account, appID);
        assertEquals(commonResult.getErrCode(), 0);

        getCustomServiceAppListResult = msgAutoReplyService.getCustomServiceAppListBySwitch(test_enterprise_account, 1);
        System.out.println("getCustomServiceAppListResult for " + test_enterprise_account + "in open status : \n" + getCustomServiceAppListResult.toString());
        int appListSizeAfterDisable = getCustomServiceAppListResult.getAppList().size();

         System.out.println("appidnum before disable: "+ appListSizeBeforeDisable+ " appidnum after disable: " + appListSizeAfterDisable);
         assertEquals(appListSizeBeforeDisable, appListSizeAfterDisable+1);

        //restore appid's status
        commonResult = eaAuthService.startEaAuth(null, null, test_enterprise_account, appID);
        assertEquals(commonResult.getErrCode(), 0);
    }
    */
}
