package com.facishare.open.autoreplymsg.dao;

import com.facishare.open.autoreplymsg.dao.impl.AutoReplyDAOImpl;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import static org.junit.Assert.assertEquals;

/**
 * Created by fengyh on 2016/3/8.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/applicationContext.xml")
@Ignore
public class AutoReplyDAOTest extends AbstractJUnit4SpringContextTests {

    @Resource
    private AutoReplyDAOImpl autoReplyDAOImpl;

    private String test_appID = "FSAID_TEST1234567890";
    private String test_enterprise_account = "fsfte2a";

    @Before
    public void TestQueryBefore() throws Exception {
        int x = 1;
        System.out.println(x);
    }

    @Test
    public void TestQuery() throws Exception {

        try {
            int ret = autoReplyDAOImpl.queryAutoReplySwitch(test_appID, test_enterprise_account);
            System.out.println("queryAutoReplySwitch :" + ret);

            /**
            autoReplyDAOImpl.setCustomServiceReplySwitch(test_appID, test_enterprise_account, 1);
            ret = autoReplyDAOImpl.getCustomServiceReplySwitch(test_appID, test_enterprise_account);
            System.out.println("queryAutoReplySwitch  after set:" + ret);
            assertEquals(1, ret);

            autoReplyDAOImpl.setCustomServiceReplySwitch(test_appID, test_enterprise_account, 0);
            ret = autoReplyDAOImpl.getCustomServiceReplySwitch(test_appID, test_enterprise_account);
            System.out.println("queryAutoReplySwitch  after clear:" + ret);
            assertEquals(0, ret);
            */

        } catch (Exception e) {
            System.out.println("Exception :" + e.toString());
        }

    }
}
