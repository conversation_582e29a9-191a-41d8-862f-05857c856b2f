package com.facishare.open.autoreplymsg.service.test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.customerservice.outapi.service.CustomerServiceService;
import com.facishare.enterprise.common.model.EnterpriseSimpleVo;
import com.facishare.enterprise.common.result.Result;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.model.WorkbenchMessageVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionQueryVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.qixin.api.model.open.arg.OpenCSCustomerProperty;
import com.facishare.qixin.api.model.open.arg.OpenCrossCSSessionUpdateArg;
import com.facishare.qixin.api.model.open.arg.OpenOSS1DefinitionArg;
import com.facishare.qixin.api.model.open.result.UpdateOSS1DefinitionResult;
import com.facishare.qixin.api.open.OpenCrossCustomerService;
import com.facishare.qixin.api.open.OpenCrossDefinitionService;
import com.facishare.qixin.api.open.OpenSessionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations="classpath:spring/applicationContext.xml")
public class MsgCustomerServiceTest {

    @Autowired
    private MsgCustomerService msgCustomerService;
//    
//    @Autowired
//    private MsgAutoReplyService msgAutoReplyService;
    @Autowired
    private OpenCrossCustomerService openCrossCustomService;
    
    @Autowired
    private OpenSessionService openSessionService;
    
    @Autowired
    private OpenCrossDefinitionService openCrossDefinitionService;

    @Autowired
    private CustomerServiceService customerServiceService;
    
    @Test
    public void testSetCustomerName() {
        
        CustomerSessionInfoVO customerSessionInfoVO = new CustomerSessionInfoVO();
        
        customerSessionInfoVO.setAppId("FSAID_11e1a3eb");
        customerSessionInfoVO.setEnterpriseAccount("2");
        customerSessionInfoVO.setCustomerName("fscest1");
        customerSessionInfoVO.setCustomerIcon("https://a6.ceshi113.com/FSC/EM/Avatar/GetAvatar?path=N_201708_03_f1413626520f4b68909ffc18578ecee0.png&size=150_150&ea=appCenter");
        customerSessionInfoVO.setCustomerSessionType(2);
        customerSessionInfoVO.setCustomerList(Lists.newArrayList("1002"));
        
        System.out.println(msgCustomerService.setCustomerSessionInfo(customerSessionInfoVO).getData());
    }
//    
//    @Test
//    public void testSetCustomerSwitch() {
//        System.out.println(msgAutoReplyService.setCustomServiceReplySwitch("fsfte2a", "FSAID_1314568", 1));
//    }
//    
    @Test
    public void testSetCustomerStatus() {
        System.out.println(msgCustomerService.updatePlatformMetaSession("FSAID_1314568", "fsfte2a", 4, null));
    }
    
    @Test
    public void testSetWorkSession() {
        
       // msgCustomerService.updateWorkbenchSession(workbenchSessionVO);
        
        System.out.println(msgCustomerService.updatePlatformMetaSession("FSAID_1314568", "fsfte2a", 4 ,null));
    }

    @Test
    public void testSendWechatMsgSetupSession() {
        //String testAppID = "FSAID_13132b3";
        String testAppID = "FSAID_bebc794";
        String testEa = "fsfte2a";
        CustomerSessionInfoVO customerSessionInfoVO = new CustomerSessionInfoVO();
        customerSessionInfoVO.setEnterpriseAccount(testEa);
        customerSessionInfoVO.setAppId(testAppID);
        //customerSessionInfoVO.setUserId();
        customerSessionInfoVO.setCustomerIcon("https://open.fxiaoke.com/fscdn/img?imgId=group1/M00/01/18/rBEiBleQiiuAdxvXAAFMKIxvrm8447.png&type=WEB&width=60&height=60");
        customerSessionInfoVO.setCustomerName("hardy_testname");

        CustomerSessionResult<Boolean> customerSessionResult =
        msgCustomerService.setCustomerSessionInfo(customerSessionInfoVO);

        System.out.println("customerSessionResult: " + customerSessionResult.toString());

    }

    @Test
    public void testWechatSessionSetup() {

        String testAppID = "FSAID_bebc794";
        String testEa = "fsfte2a";
        PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
        platformMetaSessionVO.setCustomerSessionName("hardy微信工作台名称");
        platformMetaSessionVO.setAdminDescription("hardy管理员工作台描述");
        platformMetaSessionVO.setCustomerDescription("hardy客服人员工作台描述");
        platformMetaSessionVO.setCustomerSessionPortrait("https://open.fxiaoke.com/fscdn/img?imgId=group1/M00/01/18/rBEiBleQiiuAdxvXAAFMKIxvrm8447.png&type=WEB&width=60&height=60");
        platformMetaSessionVO.setCustomerSessionSubName("hardy工作台副标题");
        CustomerSessionResult<Boolean> customerSessionResult =
                msgCustomerService.updatePlatformMetaSessionNew(testAppID, testEa, 0, platformMetaSessionVO, 1);
        System.out.println("customerSessionResult: " + customerSessionResult.toString());
    }

    @Test
    public void testSetAppServiceStatus() {
    	msgCustomerService.setAppServiceStatus("61037", "FSAID_bebd113", false, 2);
    }
    
    @Test
    public void testUpdatePlatformMetaSessionNew() {
    	//创建工作台
    	String appId = "FSAID_11e1a31d";
    	String enterpriseAccount = "2";
    	int updateType = 2;
    	int customerSessionType = 2;
    	PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
    	platformMetaSessionVO.setAdminDescription("欢迎使用互联服务号");
    	platformMetaSessionVO.setAdmins(Lists.newArrayList(1070));
    	platformMetaSessionVO.setCustomerDescription("我是客服2");
    	platformMetaSessionVO.setCustomers(Lists.newArrayList(1070));
    	platformMetaSessionVO.setCustomerSessionName("tw互联号测试");
    	//platformMetaSessionVO.setCustomerSessionPortrait(customerSessionPortrait);
    	platformMetaSessionVO.setCustomerSessionPortrait("https://a6.ceshi113.com/FSC/EM/Avatar/GetAvatar?path=N_201707_11_edfee454f47f4cb299c92a4d15720270.png&size=150_150&ea=appCenter&type=WEB");
    	platformMetaSessionVO.setCustomerSessionSubName("小美2");
    	msgCustomerService.updatePlatformMetaSessionNew(appId, enterpriseAccount, updateType, platformMetaSessionVO, customerSessionType);
    }
    
    @Test
    public void testUpdateCrossCustomerServicePlatformSession(){
    	 OpenCrossCSSessionUpdateArg openCrossCSSessionUpdateArg = new OpenCrossCSSessionUpdateArg();
         openCrossCSSessionUpdateArg.setAppId("FSAID_11e1a3d3");
         openCrossCSSessionUpdateArg.setEnterpriseAccount("2");
         
         OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
         
         openCSCustomerProperty.setCustomers(Lists.newArrayList(1070,1000));
         
         openCrossCSSessionUpdateArg.setProperty(openCSCustomerProperty);
         openCrossCSSessionUpdateArg.setUpdateType(2);
         openCrossCustomService.updateCrossCustomerServicePlatformSession(openCrossCSSessionUpdateArg);
         
    }
    
    @Test
    public void testUpdateCustomerServiceInfo(){
    	String appId = "FSAID_11e1a379";
    	String cname = "小美1";
    	String pic = "https://a0.ceshi113.com/FSC/EM/Avatar/GetAvatar?path=N_201708_04_bb9305218de446a5b1aff57d6e4b05fb.png&size=150_150&ea=appCenter";
    	
    	//openSessionService.updateCustomerServiceInfo(appId, cname,pic);
    	OpenOSS1DefinitionArg arg = new OpenOSS1DefinitionArg();
    	arg.setAppId(appId);
    	arg.setCustomerName(cname);
    	arg.setCustomerPortrait(pic);
    	UpdateOSS1DefinitionResult result = openCrossDefinitionService.updateOSS1Definition(arg);
    	System.out.println(result);
    	Result<List<EnterpriseSimpleVo>> listAuthedDownResult = customerServiceService.listAuthedDownstreamEaInfos("2",appId);
		if (listAuthedDownResult.isSuccess()) {
			List<EnterpriseSimpleVo> enList = listAuthedDownResult.getData();
			List<String> eaList = Lists.newArrayList();
			enList.forEach(v -> {
				eaList.add(v.getEnterpriseAccount());
			});
			arg.setUpstreamEnterprise("2");
			arg.setDownstreamEnterpriseList(Lists.newArrayList("53424"));
			result = openCrossDefinitionService.updateOSS1Definition(arg);
		}
    	//arg.setDownstreamEnterpriseList(Lists.newArrayList("53424"));
    	result = openCrossDefinitionService.updateOSS1Definition(arg);
    	System.out.println(result);
    }
    
    @Test
    public void testUpdateWorkbenchSession(){
    	String appId = "FSAID_11e1a445";
    	WorkbenchSessionVO workbenchSessionVO = new WorkbenchSessionVO();
    	workbenchSessionVO.setAppId(appId);
    	workbenchSessionVO.setEnterpriseAccount("53424");
    	workbenchSessionVO.setUpEnterpriseAccount("2");
    	workbenchSessionVO.setSessionIcon("https://open.ceshi113.com/fscdn/img?imgId=group1/M00/00/AF/rB9tNFhPxhiAcA98AAANoLLGuXY894.png");
    	workbenchSessionVO.setSessionName("文章评论");
    	workbenchSessionVO.setSessionType(2);
    	workbenchSessionVO.setUrl("https://www.ceshi113.com/open/mpadmin/#!/articlelist?appId=FSAID_11e1a445&fsEa=2");
    	workbenchSessionVO.setWorkbenchType(3);
    	msgCustomerService.updateWorkbenchSession(workbenchSessionVO);
    	
    	WorkbenchMessageVO workbenchMessageVO = new WorkbenchMessageVO();
    	workbenchMessageVO.setAppId(appId);
    	workbenchMessageVO.setEnterpriseAccount("53424");
    	workbenchMessageVO.setUpEnterpriseAccount("2");
    	workbenchMessageVO.setLastSummary("zhangjw提交了一条新的评论");
    	workbenchMessageVO.setSenderId(1040);
    	workbenchMessageVO.setSessionType(2);
    	workbenchMessageVO.setWorkbenchType(3);
    	msgCustomerService.sendWorkbenchMessage(workbenchMessageVO);
    }
    
    @Test
    public void testGetWorkbenchSessionInfo(){
    	String appId = "FSAID_11e1a445";
    	WorkbenchSessionQueryVO workbenchSessionQueryVO = new WorkbenchSessionQueryVO();
    	workbenchSessionQueryVO.setAppId(appId);
    	workbenchSessionQueryVO.setEnterpriseAccount("2");
    	//workbenchSessionQueryVO.setSessionType(2);
    	workbenchSessionQueryVO.setWorkbenchType(3);
    	msgCustomerService.getWorkbenchSessionInfo(workbenchSessionQueryVO);
    }
    
    public static void main(String[] args){  
        String[] arrayA = new String[] { "1", "2", "3", "3", "4", "5" };  
        String[] arrayB = new String[] { "3", "4", "4", "5", "6", "1", "1","2" ,"2", "7" };  
  
        Set<String> a = Sets.newHashSet();
        Set<String> b = Sets.newHashSet( arrayB);  
  
        Set<String> union = new HashSet<>(CollectionUtils.union(a, b));  //并集  
        Set<String> intersection = new HashSet<>(CollectionUtils.intersection( a, b )); //交集  
        Set<String> disjunction = new HashSet<>(CollectionUtils.disjunction( a, b )); //析取  
        Set<String> subtract = new HashSet<>(CollectionUtils.subtract( a, b )); //差集  
        
        System.out.println( "A: " + a.toString() );  
        System.out.println( "B: " + b.toString() );  
        System.out.println( "Union: " + union.toString());  
        System.out.println( "Intersection: " +  
                intersection.toString() );  
        System.out.println( "Disjunction: " +  
                disjunction.toString() );  
        System.out.println( "Subtract: " + subtract.toString() ); 
        
        a.removeAll(intersection);
        b.removeAll(intersection);
        
        System.out.println( "A: " + a.toString() );  
        System.out.println( "B: " + b.toString() );  
    } 
}
