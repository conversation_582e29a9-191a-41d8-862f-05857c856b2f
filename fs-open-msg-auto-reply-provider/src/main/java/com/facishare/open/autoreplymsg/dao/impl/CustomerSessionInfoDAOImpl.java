package com.facishare.open.autoreplymsg.dao.impl;

import java.util.List;
import java.util.Map;

import com.facishare.open.autoreplymsg.dao.CustomerSessionInfoDAO;
import com.facishare.open.autoreplymsg.dao.base.AutoReplyMsgBaseDAO;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

/**
 * Created by huanghp on 2016/6/3.
 */
@Repository
public class CustomerSessionInfoDAOImpl extends AutoReplyMsgBaseDAO<CustomerSessionInfoDO> implements CustomerSessionInfoDAO {

    private static final Logger logger = LoggerFactory.getLogger(CustomerSessionInfoDAOImpl.class);

    @Override
    public boolean createCustomerSessionInfo(CustomerSessionInfoDO customerSessionInfoDO) {
        logger.info("createCustomerSessionInfo param--customerSessionInfoDO:{} ", customerSessionInfoDO);

        long affectedRows = this.save("createCustomerSessionInfo", customerSessionInfoDO);
        
        logger.info("createCustomerSessionInfo  customerSessionInfoDO:{}, get affectedRows:{} from save()", customerSessionInfoDO, affectedRows);

        if (affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }

    public boolean updateCustomerSessionInfo(CustomerSessionInfoDO customerSessionInfoDO) {
        logger.info("updateCustomerSessionInfo param--customerSessionInfoDO:{} ", customerSessionInfoDO);

        long affectedRows = this.update("updateCustomerSessionInfo", customerSessionInfoDO);
        
        logger.info("updateCustomerSessionInfo  customerSessionInfoDO:{}, get affectedRows:{} from update()", customerSessionInfoDO, affectedRows);

        if (affectedRows > 0) {
            return true;
        } else {
            return false;
        }
    }
    
    public CustomerSessionInfoDO findCustomerSessionInfo(Map<String, Object> paramMap) {
        logger.info("findCustomerSessionInfo param--paramMap:{} ", paramMap);

        List<CustomerSessionInfoDO> customerInfoList = this.getList("queryCustomerSessionInfo", paramMap);
        
        logger.info("findCustomerSessionInfo  paramMap:{}, customerInfoList={} from find ()", paramMap, customerInfoList);

        if (CollectionUtils.isNotEmpty(customerInfoList)) {
            return customerInfoList.get(0);
        }
        
        return null;
    }
}
