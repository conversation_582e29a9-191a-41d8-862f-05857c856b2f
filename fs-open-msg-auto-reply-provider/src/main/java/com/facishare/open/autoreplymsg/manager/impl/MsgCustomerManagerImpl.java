package com.facishare.open.autoreplymsg.manager.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.annotation.Resource;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.enterpriserelation2.arg.ListAuthedDownstreamEnterpriseInfosArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.ListAuthedDownstreamEnterpriseInfosResult;
import com.fxiaoke.enterpriserelation2.service.CustomerServiceService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

//import org.apache.rocketmq.common.message.;
//import com.facishare.common.rocketmq.AutoConfRocketMQSender;
import com.facishare.open.autoreplymsg.dao.CustomerSessionInfoDAO;
import com.facishare.open.autoreplymsg.manager.MsgCustomerManager;
import com.facishare.open.autoreplymsg.model.CustomerChangeItem;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.msg.common.constants.CustomerSessionTypeEnum;
import com.facishare.qixin.api.model.open.arg.OpenCSCustomerProperty;
import com.facishare.qixin.api.model.open.arg.OpenCSSessionUpdateArg;
import com.facishare.qixin.api.model.open.arg.OpenCrossCSSessionUpdateArg;
import com.facishare.qixin.api.model.open.arg.OpenOSS1DefinitionArg;
import com.facishare.qixin.api.model.open.result.UpdateOSS1DefinitionResult;
import com.facishare.qixin.api.open.OpenCrossCustomerService;
import com.facishare.qixin.api.open.OpenCrossDefinitionService;
import com.facishare.qixin.api.open.OpenCustomService;
import com.facishare.qixin.api.open.OpenSessionService;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

@Service("msgCustomerManager")
public class MsgCustomerManagerImpl implements MsgCustomerManager {

    private static final Logger logger = LoggerFactory.getLogger(MsgCustomerManagerImpl.class);

    @Autowired
    private CustomerSessionInfoDAO customerSessionInfoDAO;
    
    @Autowired
    private OpenCustomService openCustomService;
    
    @Autowired
    private OpenCrossCustomerService openCrossCustomService;
    
    @Autowired
    private OpenCrossDefinitionService openCrossDefinitionService;
    
    @Autowired
    private OpenSessionService openSessionService;
    
    @Autowired
    private CustomerServiceService customerServiceService;
    @Autowired
    private EIEAConverter eIEAConverter;

    @Override
    @Transactional
    public Boolean setCustomerSessionInfo(CustomerSessionInfoVO customerSessionInfoVO) {
        try {
            
            String customerList = "";
            
            List<Integer> customerIds = new ArrayList<Integer>();
            Set<Integer> customerIdSet = new HashSet<Integer>();
            Set<Integer> srcCustomerSet = new HashSet<Integer>();
            
            if(customerSessionInfoVO.getCustomerList() != null && customerSessionInfoVO.getCustomerList().size() > 0) {
                customerList = Joiner.on(",").join(customerSessionInfoVO.getCustomerList());
                
                customerSessionInfoVO.getCustomerList().forEach(customerId->{
                    customerIds.add(Integer.parseInt(customerId));
                    customerIdSet.add(Integer.parseInt(customerId));
                });
            }
            
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("appId", customerSessionInfoVO.getAppId());
            paramMap.put("enterpriseAccount", customerSessionInfoVO.getEnterpriseAccount());
            
            CustomerSessionInfoDO customerSessionInfoDO = customerSessionInfoDAO.findCustomerSessionInfo(paramMap);
            
            boolean setRet = false;
            
            if (customerSessionInfoDO != null) {
                customerSessionInfoDO.setCustomerIcon(customerSessionInfoVO.getCustomerIcon());
                customerSessionInfoDO.setCustomerName(customerSessionInfoVO.getCustomerName());
                
                if(!Strings.isNullOrEmpty(customerSessionInfoDO.getCustomerList())) {
                    Splitter.on(",").splitToList(customerSessionInfoDO.getCustomerList()).forEach(srcCustomerId->{
                        srcCustomerSet.add(Integer.parseInt(srcCustomerId));
                    });
                }
                
                customerSessionInfoDO.setCustomerList(customerList);
                customerSessionInfoDO.setGmtModified(new Date());
                
                setRet = customerSessionInfoDAO.updateCustomerSessionInfo(customerSessionInfoDO);
            } else {
                customerSessionInfoDO = new CustomerSessionInfoDO();
                
                customerSessionInfoDO.setCustomerIcon(customerSessionInfoVO.getCustomerIcon());
                customerSessionInfoDO.setCustomerName(customerSessionInfoVO.getCustomerName());
                customerSessionInfoDO.setCustomerList(customerList);
                customerSessionInfoDO.setAppId(customerSessionInfoVO.getAppId());
                customerSessionInfoDO.setEnterpriseAccount(customerSessionInfoVO.getEnterpriseAccount());
                customerSessionInfoDO.setGmtCreate(new Date());
                customerSessionInfoDO.setGmtModified(new Date());
                
                setRet = customerSessionInfoDAO.createCustomerSessionInfo(customerSessionInfoDO);
            }
            
            if (!setRet) {
                return setRet;
            }
            
            OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
            openCSCustomerProperty.setCustomers(customerIds);
                        
            Boolean qxSetRet = false;
            logger.info("setCustomerSessionInfo customerSessionType:{}",customerSessionInfoVO.getCustomerSessionType());
			if (!Objects.isNull(customerSessionInfoVO.getCustomerSessionType()) && CustomerSessionTypeEnum.cross.getType() == customerSessionInfoVO.getCustomerSessionType()) {
				// 工作台这边只涉及到客服列表的更新，没办法更新客服的名称与LOGO
				OpenCrossCSSessionUpdateArg openCrossCSSessionUpdateArg = new OpenCrossCSSessionUpdateArg();
				openCrossCSSessionUpdateArg.setAppId(customerSessionInfoVO.getAppId());
				openCrossCSSessionUpdateArg.setEnterpriseAccount(customerSessionInfoVO.getEnterpriseAccount());
				openCrossCSSessionUpdateArg.setProperty(openCSCustomerProperty);
				openCrossCSSessionUpdateArg.setUpdateType(2);
				openCrossCustomService.updateCrossCustomerServicePlatformSession(openCrossCSSessionUpdateArg);
				logger.info("setCustomerSessionInfo updateCrossCustomerServicePlatformSession arg:{}",openCrossCSSessionUpdateArg);
				
				OpenOSS1DefinitionArg openOSS1DefinitionArg = new OpenOSS1DefinitionArg();
				openOSS1DefinitionArg.setAppId(customerSessionInfoVO.getAppId());
				openOSS1DefinitionArg.setCustomerName(customerSessionInfoVO.getCustomerName());
				openOSS1DefinitionArg.setCustomerPortrait(customerSessionInfoVO.getCustomerIcon());
				UpdateOSS1DefinitionResult updateOSS1DefinitionResult = openCrossDefinitionService.updateOSS1Definition(openOSS1DefinitionArg);//更新上游工作台
				logger.info("setCustomerSessionInfo updateOSS1Definition arg:{},result:{}",openOSS1DefinitionArg,updateOSS1DefinitionResult);

                String ea = customerSessionInfoVO.getEnterpriseAccount();
                String appId = customerSessionInfoVO.getAppId();

                HeaderObj headerObj = HeaderObj.newInstance(appId, eIEAConverter.enterpriseAccountToId(ea), null, null);

                ListAuthedDownstreamEnterpriseInfosArg arg = new ListAuthedDownstreamEnterpriseInfosArg();
                arg.setUpstreamEa(ea);
                arg.setCustomerServiceId(appId);

                // listAuthedDownstreamEaInfos
                RestResult<List<ListAuthedDownstreamEnterpriseInfosResult>> listAuthedDownResult =
                        customerServiceService.listAuthedDownstreamEnterpriseInfos(headerObj, arg);
                logger.info("listAuthedDownstreamEnterpriseInfos success. ea:{}, appId:{}, result:{}", ea, appId, listAuthedDownResult);
				if (listAuthedDownResult.isSuccess()) {
                    List<ListAuthedDownstreamEnterpriseInfosResult> data = listAuthedDownResult.getData();
                    List<String> enAccountList = Lists.newArrayList();
                    data.forEach(v -> {
						enAccountList.add(v.getEnterpriseAccount());
					});
					openOSS1DefinitionArg.setUpstreamEnterprise(customerSessionInfoVO.getEnterpriseAccount());
					openOSS1DefinitionArg.setDownstreamEnterpriseList(enAccountList);
					updateOSS1DefinitionResult = openCrossDefinitionService.updateOSS1Definition(openOSS1DefinitionArg);//更新所有下游服务号
					logger.info("setCustomerSessionInfo updateOSS1Definition arg:{},result:{}",openOSS1DefinitionArg,updateOSS1DefinitionResult);
				}
				qxSetRet = !Objects.isNull(updateOSS1DefinitionResult) && 0==updateOSS1DefinitionResult.getErrorCode();
			} else {
				OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
				openCSSessionUpdateArg.setAppId(customerSessionInfoVO.getAppId());
				openCSSessionUpdateArg.setEnterpriseAccount(customerSessionInfoVO.getEnterpriseAccount());
				openCSSessionUpdateArg.setUpdateType(2);
				openCSSessionUpdateArg.setProperty(openCSCustomerProperty);
				openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
				openSessionService.updateCustomerServiceInfo(customerSessionInfoVO.getAppId(), customerSessionInfoVO.getCustomerName(), customerSessionInfoVO.getCustomerIcon());
				qxSetRet = true;
			}
            
            if (!qxSetRet) {
                throw new RuntimeException();
            }
            
            
            logger.info("customerIds={}", customerIds);
            logger.info("customerIdSet={}", customerIdSet);
            logger.info("srcCustomerSet={}", srcCustomerSet);
            
            try {               
                Collection intersectionIds = CollectionUtils.intersection(customerIdSet, srcCustomerSet);
                
                customerIdSet.removeAll(intersectionIds);
                srcCustomerSet.removeAll(intersectionIds);
                
                CustomerChangeItem customerChangeItem = new CustomerChangeItem();
                customerChangeItem.setAppId(customerSessionInfoVO.getAppId());
                customerChangeItem.setEnterpriseAccount(customerSessionInfoVO.getEnterpriseAccount());
                customerChangeItem.setUserId(customerSessionInfoVO.getUserId());
                customerChangeItem.setOperationTime(new Date());
                
                if (customerIdSet != null && customerIdSet.size() > 0) {
                    customerChangeItem.setAddCustomerIds(new ArrayList<Integer>(customerIdSet));
                }
                
                if (srcCustomerSet != null && srcCustomerSet.size() > 0) {
                    customerChangeItem.setRemoveCustomerIds(new ArrayList<Integer>(srcCustomerSet));
                }

                /**
                Message message = new Message();
                message.setFlag(200);
                message.setTags("customerChangeTags");
                message.setBody(customerChangeItem.toProto());
                 * 变更管理员以后，把时间投递到MQ。
                 * 应用中心会做相应的处理，比如推送一些引导信息。
                 * */
                //autoConfRocketMQSender.send(message);
                
            } catch (Exception ex) {
                logger.error("setCustomerSessionInfo send message notify error={}", ex);
            }

            return qxSetRet;
        } catch (Exception e) {
            logger.error("setCustomerSessionInfo error param={} error={}", customerSessionInfoVO, e);
            return false;
        }
        
    }

}
