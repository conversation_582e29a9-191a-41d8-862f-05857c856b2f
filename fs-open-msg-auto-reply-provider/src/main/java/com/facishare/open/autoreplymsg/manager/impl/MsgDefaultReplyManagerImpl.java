package com.facishare.open.autoreplymsg.manager.impl;


import com.facishare.open.autoreplymsg.dao.impl.DefaultReplyDAOImpl;
import com.facishare.open.autoreplymsg.manager.MsgDefaultReplyManager;
import com.facishare.open.autoreplymsg.model.CreateDefaultReplyVO;
import com.facishare.open.autoreplymsg.model.DefaultReplyDO;
import com.facishare.open.autoreplymsg.model.UpdateDefaultReplyVO;
import com.facishare.open.autoreplymsg.result.CreateDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fengyh on 2016/3/7.
 */
@Service
public class MsgDefaultReplyManagerImpl implements MsgDefaultReplyManager {

    private final int msgErrCode = 50000;

    @Autowired
    private DefaultReplyDAOImpl defaultReplyDAOImpl;

    /**
     * 创建默认自动回复
     *
     * @param createDefaultReplyVO
     * @return : CreateDefaultAutoReplyResult
     */
    @Override
    public CreateDefaultReplyResult createDefaultReply(CreateDefaultReplyVO createDefaultReplyVO) {
        DefaultReplyDO inParam =  new DefaultReplyDO();
        inParam.setAppID(createDefaultReplyVO.getAppID());
        inParam.setEnterpriseAccount(createDefaultReplyVO.getEnterpriseAccount());
        inParam.setContentImgTxtID(createDefaultReplyVO.getContentImgTxtID());
        inParam.setContentTxt(createDefaultReplyVO.getContentTxt());
        inParam.setActiveReplyType(createDefaultReplyVO.getActiveReplyType());

        int msgReplyID = (int)defaultReplyDAOImpl.createDefaultReply(inParam);
        CreateDefaultReplyResult ret = new CreateDefaultReplyResult(0, "OK");
        if (msgReplyID > 0) {
            ret.setReplyMsgID(msgReplyID);
        } else   {
            ret.setErrCode(msgErrCode);
            ret.setErrorMsg("SQL FAIL");
        }
        return ret;
    }

    /**
     * 删除默认自动回复
     *
     * @param enterpriseAccount 企业在fs的账号
     * @param appID
     * @param replyMsgID
     * @return :MsgBaseResult
     */
    @Override
    public MsgBaseResult deleteDefaultReply(String enterpriseAccount, String appID, long replyMsgID) {
        int errCode = defaultReplyDAOImpl.deleteDefaultReply(replyMsgID, enterpriseAccount, appID);
        if (0 != errCode) {
            return new MsgBaseResult(msgErrCode, "SQL FAIL");
        }else {
            return new MsgBaseResult(MsgCodeEnum.SUCCESS);
        }
    }

    /**
     * 更新默认回复
     *
     * @param updateDefaultAutoReplyVO
     * @return :MsgBaseResult
     */
    @Override
    public MsgBaseResult updateDefaultReply(UpdateDefaultReplyVO updateDefaultAutoReplyVO) {
        DefaultReplyDO inParam =  new DefaultReplyDO();
        inParam.setReplyMsgID(updateDefaultAutoReplyVO.getReplyMsgID());
        inParam.setAppID(updateDefaultAutoReplyVO.getAppID());
        inParam.setEnterpriseAccount(updateDefaultAutoReplyVO.getEnterpriseAccount());
        inParam.setActiveReplyType(updateDefaultAutoReplyVO.getActiveReplyType());
        inParam.setContentImgTxtID(updateDefaultAutoReplyVO.getContentImgTxtID());
        inParam.setContentTxt(updateDefaultAutoReplyVO.getContentTxt());
        int errCode  = defaultReplyDAOImpl.updateDefaultReply(inParam);
        if (0 != errCode) {
            return new MsgBaseResult(msgErrCode, "SQL FAIL");
        }else {
            return new MsgBaseResult(MsgCodeEnum.SUCCESS);
        }
    }

    /**
     * 查询指定服务号的默认回复
     *
     * @param enterpriseAccount
     * @param appID
     * @return QueryDefaultReplyResult
     */
    @Override
    public QueryDefaultReplyResult queryDefaultReply(String enterpriseAccount, String appID) {
        DefaultReplyDO defaultReplyDO =  defaultReplyDAOImpl.queryDefaultReply(enterpriseAccount, appID);
        QueryDefaultReplyResult queryDefaultReplyResult = new QueryDefaultReplyResult(0, "");

        if (null == defaultReplyDO) {
            queryDefaultReplyResult.setIsValid(false);
        } else {
            queryDefaultReplyResult.setReplyMsgID(defaultReplyDO.getReplyMsgID());
            queryDefaultReplyResult.setActiveReplyType(defaultReplyDO.getActiveReplyType());
            queryDefaultReplyResult.setContentImgTxtID(defaultReplyDO.getContentImgTxtID());
            queryDefaultReplyResult.setContentTxt(defaultReplyDO.getContentTxt());
        }
        return queryDefaultReplyResult;
    }
}
