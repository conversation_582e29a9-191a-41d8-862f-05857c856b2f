package com.facishare.open.autoreplymsg.dao.impl;

import com.facishare.open.autoreplymsg.dao.AutoReplyDAO;
import com.facishare.open.autoreplymsg.dao.base.AutoReplyMsgBaseDAO;
import com.facishare.open.autoreplymsg.model.AutoReplyDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by fengyh on 2016/3/5.
 */
@Service
public class AutoReplyDAOImpl extends AutoReplyMsgBaseDAO<AutoReplyDO> implements AutoReplyDAO {

    private static final Logger logger = LoggerFactory.getLogger(AutoReplyDAOImpl.class);

    @Override
    public int queryAutoReplySwitch(String appID, String enterpriseAccount) {

        logger.info("queryAutoReplySwitch param--appid:{},enterpriseAccount:{}",
                appID, enterpriseAccount);

        AutoReplyDO inputDO = new AutoReplyDO();
        inputDO.setAppID(appID);
        inputDO.setEnterpriseAccount(enterpriseAccount);

        AutoReplyDO outputDO = this.getUnique("queryAutoReplySwitch", inputDO);
        logger.info("queryAutoReplySwitch ret--outputDO:{}", outputDO);
        if (null == outputDO) {
            //查不到开关则返回关闭状态
            return 0;
        }
        return outputDO.getStatus();
    }

    @Override
    public int setAutoReplySwitch(String appID, String enterpriseAccount, int status) {

        logger.info("setAutoReplySwitch param--appid:{},enterpriseAccount:{}, status: {}",
                appID, enterpriseAccount, status);

        AutoReplyDO inputDO = new AutoReplyDO();
        inputDO.setAppID(appID);
        inputDO.setEnterpriseAccount(enterpriseAccount);
        inputDO.setStatus(status);

        //该SQL语句 不存在时插入，存在时更新. 插入时影响的记录数为1， 更新时为2.
        int ret = this.update("setAutoReplySwitch", inputDO);

        logger.info("setAutoReplySwitch ret:{}", ret);
        return (ret > 0 ? 0 : 1);
    }

    @Override
    public long getLastAutoReplyTime(String appID, String fsUserAccount) {
        logger.info("queryLastAutoReplyTime param--appid:{},fsUserAccount:{}",
                appID, fsUserAccount);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("appID", appID);
        map.put("fsUserAccount", fsUserAccount);

        try {
            AutoReplyDO outputDO = this.getUnique("queryLastAutoReplyTime", map);
            return outputDO.getLastAutoReplyTime();
        }catch (Exception e) {
            logger.info("queryLastAutoReplyTime empty result, return 0 ");
            return 0;
        }
    }

    @Override
    public int setLastAutoReplyTime(String appID, String fsUserAccount, long lastReplyTime) {
        logger.info("setLastAutoReplyTime param--appid:{},fsUserAccount:{}, lastReplyTime:{}",
                appID, fsUserAccount, lastReplyTime);
        AutoReplyDO inputDO = new AutoReplyDO();
        inputDO.setAppID(appID);
        inputDO.setFsUserAccount(fsUserAccount);
        inputDO.setLastAutoReplyTime(lastReplyTime);

        //该SQL语句 不存在时插入，存在时更新. 插入时影响的记录数为1， 更新时为2.
        try {
            int ret = this.update("setLastAutoReplyTime", inputDO);
            logger.info("setLastAutoReplyTime ret--{} ", ret);
            return  (ret > 0 ? 0 : 1);
        }catch (Exception e) {
            logger.info("catch setLastAutoReplyTime exception--");
            return 1;
        }
    }

    /**
     * 查询客服开关状态
     * */
    @Override
    public int getCustomServiceReplySwitch(String enterpriseAccount, String appID) {
        logger.info("getCustomServiceReplySwitch param--appid:{},enterpriseAccount:{}",
                appID, enterpriseAccount);

        AutoReplyDO inputDO = new AutoReplyDO();
        inputDO.setAppID(appID);
        inputDO.setEnterpriseAccount(enterpriseAccount);

        AutoReplyDO outputDO = this.getUnique("queryCustomServiceSwitch", inputDO);
        logger.info("queryAutoReplySwitch ret--outputDO:{}", outputDO);
        if (null == outputDO) {
            //查不到开关则返回关闭状态
            return 2;
        }
        return outputDO.getStatus();
    }

    /**
     * 设置客服开关状态
     * */
    @Override
    public int setCustomServiceReplySwitch(String appID, String enterpriseAccount, int switchState) {
        logger.info("setCustomServiceReplySwitch param--appid:{},enterpriseAccount:{}, switchState: {}",
                appID, enterpriseAccount, switchState);

        AutoReplyDO inputDO = new AutoReplyDO();
        inputDO.setAppID(appID);
        inputDO.setEnterpriseAccount(enterpriseAccount);
        inputDO.setStatus(switchState);

        //该SQL语句 不存在时插入，存在时更新. 插入时影响的记录数为1， 更新时为2.
        int ret = this.update("setCustomServiceSwitch", inputDO);

        logger.info("setCustomServiceReplySwitch ret:{}", ret);
        return (ret > 0 ? 0 : 1);
    }

    /**
     * 获取一个企业下面，指定客服开关状态的应用列表.
     * 至少设置过一次客服开关的应用，才会出现在列表中。
     * */
    @Override
    public List<String> getCustomServiceAppListBySwitch(String enterpriseAccount, int switchState) {

        logger.info("queryKeywordReply param--enterpriseAccount:{}, switchState:{}",
                enterpriseAccount, switchState);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("enterpriseAccount", enterpriseAccount);
        map.put("switchState", switchState);

        List<String> appList = new LinkedList<String>();
        List<AutoReplyDO> replyDOList  = this.getList("getCustomServiceAppListBySwitch", map);

        Iterator iterator  = replyDOList.iterator();
        while(iterator.hasNext()) {
            AutoReplyDO tmp = (AutoReplyDO)iterator.next();
            appList.add(tmp.getAppID());
        }

        logger.info("getCustomServiceAppListBySwitch result appList:{}",  appList);
        return appList;
    }
}
