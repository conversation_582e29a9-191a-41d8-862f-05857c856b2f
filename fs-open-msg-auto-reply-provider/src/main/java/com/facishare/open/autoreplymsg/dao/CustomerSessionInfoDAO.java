package com.facishare.open.autoreplymsg.dao;

import java.util.Map;

import com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO;

/**
 * Created by huanghp on 2016/6/3.
 */
public interface CustomerSessionInfoDAO {

    /**
     * 添加
     *  返回 : true成功,false 失败
    */
    public boolean createCustomerSessionInfo(CustomerSessionInfoDO customerSessionInfoDO);
    
    /**
     * 更新客服session名称
     * @param customerSessionInfoDO
     * @return
     */
    public boolean updateCustomerSessionInfo(CustomerSessionInfoDO customerSessionInfoDO);
    
    /**
     *查询客服session信息
     * @param paramMap
     * @return
     */
    public CustomerSessionInfoDO findCustomerSessionInfo(Map<String, Object> paramMap);

}
