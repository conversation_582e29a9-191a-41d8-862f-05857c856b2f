package com.facishare.open.autoreplymsg.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.autoreplymsg.dao.CustomerSessionInfoDAO;
import com.facishare.open.autoreplymsg.manager.MsgAutoReplyManager;
import com.facishare.open.autoreplymsg.manager.MsgCustomerManager;
import com.facishare.open.autoreplymsg.model.*;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.msg.common.constants.CustomerSessionTypeEnum;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.qixin.api.model.open.CustomerMembersTO;
import com.facishare.qixin.api.model.open.CustomerNameTO;
import com.facishare.qixin.api.model.open.WorkbenchEntityVo;
import com.facishare.qixin.api.model.open.arg.*;
import com.facishare.qixin.api.model.session.Session;
import com.facishare.qixin.api.open.OpenCrossCustomerService;
import com.facishare.qixin.api.open.OpenCrossDefinitionService;
import com.facishare.qixin.api.open.OpenCustomService;
import com.facishare.qixin.api.open.OpenSessionService;
import com.fxiaoke.enterpriserelation2.arg.ListDownstreamFriendEnterprisesArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.EnterpriseSimpleVo;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

//@Service("msgCustomerService")
//IgnoreI18nFile
@Service
public class MsgCustomerServiceImpl implements MsgCustomerService {

	private static final Logger logger = LoggerFactory.getLogger(MsgCustomerServiceImpl.class);

	@Autowired
	private CustomerSessionInfoDAO customerSessionInfoDAO;

	@Autowired
	private MsgCustomerManager msgCustomerManager;

	@Autowired
	private OpenCustomService openCustomService;

	@Autowired
	private OpenCrossCustomerService openCrossCustomerService;

	@Autowired
	private OpenSessionService openSessionService;

	@Autowired
	private MsgAutoReplyManager msgAutoReplyManager;

	@Autowired
	private OpenCrossDefinitionService openCrossDefinitionService;

	private Set<Integer> updateTypeSet = Sets.newHashSet(0, 1, 2, 3, 4, 5, 6, 7);


	@Resource
	private EnterpriseRelationService enterpriseRelationService;
	@Autowired
	private EIEAConverter eIEAConverter;

	@Override
	public CustomerSessionResult<Boolean> setCustomerSessionInfo(CustomerSessionInfoVO customerSessionInfoVO) {
		// TODO Auto-generated method stub
		try {
			customerSessionInfoVO.checkParams();

			Boolean setRet = msgCustomerManager.setCustomerSessionInfo(customerSessionInfoVO);

			return new CustomerSessionResult<Boolean>(setRet);

		} catch (IllegalArgumentException ex) {
			logger.error("setCustomerSessionInfo paramValidate exception params={} exception={}", customerSessionInfoVO, ex);
			return new CustomerSessionResult<>(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	@Override
	public CustomerSessionResult<CustomerSessionInfoVO> getCustomerSessionInfo(String appId, String enterpriseAccount) {
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("appId", appId);
			paramMap.put("enterpriseAccount", enterpriseAccount);

			CustomerSessionInfoDO customerSessionInfoDO = customerSessionInfoDAO.findCustomerSessionInfo(paramMap);

			if (customerSessionInfoDO != null) {
				CustomerSessionInfoVO customerSessionInfoVO = new CustomerSessionInfoVO();
				customerSessionInfoVO.setAppId(customerSessionInfoDO.getAppId());
				customerSessionInfoVO.setEnterpriseAccount(customerSessionInfoDO.getEnterpriseAccount());
				customerSessionInfoVO.setCustomerIcon(customerSessionInfoDO.getCustomerIcon());
				customerSessionInfoVO.setCustomerName(customerSessionInfoDO.getCustomerName());

				List<String> customerList = null;

				if (!Strings.isNullOrEmpty(customerSessionInfoDO.getCustomerList())) {
					customerList = Splitter.on(",").splitToList(customerSessionInfoDO.getCustomerList());
				}

				customerSessionInfoVO.setCustomerList(customerList);

				return new CustomerSessionResult<CustomerSessionInfoVO>(customerSessionInfoVO);
			}

			// 无结果集返回成功，但结果为空
			return new CustomerSessionResult<CustomerSessionInfoVO>();

		} catch (Exception e) {
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	@Override
	public CustomerSessionResult<Boolean> updatePlatformMetaSession(String appId, String enterpriseAccount, int updateType, PlatformMetaSessionVO platformMetaSessionVO) {
		return updatePlatformMetaSessionNew(appId, enterpriseAccount, updateType, platformMetaSessionVO, CustomerSessionTypeEnum.multi.getType());
	}

	private Boolean updateWxPlatformMetaSessionNew(String appId, String ea, int updateType, PlatformMetaSessionVO platformMetaSessionVO) {

		Boolean ret = true;
		OpenCSCustomerProperty openCSCustomerProperty = null;
		if (updateType == 0 || updateType == 2) { // 针对所有客服人员
			openCSCustomerProperty = new OpenCSCustomerProperty();
			openCSCustomerProperty.setAdminDescription(platformMetaSessionVO.getAdminDescription());
			openCSCustomerProperty.setCustomerDescription(platformMetaSessionVO.getCustomerDescription());
			openCSCustomerProperty.setCustomerSessionName(platformMetaSessionVO.getCustomerSessionName());
			openCSCustomerProperty.setCustomerSessionPortrait(platformMetaSessionVO.getCustomerSessionPortrait());
			openCSCustomerProperty.setCustomerSessionSubName(platformMetaSessionVO.getCustomerSessionSubName());
			//设置客服身份Id
			//别设置这个，设置这个新添加客服会收到所有的二级session
//			openCSCustomerProperty.setCustomers(platformMetaSessionVO.getCustomers());
			openCSCustomerProperty.setTotalCustomers(platformMetaSessionVO.getCustomers());
			if (platformMetaSessionVO.getCustomerNames() != null && platformMetaSessionVO.getCustomerNames().size() > 0) {
				List<CustomerNameTO> customerNames = platformMetaSessionVO.getCustomerNames().stream().map(customerName -> {
					CustomerNameTO customerNameTO = new CustomerNameTO();
					customerNameTO.setType(customerName.getType());
					customerNameTO.setName(customerName.getName());
					
					return customerNameTO;
				}).collect(Collectors.toList());
				
				openCSCustomerProperty.setCustomerNames(customerNames);
			}
		}

		if (updateType == 1) { // 针对所有客服人员
			Preconditions.checkArgument(platformMetaSessionVO.getCustomers() == null, "platformMetaSessionVO customers is not empty");
		}

		if (updateType == 3 || updateType == 4 || updateType == 5 || updateType == 6 || updateType == 7) {
			if (platformMetaSessionVO != null) {
				openCSCustomerProperty = new OpenCSCustomerProperty();
				openCSCustomerProperty.setCustomers(platformMetaSessionVO.getCustomers());
			}
		}

		OpenWXPlatformSessionUpdateArg openWXPlatformSessionUpdateArg = new OpenWXPlatformSessionUpdateArg();
		openWXPlatformSessionUpdateArg.setAppId(appId);
		openWXPlatformSessionUpdateArg.setEnterpriseAccount(ea);
		openWXPlatformSessionUpdateArg.setUpdateType(updateType);
		openWXPlatformSessionUpdateArg.setCustomerIds(openCSCustomerProperty.getCustomers());
		openWXPlatformSessionUpdateArg.setProperty(openCSCustomerProperty);

		try {
			openCustomService.updateWXPlatformSession(openWXPlatformSessionUpdateArg);
		} catch (Exception e) {
			logger.error("openCustomService.updateWXPlatformSession appid:{}, ea:{}, updatetype:{},  platformMetaSessionVO:{}, exception={}", appId, ea, updateType, platformMetaSessionVO, e);
			ret = false;
		}
		return ret;
	}

	@Override
	public CustomerSessionResult<Boolean> updatePlatformMetaSessionNew(String appId, String enterpriseAccount, int updateType, PlatformMetaSessionVO platformMetaSessionVO, Integer customerSessionType) {

		try {
			Preconditions.checkArgument(!Strings.isNullOrEmpty(appId), "appId is null or empty");
			Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount), "enterpriseAccount is null or empty");
			Preconditions.checkArgument(updateTypeSet.contains(updateType), "updateType is illegal");

			if (!Objects.isNull(customerSessionType) && CustomerSessionTypeEnum.wx.getType() == customerSessionType) {
				// 微客服的支持控制到具体的客服人员, 3 禁用 4 开启 5 隐藏 6 显示
				return new CustomerSessionResult<Boolean>(updateWxPlatformMetaSessionNew(appId, enterpriseAccount, updateType, platformMetaSessionVO));
			}

			if (!Objects.isNull(customerSessionType) && CustomerSessionTypeEnum.cross.getType() == customerSessionType) {
				// 互联服务号工作台处理
				return new CustomerSessionResult<Boolean>(updateCrossPlatformMetaSession(appId, enterpriseAccount, updateType, platformMetaSessionVO));
			}

			Preconditions.checkArgument(7 != updateType, "updateType is illegal");

			OpenCSCustomerProperty openCSCustomerProperty = null;
			OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();

			if (updateType == 0 || updateType == 2) {
				Preconditions.checkArgument(platformMetaSessionVO != null, "platformMetaSessionVO is illegal");

				openCSCustomerProperty = new OpenCSCustomerProperty();
				openCSCustomerProperty.setAdminDescription(platformMetaSessionVO.getAdminDescription());
				openCSCustomerProperty.setAdmins(platformMetaSessionVO.getAdmins());
				openCSCustomerProperty.setCustomerDescription(platformMetaSessionVO.getCustomerDescription());
				openCSCustomerProperty.setCustomers(platformMetaSessionVO.getCustomers());
				openCSCustomerProperty.setCustomerSessionName(platformMetaSessionVO.getCustomerSessionName());
				openCSCustomerProperty.setCustomerSessionPortrait(platformMetaSessionVO.getCustomerSessionPortrait());
				openCSCustomerProperty.setCustomerSessionSubName(platformMetaSessionVO.getCustomerSessionSubName());

			}

			openCSSessionUpdateArg.setUpdateType(updateType);
			openCSSessionUpdateArg.setProperty(openCSCustomerProperty);
			openCSSessionUpdateArg.setAppId(appId);
			openCSSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
			boolean upRet = true;
			try {
				/**
				 * updateType=0时，该接口把工作台的配置信息如名称，头像，描述等记下来。
				 * 如果传了客服列表或者管理员列表，那么列表中的人在手机上的一级session会创建。
				 * 对于服务工单(workbenchtype=7)，打开工作台时，管理员和客服列表都是空的，
				 * 只有在发服务工单消息时，才会触发手机端一级session的创建。
				 */
				openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
			} catch (Exception e) {
				logger.error("updateCustomerServicePlatformSession  exception openCSSessionUpdateArg={} exception={}", openCSSessionUpdateArg, e);
				upRet = false;
			}

			return new CustomerSessionResult<Boolean>(upRet);

		} catch (IllegalArgumentException ex) {
			logger.error("updatePlatformMetaSessionNew paramValidate exception appId={} enterpriseAccount={} updateType={} params={} exception={}", appId, enterpriseAccount, updateType, platformMetaSessionVO, ex);
			return new CustomerSessionResult<>(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			logger.error("updatePlatformMetaSessionNew catch exception appId={} enterpriseAccount={} updateType={} params={} exception={}, return system error ", appId, enterpriseAccount, updateType, platformMetaSessionVO, e);
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	private Boolean updateCrossPlatformMetaSession(String appId, String ea, int updateType, PlatformMetaSessionVO platformMetaSessionVO) {
		OpenCrossCSSessionUpdateArg crossCSSessionUpdateArg = new OpenCrossCSSessionUpdateArg();
		crossCSSessionUpdateArg.setAppId(appId);
		crossCSSessionUpdateArg.setEnterpriseAccount(ea);

		if (updateType == 0 || updateType == 2) {
			Preconditions.checkArgument(platformMetaSessionVO != null, "platformMetaSessionVO is illegal");
			OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
			openCSCustomerProperty.setAdminDescription(platformMetaSessionVO.getAdminDescription());
			openCSCustomerProperty.setAdmins(platformMetaSessionVO.getAdmins());
			openCSCustomerProperty.setCustomerDescription(platformMetaSessionVO.getCustomerDescription());
			openCSCustomerProperty.setCustomers(platformMetaSessionVO.getCustomers());
			openCSCustomerProperty.setCustomerSessionName(platformMetaSessionVO.getCustomerSessionName());
			openCSCustomerProperty.setCustomerSessionPortrait(platformMetaSessionVO.getCustomerSessionPortrait());
			openCSCustomerProperty.setCustomerSessionSubName(platformMetaSessionVO.getCustomerSessionSubName());

			crossCSSessionUpdateArg.setProperty(openCSCustomerProperty);
		}
		crossCSSessionUpdateArg.setUpdateType(updateType);
		try {
			openCrossCustomerService.updateCrossCustomerServicePlatformSession(crossCSSessionUpdateArg);
			return true;
		} catch (Exception e) {
			logger.error("openCustomService.updateWXPlatformSession appid:{}, ea:{}, updatetype:{},  platformMetaSessionVO:{}, exception={}", appId, ea, updateType, platformMetaSessionVO, e);
			return false;
		}

	}

	@Override
	public CustomerSessionResult<Void> sendWorkbenchMessage(WorkbenchMessageVO workbenchMessageVO) {
		try {
			logger.info("sendWorkbenchMessage workbenchMessageVO:{}",workbenchMessageVO);
			workbenchMessageVO.checkParams();

			if (CustomerSessionTypeEnum.multi.getType() == workbenchMessageVO.getSessionType()) {
				if (workbenchMessageVO.getWorkbenchType() == 7) { // 服务工单
																	// 这种session的信息是接收者私有的,其它类型的信息是角色共享的
					OpenSendUnsharedWorkBenchMessageArg arg = new OpenSendUnsharedWorkBenchMessageArg();
					arg.setEnterpriseAccount(workbenchMessageVO.getEnterpriseAccount());
					arg.setAppId(workbenchMessageVO.getAppId());
					arg.setWorkbenchType(workbenchMessageVO.getWorkbenchType());
					arg.setLastSummary(workbenchMessageVO.getLastSummary());
					arg.setReceiverIds(workbenchMessageVO.getCustomServiceRepresentiveIDs());
					openCustomService.sendUnsharedWorkbenchCSMessage(arg);
				} else {
					OpenSendWorkBenchMessageArg openSendWorkBenchMessageArg = new OpenSendWorkBenchMessageArg();
					openSendWorkBenchMessageArg.setEnterpriseAccount(workbenchMessageVO.getEnterpriseAccount());
					
					openSendWorkBenchMessageArg.setAppId(workbenchMessageVO.getAppId());
					openSendWorkBenchMessageArg.setWorkbenchType(workbenchMessageVO.getWorkbenchType());
					openSendWorkBenchMessageArg.setSenderId(workbenchMessageVO.getSenderId());
					openSendWorkBenchMessageArg.setLastSummary(workbenchMessageVO.getLastSummary());
					openCustomService.sendWorkbenchCSMessage(openSendWorkBenchMessageArg);
				}
			} else if (CustomerSessionTypeEnum.cross.getType() == workbenchMessageVO.getSessionType()) {
				OpenCrossSendWorkBenchMessageArg arg = new OpenCrossSendWorkBenchMessageArg();
				arg.setAppId(workbenchMessageVO.getAppId());
				if(StringUtils.isNotEmpty(workbenchMessageVO.getUpEnterpriseAccount())){
					arg.setEnterpriseAccount(workbenchMessageVO.getUpEnterpriseAccount());
				}else{
					arg.setEnterpriseAccount(workbenchMessageVO.getEnterpriseAccount());
				}
				arg.setLastSummary(workbenchMessageVO.getLastSummary());
				arg.setSenderId(workbenchMessageVO.getSenderId());
				arg.setWorkbenchType(workbenchMessageVO.getWorkbenchType());
				openCrossCustomerService.sendCrossWorkbenchCSMessage(arg);
			} else {
				OpenSendWXWorkBenchMessageArg openSendWXWorkBenchMessageArg = new OpenSendWXWorkBenchMessageArg();
				openSendWXWorkBenchMessageArg.setEnterpriseAccount(workbenchMessageVO.getEnterpriseAccount());
				openSendWXWorkBenchMessageArg.setAppId(workbenchMessageVO.getAppId());
				openSendWXWorkBenchMessageArg.setWorkbenchType(workbenchMessageVO.getWorkbenchType());
				openSendWXWorkBenchMessageArg.setOpenSenderId(workbenchMessageVO.getSenderOpenId());
				openSendWXWorkBenchMessageArg.setLastSummary(workbenchMessageVO.getLastSummary());
				openSendWXWorkBenchMessageArg.setReceiverIds(workbenchMessageVO.getCustomServiceRepresentiveIDs());
				openCustomService.sendWXWorkbenchCSMessage(openSendWXWorkBenchMessageArg);
			}

			return new CustomerSessionResult<Void>();

		} catch (IllegalArgumentException ex) {
			logger.error("sendWorkbenchMessage paramValidate exception params={} exception={}", workbenchMessageVO, ex);
			return new CustomerSessionResult<>(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	private CustomerSessionResult<Boolean> updateWxWorkbenchSession(WorkbenchSessionVO workbenchSessionVO) {
		OpenWXPlatformSessionUpdateArg openWXPlatformSessionUpdateArg = new OpenWXPlatformSessionUpdateArg();

		OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
		WorkbenchEntityVo workbenchEntityVo = new WorkbenchEntityVo();
		workbenchEntityVo.setSessionIcon(workbenchSessionVO.getSessionIcon());
		workbenchEntityVo.setSessionName(workbenchSessionVO.getSessionName());
		workbenchEntityVo.setWorkType(workbenchSessionVO.getWorkbenchType());
		workbenchEntityVo.setUri(workbenchSessionVO.getUrl());
		List<WorkbenchEntityVo> WorkbenchList = Lists.newArrayList(workbenchEntityVo);
		openCSCustomerProperty.setWorkbenchEntities(WorkbenchList);

		openWXPlatformSessionUpdateArg.setAppId(workbenchSessionVO.getAppId());
		openWXPlatformSessionUpdateArg.setEnterpriseAccount(workbenchSessionVO.getEnterpriseAccount());
		openWXPlatformSessionUpdateArg.setUpdateType(2);
		openWXPlatformSessionUpdateArg.setProperty(openCSCustomerProperty);

		boolean upRet = true;
		try {
			openCustomService.updateWXPlatformSession(openWXPlatformSessionUpdateArg);
		} catch (Exception e) {
			upRet = false;
		}
		return new CustomerSessionResult<Boolean>(upRet);
	}

	private CustomerSessionResult<Boolean> updateCrossWorkbenchSession(WorkbenchSessionVO workbenchSessionVO) {
		OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();

		WorkbenchEntityVo workbenchEntityVo = new WorkbenchEntityVo();
		workbenchEntityVo.setSessionIcon(workbenchSessionVO.getSessionIcon());
		workbenchEntityVo.setSessionName(workbenchSessionVO.getSessionName());
		workbenchEntityVo.setWorkType(workbenchSessionVO.getWorkbenchType());
		workbenchEntityVo.setUri(workbenchSessionVO.getUrl());

		List<WorkbenchEntityVo> WorkbenchList = Lists.newArrayList(workbenchEntityVo);
		openCSCustomerProperty.setWorkbenchEntities(WorkbenchList);

		// boolean upRet =
		// openCustomService.updateCustomerServicePlatformSession(workbenchSessionVO.getEnterpriseAccount(),
		// workbenchSessionVO.getAppId(), (byte)2, openCSCustomerProperty);
		OpenCrossCSSessionUpdateArg arg = new OpenCrossCSSessionUpdateArg();
		if(StringUtils.isNotEmpty(workbenchSessionVO.getUpEnterpriseAccount())){
			arg.setEnterpriseAccount(workbenchSessionVO.getUpEnterpriseAccount());
		}else{
			arg.setEnterpriseAccount(workbenchSessionVO.getEnterpriseAccount());
		}
		
		arg.setAppId(workbenchSessionVO.getAppId());
		arg.setUpdateType(2);
		arg.setProperty(openCSCustomerProperty);

		logger.info("updateCrossWorkbenchSession  workbenchSessionVO={} sessiontype={}", workbenchSessionVO, workbenchSessionVO.getSessionType());
		boolean upRet = true;
		try {
			openCrossCustomerService.updateCrossCustomerServicePlatformSession(arg);
		} catch (Exception e) {
			upRet = false;
		}
		return new CustomerSessionResult<Boolean>(upRet);

	}

	@Override
	public CustomerSessionResult<Boolean> updateWorkbenchSession(WorkbenchSessionVO workbenchSessionVO) {
		try {
			logger.info("updateCrossWorkbenchSession workbenchSessionVO:{}",workbenchSessionVO);
			workbenchSessionVO.checkParams();

			if (CustomerSessionTypeEnum.wx.getType() == workbenchSessionVO.getSessionType()) {
				return updateWxWorkbenchSession(workbenchSessionVO);
			}

			if (CustomerSessionTypeEnum.cross.getType() == workbenchSessionVO.getSessionType()) {
				return updateCrossWorkbenchSession(workbenchSessionVO);
			}
			OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();

			WorkbenchEntityVo workbenchEntityVo = new WorkbenchEntityVo();
			workbenchEntityVo.setSessionIcon(workbenchSessionVO.getSessionIcon());
			workbenchEntityVo.setSessionName(workbenchSessionVO.getSessionName());
			workbenchEntityVo.setWorkType(workbenchSessionVO.getWorkbenchType());
			workbenchEntityVo.setUri(workbenchSessionVO.getUrl());

			List<WorkbenchEntityVo> WorkbenchList = Lists.newArrayList(workbenchEntityVo);
			openCSCustomerProperty.setWorkbenchEntities(WorkbenchList);

			// boolean upRet =
			// openCustomService.updateCustomerServicePlatformSession(workbenchSessionVO.getEnterpriseAccount(),
			// workbenchSessionVO.getAppId(), (byte)2, openCSCustomerProperty);
			OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
			openCSSessionUpdateArg.setEnterpriseAccount(workbenchSessionVO.getEnterpriseAccount());
			openCSSessionUpdateArg.setAppId(workbenchSessionVO.getAppId());
			openCSSessionUpdateArg.setUpdateType(2);
			openCSSessionUpdateArg.setProperty(openCSCustomerProperty);

			logger.info("updateWorkbenchSession  workbenchSessionVO={} sessiontype={}", workbenchSessionVO, workbenchSessionVO.getSessionType());
			boolean upRet = true;
			try {
				openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
			} catch (Exception e) {
				upRet = false;
			}
			return new CustomerSessionResult<Boolean>(upRet);

		} catch (IllegalArgumentException ex) {
			logger.error("updateWorkbenchSession paramValidate exception params={} exception={}", workbenchSessionVO, ex);
			return new CustomerSessionResult<>(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	private CustomerSessionResult<Void> setWxAppServiceStatus(String enterpriseAccount, String appId, boolean status, int switchRet) {
		/** 隐藏或者显示session */
		OpenWXPlatformSessionUpdateArg openWXPlatformSessionUpdateArg = new OpenWXPlatformSessionUpdateArg();
		openWXPlatformSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
		openWXPlatformSessionUpdateArg.setAppId(appId);
		openWXPlatformSessionUpdateArg.setUpdateType(status ? (byte) 6 : (byte) 5);
		openWXPlatformSessionUpdateArg.setCustomerIds(null);
		openWXPlatformSessionUpdateArg.setProperty(null);
		openCustomService.updateWXPlatformSession(openWXPlatformSessionUpdateArg);

		/** 禁用或者开启session */
		/**
		 * if (switchRet == 1) {
		 * openWXPlatformSessionUpdateArg.setUpdateType(status ? (byte) 4 :
		 * (byte) 3);
		 * 
		 * logger.info("setWxAppServiceStatus
		 * openCustomService.updateWXPlatformSession appid={}， ea={},
		 * updatetype={}, params={} ",
		 * openWXPlatformSessionUpdateArg.getAppId(),
		 * openWXPlatformSessionUpdateArg.getEnterpriseAccount(),
		 * openWXPlatformSessionUpdateArg.getUpdateType(),
		 * openWXPlatformSessionUpdateArg);
		 * openCustomService.updateWXPlatformSession(openWXPlatformSessionUpdateArg);
		 * }
		 */

		return new CustomerSessionResult<Void>();
	}

	public CustomerSessionResult<Void> setAppServiceStatus(String enterpriseAccount, String appId, boolean status, int sessionType) {
		logger.info("setAppServiceStatus param--appid:{},usable:{} ", appId, status);
		try {
			Preconditions.checkArgument(CustomerSessionTypeEnum.multi.getType() == sessionType || CustomerSessionTypeEnum.wx.getType() == sessionType || CustomerSessionTypeEnum.cross.getType() == sessionType);

			Preconditions.checkArgument(!Strings.isNullOrEmpty(appId), "appId is null or empty");

			Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount), "enterpriseAccount is null or empty");

			// 先查询客服开关是否打开了
			int switchRet = msgAutoReplyManager.getCustomServiceReplySwitch(enterpriseAccount, appId);

			if (2 == sessionType) {
				OpenOSS1DefinitionArg openOSS1DefinitionArg = new OpenOSS1DefinitionArg();
				openOSS1DefinitionArg.setAppId(appId);
				openOSS1DefinitionArg.setUpstreamEnterprise(enterpriseAccount);

				// 从互联平台rest接口获取
				logger.info("setAppServiceStatus start. onCloud[{}], ea[{}], appId[{}]", true, enterpriseAccount, appId);
				HeaderObj headerObj = HeaderObj.newInstance(appId,  eIEAConverter.enterpriseAccountToId(enterpriseAccount),  null, null);
				ListDownstreamFriendEnterprisesArg arg = new ListDownstreamFriendEnterprisesArg(enterpriseAccount);
				RestResult<List<EnterpriseSimpleVo>> restResult = enterpriseRelationService.listDownstreamFriendEnterprises(headerObj, arg);
				if (!restResult.isSuccess()) {
					logger.error("setAppServiceStatus error. onCloud:{}, ea:{}, appId:{}, restResult:{}", true, enterpriseAccount, appId, restResult);
					return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
				}
				List<String> downEaList = restResult.getData().stream().map(EnterpriseSimpleVo::getEnterpriseAccount).collect(Collectors.toList());
				downEaList.add(enterpriseAccount);
				logger.info("setAppServiceStatus success. onCloud:{}, ea:{}, appId:{}, restResult:{}", true, enterpriseAccount, appId, restResult);

				openOSS1DefinitionArg.setDownstreamEnterpriseList(downEaList);
				openOSS1DefinitionArg.setDisable(!status);
				//服务号状态设置如open_FSAID_11e1a403#1|57255
				openCrossDefinitionService.updateOSS1Definition(openOSS1DefinitionArg);
				//工作台状态设置如open_FSAID_11e1a403
				openOSS1DefinitionArg.setUpstreamEnterprise(null);
				openCrossDefinitionService.updateOSS1Definition(openOSS1DefinitionArg);
				
				/** 隐藏或者显示 */
				int updateType = status ? 6 : 5;

				OpenCrossCSSessionUpdateArg openCrossCSSessionUpdateArg = new OpenCrossCSSessionUpdateArg();
				openCrossCSSessionUpdateArg.setAppId(appId);
				openCrossCSSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
				openCrossCSSessionUpdateArg.setProperty(null);
				openCrossCSSessionUpdateArg.setUpdateType(updateType);
				openCrossCustomerService.updateCrossCustomerServicePlatformSession(openCrossCSSessionUpdateArg);

				if (switchRet == 1) {
					/** 禁用或者启用 */
					updateType = status ? 4 : 3;
					openCrossCSSessionUpdateArg.setUpdateType(updateType);
					openCrossCustomerService.updateCrossCustomerServicePlatformSession(openCrossCSSessionUpdateArg);
				}
				return new CustomerSessionResult<Void>();
			}

			/** 企信后台给的启用停用服务号接口 */
			openSessionService.updateAppUsable(appId, status);

			OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();

			logger.info("setAppServiceStatus ea={}, appid={}，status={}, sesstiontype={}, switchRet={}", enterpriseAccount, appId, status, sessionType, switchRet);
			if (CustomerSessionTypeEnum.wx.getType() == sessionType) {
				return setWxAppServiceStatus(enterpriseAccount, appId, status, switchRet);
			}

			/** 隐藏或者显示session */
			openCSSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
			openCSSessionUpdateArg.setAppId(appId);
			openCSSessionUpdateArg.setUpdateType(status ? (byte) 6 : (byte) 5);
			openCSSessionUpdateArg.setProperty(null);
			openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);

			if (switchRet == 1) {
				/** 禁用或者开启session */
				openCSSessionUpdateArg.setUpdateType(status ? (byte) 4 : (byte) 3);
				openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
			}

			return new CustomerSessionResult<Void>();

		} catch (IllegalArgumentException ex) {
			logger.error("setAppServiceStatus paramValidate exception enterpriseAccount={} appId={} exception={}", enterpriseAccount, appId, ex);
			return new CustomerSessionResult<>(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			logger.error("setAppServiceStatus error", e);
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	public CustomerSessionResult<Void> setAppServiceStatus(String enterpriseAccount, String appId, boolean status) {
		return setAppServiceStatus(enterpriseAccount, appId, status, CustomerSessionTypeEnum.multi.getType());
	}

	@Override
	public CustomerSessionResult<WorkbenchSessionInfoVO> getWorkbenchSessionInfo(WorkbenchSessionQueryVO workbenchSessionQueryVO) {
		logger.info("getWorkbenchSessionInfo param:{}", workbenchSessionQueryVO);
		try {
			workbenchSessionQueryVO.checkParams();
			Session session = null;
			if (CustomerSessionTypeEnum.cross.getType() == workbenchSessionQueryVO.getSessionType()) {
				OpenCrossGetWBSessionInfoArg arg = new OpenCrossGetWBSessionInfoArg();
				arg.setAppId(workbenchSessionQueryVO.getAppId());
				arg.setEnterpriseAccount(workbenchSessionQueryVO.getEnterpriseAccount());
				arg.setWorkbenchType(workbenchSessionQueryVO.getWorkbenchType());
				session = openCrossCustomerService.getCrossWorkbenchSessionInfo(arg);
			}else{
				OpenGetWBSessionInfoArg arg = new OpenGetWBSessionInfoArg();
				arg.setAppId(workbenchSessionQueryVO.getAppId());
				arg.setEnterpriseAccount(workbenchSessionQueryVO.getEnterpriseAccount());
				arg.setWorkbenchType(workbenchSessionQueryVO.getWorkbenchType());
				session = openCustomService.getWorkbenchSessionInfo(arg);
			}

			if (session == null) {
				return new CustomerSessionResult<>(MsgCodeEnum.SESSION_CUSTOMERSERVICE_NOT_EXIST);
			}

			WorkbenchSessionInfoVO workbenchSessionInfoVO = new WorkbenchSessionInfoVO();
			workbenchSessionInfoVO.setSessionId(session.getSessionId());
			workbenchSessionInfoVO.setNotReadCount(session.getNotReadCount());

			return new CustomerSessionResult<>(workbenchSessionInfoVO);

		} catch (Exception e) {
			logger.error("getWorkbenchSessionInfo error", e);
			return new CustomerSessionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public CustomerSessionResult<WorkbenchSessionInfoVO> getWorkbenchSessionInfo(String enterpriseAccount, String appId, int workbenchType) {
		WorkbenchSessionQueryVO workbenchSessionQueryVO = new WorkbenchSessionQueryVO();
		workbenchSessionQueryVO.setAppId(appId);
		workbenchSessionQueryVO.setEnterpriseAccount(enterpriseAccount);
		workbenchSessionQueryVO.setWorkbenchType(workbenchType);
		return getWorkbenchSessionInfo(workbenchSessionQueryVO);
	}

	@Override
	public CustomerSessionResult<Void> deleteWxCustomSession(String appID, String ea, List<Integer> useIDs) {
		CustomerSessionResult<Void> customerSessionResult = new CustomerSessionResult<Void>();
		return customerSessionResult;
	}
	
	@Override
	public CustomerSessionResult<Void> updateWxSecondSession(UpdateWxSecondSessionVO updateWxSecondSessionVO) {
		
		try {
			OpenWXUpdateCustomersArg  openWXUpdateCustomersArg = new OpenWXUpdateCustomersArg();
			
			openWXUpdateCustomersArg.setAppId(updateWxSecondSessionVO.getAppId());
			openWXUpdateCustomersArg.setEnterpriseAccount(updateWxSecondSessionVO.getEnterpriseAccount());
			openWXUpdateCustomersArg.setSenderName(updateWxSecondSessionVO.getSenderName());
			openWXUpdateCustomersArg.setSenderOpenId(updateWxSecondSessionVO.getSenderOpenId());
			openWXUpdateCustomersArg.setSenderPortrait(updateWxSecondSessionVO.getSenderPortrait());
			
			if (updateWxSecondSessionVO.getCustomerMembers() != null && updateWxSecondSessionVO.getCustomerMembers().size() > 0) {
				List<CustomerMembersTO> customerMembers = updateWxSecondSessionVO.getCustomerMembers().stream().map(member -> {
					CustomerMembersTO customerMembersTO = new CustomerMembersTO();
					customerMembersTO.setType(member.getType());
					customerMembersTO.setMembers(member.getMembers());
					return customerMembersTO;
				}).collect(Collectors.toList());
				
				openWXUpdateCustomersArg.setCustomerMembers(customerMembers);
			}
			
			if (updateWxSecondSessionVO.getExpertMembers() != null && updateWxSecondSessionVO.getExpertMembers().size() > 0) {
				List<CustomerMembersTO> expertMembers = updateWxSecondSessionVO.getExpertMembers().stream().map(member -> {
					CustomerMembersTO customerMembersTO = new CustomerMembersTO();
					customerMembersTO.setType(member.getType());
					customerMembersTO.setMembers(member.getMembers());
					return customerMembersTO;
				}).collect(Collectors.toList());
				
				openWXUpdateCustomersArg.setExpertMembers(expertMembers);
			}
					
			openCustomService.updateWXCustomers(openWXUpdateCustomersArg);

			return new CustomerSessionResult<Void>();
		} catch (Exception e) {
			logger.error("updateWxSecondSession exception arg={}", updateWxSecondSessionVO, e);
			
			return new CustomerSessionResult<Void>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}
	
	@Override
	public CustomerSessionResult<Void> updateWxSecondSessionName(UpdateWxSecondSessionNameVO updateWxSecondSessionNameVO) {
		
		try {
			OpenWXUpdateSessionNameArg arg = new OpenWXUpdateSessionNameArg();
			arg.setAppId(updateWxSecondSessionNameVO.getAppId());
			arg.setEnterpriseAccount(updateWxSecondSessionNameVO.getEnterpriseAccount());
			arg.setSenderOpenId(updateWxSecondSessionNameVO.getSenderOpenId());
			arg.setSenderName(updateWxSecondSessionNameVO.getSenderName());

			openCustomService.updateWXSessionName(arg);
			return new CustomerSessionResult<Void>();
		} catch (Exception e) {
			logger.error("updateWxSecondSessionName exception arg={}", updateWxSecondSessionNameVO, e);
			
			return new CustomerSessionResult<Void>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}
	
    @Override
    public CustomerSessionResult<Void> updateWXCustomerExtraDataMap(UpdateWXCustomerExtraDataMapArg updateWXCustomerExtraDataMapArg) {
		try {
	    	OpenUpdateWXCustomerExtraDataMapArg arg = new OpenUpdateWXCustomerExtraDataMapArg();
	    	arg.setAppId(updateWXCustomerExtraDataMapArg.getAppId());
	    	arg.setEnterpriseAccount(updateWXCustomerExtraDataMapArg.getEnterpriseAccount());
	    	arg.setSenderOpenId(updateWXCustomerExtraDataMapArg.getSenderOpenId());
	    	arg.setExtraDataMap(updateWXCustomerExtraDataMapArg.getExtraDataMap());
	    	
	    	
	    	openCustomService.updateWXCustomerExtraDataMap(arg);
			return new CustomerSessionResult<Void>();
		} catch (Exception e) {
			logger.error("updateWXCustomerExtraDataMap exception arg={}", updateWXCustomerExtraDataMapArg, e);
			
			return new CustomerSessionResult<Void>(MsgCodeEnum.SYSTEM_ERROR);
		}
    }
 
    @Override
    public CustomerSessionResult<Void> updateWXPlatformExtraDataMap(UpdateWXPlatformExtraDataMapArg updateWXPlatformExtraDataMapArg) {
		try {
			OpenUpdateWXPlatformExtraDataMapArg arg = new OpenUpdateWXPlatformExtraDataMapArg ();
	    	arg.setAppId(updateWXPlatformExtraDataMapArg.getAppId());
	    	arg.setEnterpriseAccount(updateWXPlatformExtraDataMapArg.getEnterpriseAccount());
	    	arg.setExtraDataMap(updateWXPlatformExtraDataMapArg.getExtraDataMap());
	    	arg.setCustomerIds(updateWXPlatformExtraDataMapArg.getCustomerIds());
	    	
	    	openCustomService.updateWXPlatformExtraDataMap(arg);
			return new CustomerSessionResult<Void>();
		} catch (Exception e) {
			logger.error("updateWXPlatformExtraDataMap exception arg={}", updateWXPlatformExtraDataMapArg, e);
			
			return new CustomerSessionResult<Void>(MsgCodeEnum.SYSTEM_ERROR);
		}
    }
}
