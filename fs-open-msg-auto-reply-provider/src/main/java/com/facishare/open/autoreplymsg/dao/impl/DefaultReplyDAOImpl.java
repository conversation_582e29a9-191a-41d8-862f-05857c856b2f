package com.facishare.open.autoreplymsg.dao.impl;

import com.facishare.open.autoreplymsg.dao.DefaultReplyDAO;
import com.facishare.open.autoreplymsg.dao.base.AutoReplyMsgBaseDAO;
import com.facishare.open.autoreplymsg.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by fengyh on 2016/3/5.
 */

@Service
public class DefaultReplyDAOImpl extends AutoReplyMsgBaseDAO<DefaultReplyDO> implements DefaultReplyDAO {

    private static final Logger logger = LoggerFactory.getLogger(DefaultReplyDAOImpl.class);

    @Override
    public long createDefaultReply(DefaultReplyDO defaultReplyDO) {

        logger.info("createDefaultReply param--defaultReplyDO:{} ",  defaultReplyDO);
        int affectedRows = this.save("createDefaultReply", defaultReplyDO);
        logger.info("createDefaultReply  defaultReplyDO:{}, affectedRows:{} ", defaultReplyDO, affectedRows);

        if (affectedRows > 0) {
            return defaultReplyDO.getReplyMsgID();
        } else {
            return -1;
        }
    }

    @Override
    public int deleteDefaultReply(long replyMsgID, String enterpriseAccount, String appID) {
        logger.info("deleteDefaultReply param--appid:{},enterpriseAccount:{}, replyMsgID:{}",
                appID, enterpriseAccount, replyMsgID);

        DefaultReplyDO replyDO = new DefaultReplyDO();
        replyDO.setReplyMsgID(replyMsgID);
        replyDO.setEnterpriseAccount(enterpriseAccount);
        replyDO.setAppID(appID);
        int affectedRows =this.delete("deleteDefaultReply", replyDO);

        logger.info("deleteDefaultReply replyDO:{}, affectedRows:{} ", replyDO, affectedRows);
        return (1 == affectedRows ? 0 : 1);
    }

    @Override
    public int updateDefaultReply(DefaultReplyDO defaultReplyDO) {
        logger.info("updateDefaultReply param--defaultReplyDO:{} ",  defaultReplyDO);
        int affectedRows = this.update("updateDefaultReply", defaultReplyDO);
        logger.info("updateDefaultReply defaultReplyDO:{},  affectedRows:{} ", defaultReplyDO, affectedRows);
        return (1 == affectedRows ? 0 : 1);
    }

    @Override
    public DefaultReplyDO queryDefaultReply(String enterpriseAccount, String appID) {
        logger.info("queryDefaultReply param--appid:{},enterpriseAccount:{}",
                appID, enterpriseAccount);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("enterpriseAccount", enterpriseAccount);
        map.put("appID", appID);
        DefaultReplyDO replyDORet = this.getUnique("queryDefaultReply", map);

        logger.info("queryDefaultReply map:{}, ret:{}", map, replyDORet);
        return replyDORet;
    }
}
