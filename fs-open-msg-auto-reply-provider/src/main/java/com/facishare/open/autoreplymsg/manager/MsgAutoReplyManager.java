package com.facishare.open.autoreplymsg.manager;

import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.msg.result.MsgBaseResult;

import java.util.List;

/**
 * Created by fengyh on 2016/3/7.
 */
public interface MsgAutoReplyManager {

    /**
     * 查询自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @return : ReplySwitchStatusEnum
     */
    public int queryAutoReplySwitch(String enterpriseAccount, String appID);

    /**
     * 设置自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @param status            :
     * @return : MsgBaseResult
     */
    public MsgBaseResult setAutoReplySwitch(String enterpriseAccount, String appID, int status);

    /**
     * 获取自动回复的内容。自动回复的内容可能是以下形式：
     * （1）一段文本。
     * (2)一个图文消息ID
     * 用户在服务号中上行的每条消息，都要调用这个接口检查是否要自动回复。
     * 如果自动回复开关没打开，回复内容为空。
     * 如果打开了，分两种情况：
     * （1）关键字匹配成功，用关键字回复。
     * （2）关键字匹配失败，用默认回复。
     *
     * @param fsUserAccount ： 用户的纷享账号，格式为  E.企业账号.员工ID
     * @param appID         : 开放平台分配的应用ID号
     * @param userInput     : 用户的输入
     * @return : MsgBaseResult
     */
    public GetAutoReplyResult getAutoReply(String fsUserAccount, String appID, String userInput);
    
    /**
     * 获取跨企业的自动回复内容
     * @param crossEa
     * @param fsUserAccount
     * @param appID
     * @param userInput
     * @return
     */
    public GetAutoReplyResult getAutoReplyByEa(String ea,String fsUserAccount, String appID, String userInput);


    /**
     * 获取一个用户的最后自动回复时间。
     *
     * @param fsUserAccount ：员工账号，  E.企业账号.员工ID 这种格式
     * @param appID             : 开放平台分配的应用ID号
     *
     * @return : 返回0表示该用户没有被自动回复过. 返回的时间单位是秒, 使用unix时间戳。
     */
    public long getLastAutoReplyTime(String appID, String fsUserAccount);

    /**
     * 设置一个用户的最后自动回复时间。
     *
     * @param fsUserAccount ：员工账号，  E.企业账号.员工ID 这种格式
     * @param appID             : 开放平台分配的应用ID号
     * @param lastReplyTime: 单位是秒, 使用unix时间戳。
     *
     * @return : 0-成功， 其它-是吧。
     */
    public int setLastAutoReplyTime(String appID, String fsUserAccount, long lastReplyTime);

    /**
     * 查询客服开关
     * */
    public int getCustomServiceReplySwitch(String enterpriseAccount, String appID);

    /**
     * 设置客服开关
     * */
    public int setCustomServiceReplySwitch(String enterpriseAccount, String appID, int status);

    /**
     * 设置客服开关New
     * */
    @Deprecated
    public int setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status);


    /**
     * 设置客服开关New, 支持多客服+微客服
     * */
    public int setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status,  Integer customerSessionType);

    /**
     * 获取设置了客服开关的应用列表
     * */
    public List<String> getCustomServiceAppListBySwitch(String enterpriseAccount, int status);

}
