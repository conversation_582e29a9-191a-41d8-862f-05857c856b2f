package com.facishare.open.autoreplymsg.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.facishare.open.autoreplymsg.manager.impl.MsgAutoReplyManagerImpl;
import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceAppListResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceSwitchResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.msg.common.constants.CustomerSessionTypeEnum;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.open.oauth.model.enums.OauthCodeEnum;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.service.AppService;
import com.facishare.open.oauth.service.EaAuthService;
import com.facishare.qixin.api.model.open.arg.OpenCSCustomerProperty;
import com.facishare.qixin.api.model.open.arg.OpenCSSessionUpdateArg;
import com.facishare.qixin.api.open.OpenCustomService;
import com.google.common.base.Splitter;

/**
 * Created by fengyh on 2016/3/5.
 */

import org.apache.ibatis.executor.statement.StatementHandler;

//@Service("msgAutoReplyService")
@Service
public class MsgAutoReplyServiceImpl implements MsgAutoReplyService {

    private static final Logger logger = LoggerFactory.getLogger(MsgAutoReplyServiceImpl.class);

    @Autowired
    private MsgAutoReplyManagerImpl msgAutoReplyManagerImpl;

    @Autowired
    private OpenCustomService openCustomService;

    @Autowired
    private AppService appService;

    @Autowired
    private EaAuthService eaAuthService;

    @PostConstruct
    private void init() {
        /**
        IConfigFactory factory = ConfigFactory.getInstance();
        IChangeableConfig config = factory.getConfig("gray-rel-message-send", iConfig -> {
            String grayStatus = iConfig.get("GRAY.STATUS", "on");

            if(grayStatus.equals("on")) {
                gray_control_switch = 1;
            } else {
                gray_control_switch = 0;
            }
            logger.info("reload  cms gray_control_switch:{} ", gray_control_switch);
        });

        String grayStatus = config.get("GRAY.STATUS", "on");
        if(grayStatus.equals("on")) {
            gray_control_switch = 1;
        } else {
            gray_control_switch = 0;
        }
        logger.info("get cms gray_control_switch:{} ", gray_control_switch);
        */
    }

    /**
     * 查询自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @return : ReplySwitchStatusEnum
     */
    @Override
    public int queryAutoReplySwitch(String enterpriseAccount, String appID) {
        logger.info("queryAutoReplySwitch param--appid:{},enterpriseAccount:{} ",
                appID, enterpriseAccount);
        int ret = msgAutoReplyManagerImpl.queryAutoReplySwitch(enterpriseAccount, appID);

        logger.info("queryAutoReplySwitch ret--ReplySwitchStatusEnum:{} ", ret);
        return ret;
    }

    /**
     * 设置自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @param status            :
     * @return : MsgBaseResult
     */
    @Override
    public MsgBaseResult setAutoReplySwitch(String enterpriseAccount, String appID, int status) {
        logger.info("setAutoReplySwitch param--appid:{},enterpriseAccount:{}, status:{} ",
                appID, enterpriseAccount, status);
        MsgBaseResult ret = msgAutoReplyManagerImpl.setAutoReplySwitch(enterpriseAccount, appID, status);
        logger.info("setAutoReplySwitch ret--MsgBaseResult:{} ", ret);
        return ret;
    }

    /**
     * 获取自动回复的内容。自动回复的内容可能是以下形式：
     * （1）一段文本。
     * (2)一个图文消息ID
     * 用户在服务号中上行的每条消息，都要调用这个接口检查是否要自动回复。
     * 如果自动回复开关没打开，回复内容为空。
     * 如果打开了，分两种情况：
     * （1）关键字匹配成功，用关键字回复。
     * （2）关键字匹配失败，用默认回复。
     *
     * @param fsUserAccount ： 用户的纷享账号，格式为  E.企业账号.员工ID
     * @param appID         : 开放平台分配的应用ID号
     * @param userInput     : 用户的输入
     * @return : MsgBaseResult
     */
    @Override
    public GetAutoReplyResult getAutoReply(String fsUserAccount, String appID, String userInput) {
        logger.info("getAutoReply param--appid:{},fsUserAccount:{}, userInput:{}  ",
                appID, fsUserAccount, userInput);

        GetAutoReplyResult getAutoReplyResult = msgAutoReplyManagerImpl.getAutoReply(fsUserAccount, appID, userInput);
        logger.info("getAutoReply ret--GetAutoReplyResult:{} ", getAutoReplyResult);
        return getAutoReplyResult;
    }

    @Override
	public GetAutoReplyResult getCrossAutoReply(String upEnterpriseAccount, String fsUserAccount, String appID,
			String userInput) {
    	logger.info("getCrossAutoReply param--upEnterpriseAccount:{},appid:{},fsUserAccount:{}, userInput:{}  ",upEnterpriseAccount,appID, fsUserAccount, userInput);

        GetAutoReplyResult getAutoReplyResult = msgAutoReplyManagerImpl.getAutoReplyByEa(upEnterpriseAccount, fsUserAccount, appID, userInput);
        logger.info("getCrossAutoReply ret--GetAutoReplyResult:{} ", getAutoReplyResult);
        return getAutoReplyResult;
	}

	/**
     * 查询 移动客服 开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @return : GetCustomServiceSwitchResult
     */
    public GetCustomServiceSwitchResult queryCustomServiceReplySwitch(String enterpriseAccount, String appID) {
        logger.info("queryCustomServiceReplySwitch param--appid:{},enterpriseAccount:{} ",
                appID, enterpriseAccount);
        GetCustomServiceSwitchResult getCustomServiceSwitchResult = new GetCustomServiceSwitchResult();
        int switchState = msgAutoReplyManagerImpl.getCustomServiceReplySwitch(enterpriseAccount, appID);
        logger.info("queryCustomServiceReplySwitch ret--result:{} ", switchState);
        getCustomServiceSwitchResult.setReplySwitch(switchState);

        if (switchState < 0) {
                getCustomServiceSwitchResult.setErrCode(-2);
                getCustomServiceSwitchResult.setErrorMsg("system error");
                logger.error("queryCustomServiceReplySwitch SQL ERROR ret--result:{} ", switchState);
        }
        return getCustomServiceSwitchResult;
    }

    /**
     *设置 移动客服 开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param status: 0： 禁用  1：启用
     * @return : MsgBaseResult
     */
    public MsgBaseResult setCustomServiceReplySwitch(String enterpriseAccount, String appID, int status) {
        logger.info("setCustomServiceReplySwitch param--appid:{},enterpriseAccount:{} ",
                appID, enterpriseAccount);
        MsgBaseResult result = new MsgBaseResult();
        try {
            int ret = msgAutoReplyManagerImpl.setCustomServiceReplySwitch(enterpriseAccount, appID, status);
            if (0 != ret) {
                result.setErrCode(-2);
                result.setErrorMsg("system error");
            }
            
            logger.info("setCustomServiceReplySwitch ret--result:{} ", result);
        } catch (Exception e) {
            result = new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
        }
        return result;
    }
    
    /**
     *设置 移动客服 开关New
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param status: 0： 禁用  1：启用
     * @return : MsgBaseResult
     */
    @Override
    public MsgBaseResult setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status) {
        return setCustomServiceReplySwitchWithType(enterpriseAccount, appID, status, CustomerSessionTypeEnum.multi.getType());
    }

    @Override
    public MsgBaseResult setCustomServiceReplySwitchWithType(String enterpriseAccount, String appID, int status, Integer customerSessionType) {
        logger.info("setCustomServiceReplySwitchNew param--appid:{},enterpriseAccount:{} ",
                appID, enterpriseAccount);
        MsgBaseResult result = new MsgBaseResult();
        try {
            int ret = msgAutoReplyManagerImpl.setCustomServiceReplySwitchNew(enterpriseAccount, appID, status, customerSessionType);
            if (0 != ret) {
                result.setErrCode(-2);
                result.setErrorMsg("system error");
            }

            logger.info("setCustomServiceReplySwitchNew ret--result:{} ", result);
        } catch (Exception e) {
            result = new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
        }
        return result;

    }

    /**
     过滤掉处于禁用状态的AppID
     *@param appIDs： 打开了多客服开关的appID列表。函数返回时，appIDs中处于
     *禁用状态的appID已经被移除了。
     *@return :
     * */
    private void filterDisabledAppID(String enterpriseAccount, GetCustomServiceAppListResult appIDs) {
        Iterator<String> iterator =  appIDs.getAppList().iterator();

        while (iterator.hasNext()) {
            String appID = iterator.next();
            CommonResult commonResult = eaAuthService.isEaAuthStatusNormal(null, null, enterpriseAccount, appID);

            if(OauthCodeEnum.SUCCESS != commonResult.getCodeEnum()) {
                iterator.remove();
            }
        }
    }

    /**
    获取一个企业下面打开了客服开关的应用列表.
    *@param enterpriseAccount： 企业的纷享账号，是 E.企业账号.员工ID 中间这部分。
    *@param status: 1:获取打开客服开关的列表，0-获取关闭客服开关的列表
    *@return : AppID列表
    * */
    @Override
    public GetCustomServiceAppListResult getCustomServiceAppListBySwitch(String enterpriseAccount, int status) {
        logger.info("getCustomServiceAppListBySwitch param: enterpriseAccount:{}, status:{} ",
                enterpriseAccount, status);
        GetCustomServiceAppListResult result = new GetCustomServiceAppListResult();
        List<String> appList =  msgAutoReplyManagerImpl.getCustomServiceAppListBySwitch(enterpriseAccount, status);
        result.setAppList(appList);
        logger.info("setCustomServiceReplySwitch ret--result:{} ", result);

        /**过滤掉处于禁用状态的AppID*/
        try {
            filterDisabledAppID(enterpriseAccount, result);
        }catch (Exception e) {
            logger.error("getCustomServiceAppListBySwitch get exception while call filterDisabledAppID: ", e);
        }

        return result;
    }

    /**
     * 开启客服会话
     *
     * @param ea
     * @param appId
     * @param customServiceRepresentives:客服人员的 ID， E.企业账号.员工账号 后面这一部分
     * @param sessionName: 会话名称
     * @return
     */
    public MsgBaseResult createCustomServiceRepresentiveList(String ea, String appId, List<String> customServiceRepresentives, String sessionName) {
        logger.info("createCustomServiceRepresentiveList param: ea:{}, appId:{}  CustomServiceRepresentive:{}, sessionname:{} ",
                ea, appId, customServiceRepresentives, sessionName);

        List<Integer> customServiceRepresentiveID =  new ArrayList<Integer>(customServiceRepresentives.size());

        for (String s: customServiceRepresentives) {
            List<String> fsUserTmp = Splitter.on(".").splitToList(s);
            if (fsUserTmp.size() != 3) {
                logger.error("admin is incorrect form, admin:{}", s);
                continue;
            }
            String fsUserID = fsUserTmp.get(2);
            customServiceRepresentiveID.add(Integer.valueOf(fsUserID));
        }

        byte updateCSMetaSessionType = 0; //0-开启session
        try {
            //openCustomService.updateCSMetaSession(ea, appId, updateCSMetaSessionType, customServiceRepresentiveID, sessionName);
            OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
            openCSCustomerProperty.setCustomers(customServiceRepresentiveID);
            openCSCustomerProperty.setCustomerSessionName(sessionName);

            OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
            openCSSessionUpdateArg.setEnterpriseAccount(ea);
            openCSSessionUpdateArg.setAppId(appId);
            openCSSessionUpdateArg.setUpdateType(updateCSMetaSessionType);
            openCSSessionUpdateArg.setProperty(openCSCustomerProperty);

            openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
        }catch (Exception e) {
            logger.error("createCustomServiceRepresentiveList ea:{}, appId:{}, customServiceRepresentives:{}, sessionName:{}, error ",ea, appId, customServiceRepresentives, sessionName, e);
            return new MsgBaseResult(-2, "system error");
        }
        logger.info("createCustomServiceRepresentiveList succ, param: ea:{}, appId:{}  CustomServiceRepresentive:{}, sessionname:{} ",
                ea, appId, customServiceRepresentives, sessionName);
        return new MsgBaseResult();
    }

    /**
     * 更新客服会话
     *
     * @param ea: 企业账号
     * @param appId
     * @param customServiceRepresentives: 最新的客服人员的 ID列表， E.企业账号.员工账号 后面这一部分
     * @return
     */

    public MsgBaseResult updateCustomServiceRepresentiveList(String ea, String appId, List<String> customServiceRepresentives) {
        logger.info("updateCustomServiceRepresentiveList param: ea:{}, appId:{}  CustomServiceRepresentive:{}  ",
                ea, appId, customServiceRepresentives);

        List<Integer> customerIDs =  new ArrayList<Integer>(customServiceRepresentives.size());

        for (String s: customServiceRepresentives) {
            List<String> fsUserTmp = Splitter.on(".").splitToList(s);
            if (fsUserTmp.size() != 3) {
                logger.error("admin is incorrect form, admin:{}", s);
                continue;
            }
            String fsUserID = fsUserTmp.get(2);
            customerIDs.add(Integer.valueOf(fsUserID));
        }

        byte updateCSMetaSessionType = 2; //2-更新客服人员列表session
        try {
            //openCustomService.updateCSMetaSession(ea, appId, updateCSMetaSessionType, customerIDs, null);
            OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
            openCSCustomerProperty.setCustomers(customerIDs);

            OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
            openCSSessionUpdateArg.setEnterpriseAccount(ea);
            openCSSessionUpdateArg.setAppId(appId);
            openCSSessionUpdateArg.setUpdateType(updateCSMetaSessionType);
            openCSSessionUpdateArg.setProperty(openCSCustomerProperty);

            openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
        }catch (Exception e) {
            logger.error("updateCustomServiceRepresentiveList ea:{}, appId:{}, customServiceRepresentives:{},  error ",ea, appId, customServiceRepresentives, e);
            return new MsgBaseResult(-2, "system error");
        }
        logger.info("updateCustomServiceRepresentiveList succ, param: ea:{}, appId:{}  CustomServiceRepresentive:{}  ",
                ea, appId, customServiceRepresentives);
        return new MsgBaseResult();
    }
    /**
     * 删除客服会话
     *
     * @param ea
     * @param appId
     * @return
     */
    public MsgBaseResult deleteCustomServiceRepresentiveList(String ea, String appId) {
        logger.info("deleteCustomServiceRepresentiveList param: ea:{}, appId:{} ", ea, appId);

        byte updateCSMetaSessionType = 1; //1-删除客服人员列表session
        try {
            //openCustomService.updateCSMetaSession(ea, appId, updateCSMetaSessionType, null, null);
            OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();

            OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
            openCSSessionUpdateArg.setEnterpriseAccount(ea);
            openCSSessionUpdateArg.setAppId(appId);
            openCSSessionUpdateArg.setUpdateType(updateCSMetaSessionType);
            openCSSessionUpdateArg.setProperty(openCSCustomerProperty);

            openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);

        }catch (Exception e) {
            logger.error("deleteCustomServiceRepresentiveList ea:{}, appId:{},  error ",ea, appId, e);
            return new MsgBaseResult(-2, "system error");
        }
        logger.info("deleteCustomServiceRepresentiveList succ, param: ea:{}, appId:{} ", ea, appId);
        return new MsgBaseResult();
    }

    public MsgBaseResult updateCustomServiceRepresentiveListAndSessionName(String ea, String appId, List<String> customServiceRepresentives, String sessionName) {

        logger.info("updateCustomServiceRepresentiveListAndSessionName param: ea:{}, appId:{}  CustomServiceRepresentive:{}  ",
                ea, appId, customServiceRepresentives);

        List<Integer> customerIDs =  new ArrayList<Integer>(customServiceRepresentives.size());

        for (String s: customServiceRepresentives) {
            List<String> fsUserTmp = Splitter.on(".").splitToList(s);
            if (fsUserTmp.size() != 3) {
                logger.error("admin is incorrect form, admin:{}", s);
                continue;
            }
            String fsUserID = fsUserTmp.get(2);
            customerIDs.add(Integer.valueOf(fsUserID));
        }

        byte updateCSMetaSessionType = 2; //2-更新客服人员列表session
        try {
            //openCustomService.updateCSMetaSession(ea, appId, updateCSMetaSessionType, customerIDs, sessionName);
            OpenCSCustomerProperty openCSCustomerProperty = new OpenCSCustomerProperty();
            openCSCustomerProperty.setCustomers(customerIDs);
            openCSCustomerProperty.setCustomerSessionName(sessionName);

            OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
            openCSSessionUpdateArg.setEnterpriseAccount(ea);
            openCSSessionUpdateArg.setAppId(appId);
            openCSSessionUpdateArg.setUpdateType(updateCSMetaSessionType);
            openCSSessionUpdateArg.setProperty(openCSCustomerProperty);

            openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
        }catch (Exception e) {
            logger.error("updateCustomServiceRepresentiveListAndSessionName ea:{}, appId:{}, customServiceRepresentives:{},  error ",ea, appId, customServiceRepresentives, e);
            return new MsgBaseResult(-2, "system error");
        }
        logger.info("updateCustomServiceRepresentiveListAndSessionName succ, param: ea:{}, appId:{}  CustomServiceRepresentive:{}  ",
                ea, appId, customServiceRepresentives);
        return new MsgBaseResult();
    }

}
