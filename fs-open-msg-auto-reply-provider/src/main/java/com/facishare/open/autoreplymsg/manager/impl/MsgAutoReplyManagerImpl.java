package com.facishare.open.autoreplymsg.manager.impl;

import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.facishare.open.autoreplymsg.constant.AutoReplyMsgTypeEnum;
import com.facishare.open.autoreplymsg.constant.AutoReplyTypeEnum;
import com.facishare.open.autoreplymsg.dao.impl.AutoReplyDAOImpl;
import com.facishare.open.autoreplymsg.manager.MsgAutoReplyManager;
import com.facishare.open.autoreplymsg.model.KeywordReplyDO;
import com.facishare.open.autoreplymsg.model.KeywordTypeInfo;
import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.QueryKeywordReplyListResult;
import com.facishare.open.msg.common.constants.CustomerSessionTypeEnum;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.qixin.api.model.open.arg.OpenCSSessionUpdateArg;
import com.facishare.qixin.api.model.open.arg.OpenCrossCSSessionUpdateArg;
import com.facishare.qixin.api.model.open.arg.OpenWXPlatformSessionUpdateArg;
import com.facishare.qixin.api.open.OpenCrossCustomerService;
import com.facishare.qixin.api.open.OpenCustomService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;


import org.mybatis.spring.SqlSessionFactoryBean;
/**
 * Created by fengyh on 2016/3/7.
 */
@Service
public class MsgAutoReplyManagerImpl implements MsgAutoReplyManager {

    private static final Logger logger = LoggerFactory.getLogger(MsgAutoReplyManagerImpl.class);

    @Autowired
    private AutoReplyDAOImpl autoReplyDAOImpl;

    @Autowired
    private MsgKeywordReplyManagerImpl msgKeywordReplyManagerImpl;

    @Autowired
    private MsgDefaultReplyManagerImpl msgDefaultReplyManagerImpl;
    
    @Autowired
    private OpenCustomService openCustomService;
    
    @Autowired
    private OpenCrossCustomerService openCrossCustomService;

    //自动回复频率控制。多少秒内最多回复一次。
    private  long autoReplyFreqSecond = 3600L;

    /**
     * 查询自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @return : ReplySwitchStatusEnum
     */
    @Override
    public int queryAutoReplySwitch(String enterpriseAccount, String appID) {
        int ret = autoReplyDAOImpl.queryAutoReplySwitch(appID, enterpriseAccount);
        return ret;
    }

    /**
     * 设置自动回复开关
     *
     * @param enterpriseAccount ： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID             : 开放平台分配的应用ID号
     * @param status            :
     * @return : MsgBaseResult
     */
    @Override
    public MsgBaseResult setAutoReplySwitch(String enterpriseAccount, String appID, int status) {
        int errCode = autoReplyDAOImpl.setAutoReplySwitch(appID, enterpriseAccount, status);
        return new MsgBaseResult(errCode, "");
    }

    /**
     * 判断关键词是否匹配成功。如果匹配成功，则回复内容放到 getAutoReplyResult中。
     *
     * @param getAutoReplyResult ： 存放自动回复结果的地方
     * @param queryKeywordReplyListResult     : 关键词列表
     * @param userInput           : 用户上行的输入，必须是字符串。如果是图片，位置，视频等，不应该调用判断自动回复的函数。
     * @return : true-关键词匹配成功， false-关键词匹配失败。
     */
    private boolean TryKeywordMatch(GetAutoReplyResult getAutoReplyResult, QueryKeywordReplyListResult queryKeywordReplyListResult, String userInput) {

        boolean isKeywordMatch =false;

        if(null == userInput || userInput.isEmpty()) {
            return isKeywordMatch;
        }

        List<KeywordReplyDO> keywordReplyDORet =  queryKeywordReplyListResult.getKeywordList();
        Iterator keywordReplyIterator = keywordReplyDORet.iterator();

        while(keywordReplyIterator.hasNext() && (false == isKeywordMatch)) {
            KeywordReplyDO keywordReplyDO = (KeywordReplyDO)keywordReplyIterator.next();

            List<KeywordTypeInfo> keywordTypeInfoRet  =  keywordReplyDO.getKeywordList();
            Iterator keywordTypeIterator = keywordTypeInfoRet.iterator();

            while(keywordTypeIterator.hasNext() && (false == isKeywordMatch)) {
                KeywordTypeInfo keywordTypeInfo = (KeywordTypeInfo)keywordTypeIterator.next();

                logger.info("getAutoReply check reply，keywordType：{}，keywordName:{}",  keywordTypeInfo.getType(), keywordTypeInfo.getName());

                //忽略大小写的比较
                if((keywordTypeInfo.getType().equals("equal") && (keywordTypeInfo.getName().equalsIgnoreCase(userInput)))
                        || (keywordTypeInfo.getType().equals("contain") && (userInput.toLowerCase().indexOf(keywordTypeInfo.getName().toLowerCase()) >= 0))) {

                    AutoReplyMsgTypeEnum autoReplyMsgType = AutoReplyMsgTypeEnum.valueOf(keywordReplyDO.getActiveReplyType());
                    getAutoReplyResult.setReplyType(autoReplyMsgType);
                    getAutoReplyResult.setNeedReply(true);
                    isKeywordMatch = true;
                    switch (autoReplyMsgType) {
                        case IMGTXT: //图文消息
                            getAutoReplyResult.setReplyImgTxtID(keywordReplyDO.getContentImgTxtID());
                            break;
                        default: //文本消息
                            getAutoReplyResult.setReplyTxt(keywordReplyDO.getContentTxt());
                            break;
                    }
                }
            }
        }
        return  isKeywordMatch;
    }

    @Override
    public GetAutoReplyResult getAutoReply(String fsUserAccount, String appID, String userInput){
    	 //1. 从fsUserAccount中获取企业账号。fsUserAccount的形式为E.企业账号.员工ID
    	 String[] strs=fsUserAccount.split("\\.", 3);
         if (3 != strs.length) {
             return new GetAutoReplyResult(-1,"parameter fsUserAccount is not in correct form");
         }
        return this.getAutoReplyByEa(strs[1], fsUserAccount, appID, userInput);
    }

    @Override
	public GetAutoReplyResult getAutoReplyByEa(String ea, String fsUserAccount, String appID, String userInput) {
    	logger.info("getAutoReply param--appid:{},fsAccount:{},userinput:{}",
                appID, fsUserAccount, userInput);

        GetAutoReplyResult getAutoReplyResult = new GetAutoReplyResult(MsgCodeEnum.SUCCESS.getErrorCode(), MsgCodeEnum.SUCCESS.getErrorMsg());
        getAutoReplyResult.setNeedReply(false);

        //去掉 全角，半角空格+tab.
        userInput=userInput.replaceAll(" |    ","");

        
        //2.如果自动回复开关没有打开，返回空result, 不需要回复用户。
        if (0 == this.queryAutoReplySwitch(ea, appID)) {
            logger.info("getAutoReply deny reply since autoreplyswitch is off，appID：{}，userinput:{}",  appID, userInput);
            return getAutoReplyResult;
        }

        //3. 如果关键字匹配，返回关键字回复
        boolean isKeywordMatch = false;
        QueryKeywordReplyListResult queryKeywordReplyListResult = msgKeywordReplyManagerImpl.queryKeywordReplyList(ea, appID);
        if (queryKeywordReplyListResult.isSuccess()) {
            isKeywordMatch = TryKeywordMatch(getAutoReplyResult, queryKeywordReplyListResult, userInput);
        }

        if (isKeywordMatch) {
            logger.info("getAutoReply keywordMatch succ");
            getAutoReplyResult.setType(AutoReplyTypeEnum.REPLY_KEYWORD);
            return getAutoReplyResult;
        }

        /*4. 使用默认回复之前，检查回复频率.
        具体是先查出最近一次默认回复时间，与当前时间比较。如果在60分钟内，则不做回复。
        */
        long curSecond = System.currentTimeMillis()/1000;
        long lastReplySecond = this.getLastAutoReplyTime(appID, fsUserAccount);
        if(curSecond - lastReplySecond < autoReplyFreqSecond) {
            logger.info("getAutoReply deny reply according to frequence limitation, appID：{}，fsUserAccount:{}, autoReplyFreqSecond:{},  curSecond:{}, lastReplySecond:{} ",
                    appID, fsUserAccount, autoReplyFreqSecond, curSecond, lastReplySecond);
            return getAutoReplyResult;
        }

        //5. 返回默认回复内容
        QueryDefaultReplyResult defaultReplyResult = msgDefaultReplyManagerImpl.queryDefaultReply(ea, appID);
        if (defaultReplyResult.isSuccess()) {
            AutoReplyMsgTypeEnum autoReplyMsgType = AutoReplyMsgTypeEnum.valueOf((int) defaultReplyResult.getActiveReplyType());
            getAutoReplyResult.setReplyType(autoReplyMsgType);

            //只有默认自动回复内容不为空才需要回复给用户
            switch (autoReplyMsgType) {
                case IMGTXT:
                    if (null != defaultReplyResult.getContentImgTxtID() && !defaultReplyResult.getContentImgTxtID().isEmpty()) {
                        getAutoReplyResult.setNeedReply(true);
                        getAutoReplyResult.setReplyImgTxtID(defaultReplyResult.getContentImgTxtID());
                    }
                    break;
                default:
                    if(null != defaultReplyResult.getContentTxt() && !defaultReplyResult.getContentTxt().isEmpty()) {
                        getAutoReplyResult.setNeedReply(true);
                        getAutoReplyResult.setReplyTxt(defaultReplyResult.getContentTxt());
                    }
                    break;
            }
        }

        //把最后默认回复时间记录下来。
        if (getAutoReplyResult.isNeedReply()) {
            this.setLastAutoReplyTime(appID, fsUserAccount, curSecond);
        }

        logger.info("getAutoReply ret: getAutoReplyResult:{} ", getAutoReplyResult);
        return getAutoReplyResult;
	}

	@Override
    public long getLastAutoReplyTime(String appID, String fsUserAccount) {
        logger.info("getLastAutoReplyTime param--appid:{},fsUserAccount:{}",
                appID, fsUserAccount);
        long ret = autoReplyDAOImpl.getLastAutoReplyTime(appID, fsUserAccount);
        logger.info("getLastAutoReplyTime ret:{} ", ret);
        return ret;
    }


    @Override
    public int setLastAutoReplyTime(String appID, String fsUserAccount, long lastReplyTime) {
        logger.info("setLastAutoReplyTime param--appid:{},fsUserAccount:{}, lastReplyTime:{}",
                appID, fsUserAccount, lastReplyTime);
        int ret =  autoReplyDAOImpl.setLastAutoReplyTime(appID, fsUserAccount, lastReplyTime);
        logger.info("setLastAutoReplyTime ret:{} ", ret);
        return ret;
    }

    @Override
    public int getCustomServiceReplySwitch(String enterpriseAccount, String appID) {
        logger.info("getCustomServiceReplySwitch param--enterpriseAccount:{},appID:{} ",
                enterpriseAccount, appID);
        int ret = autoReplyDAOImpl.getCustomServiceReplySwitch(enterpriseAccount, appID);
        logger.info("getCustomServiceReplySwitch ret:{} ", ret);
        return ret;
    }

    @Override
    public int setCustomServiceReplySwitch(String enterpriseAccount, String appID, int status) {
        logger.info("setCustomServiceReplySwitch param--enterpriseAccount:{},appID:{}, status:{} ",
                enterpriseAccount, appID, status);
        int ret = autoReplyDAOImpl.setCustomServiceReplySwitch(appID, enterpriseAccount, status);
        
        logger.info("setCustomServiceReplySwitch ret:{} ", ret);
        return ret;
    }
    
    @Override
    @Transactional
    public int setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status) {
        return setCustomServiceReplySwitchNew(enterpriseAccount, appID, status, CustomerSessionTypeEnum.multi.getType());
    }

    public boolean setWxCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status) {
        OpenWXPlatformSessionUpdateArg  openWXPlatformSessionUpdateArg = new OpenWXPlatformSessionUpdateArg();
        openWXPlatformSessionUpdateArg.setAppId(appID);
        openWXPlatformSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
        openWXPlatformSessionUpdateArg.setUpdateType(status == 0 ? (byte) 3 : (byte) 4);
        openWXPlatformSessionUpdateArg.setProperty(null);

        boolean ret = true;
        try {
            openCustomService.updateWXPlatformSession(openWXPlatformSessionUpdateArg);
        } catch (Exception e) {
            ret = false;
        }
        return ret;
    }

    /**
     * 设置客服开关New, 支持多客服+微客服+互联客服
     * */
    public int setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status,  Integer customerSessionType) {
        logger.info("setCustomServiceReplySwitchNew param--enterpriseAccount:{},appID:{}, status:{} ",
                enterpriseAccount, appID, status);
        int ret = autoReplyDAOImpl.setCustomServiceReplySwitch(appID, enterpriseAccount,  status);

        if(CustomerSessionTypeEnum.wx.getType() == customerSessionType) {
            if(setWxCustomServiceReplySwitchNew(enterpriseAccount, appID, status)) {
                return ret;
            } else {
                throw new RuntimeException();
            }
        }

        //移动多客服禁用掉
        boolean updateRet = true;// openCustomService.updateCustomerServicePlatformSession(enterpriseAccount, appID, status == 0 ? (byte) 3 : (byte) 4, null);
        if(CustomerSessionTypeEnum.cross.getType() == customerSessionType) {
        	 OpenCrossCSSessionUpdateArg arg0 = new OpenCrossCSSessionUpdateArg();
             arg0.setAppId(appID);
             arg0.setEnterpriseAccount(enterpriseAccount);
             arg0.setProperty(null);
             arg0.setUpdateType(status == 0 ? (byte) 3 : (byte) 4);
             try {
				openCrossCustomService.updateCrossCustomerServicePlatformSession(arg0);
			} catch (Exception e) {
				logger.error("updateCrossCustomerServicePlatformSession error：{}",e.getMessage());
				updateRet = false;
			}
        }else{
        	OpenCSSessionUpdateArg openCSSessionUpdateArg = new OpenCSSessionUpdateArg();
        	openCSSessionUpdateArg.setEnterpriseAccount(enterpriseAccount);
        	openCSSessionUpdateArg.setAppId(appID);
        	openCSSessionUpdateArg.setUpdateType(status == 0 ? (byte) 3 : (byte) 4);
        	openCSSessionUpdateArg.setProperty(null);
        	
        	try {
        		openCustomService.updateCustomerServicePlatformSession(openCSSessionUpdateArg);
        	}catch (Exception e) {
        		logger.error("updateCustomerServicePlatformSession error：{}",e.getMessage());
        		updateRet = false;
        	}
        }

        if (!updateRet) {
            throw new RuntimeException();
        }

        logger.info("setCustomServiceReplySwitchNew ret:{} ", ret);
        return ret;
    }

    /**
     * 获取设置了客服开关的应用列表
     * */
    @Override
    public List<String> getCustomServiceAppListBySwitch(String enterpriseAccount, int status) {
        logger.info("getCustomServiceAppListBySwitch param--enterpriseAccount:{}, status:{} ",
                enterpriseAccount, status);
        List<String> appList =  autoReplyDAOImpl.getCustomServiceAppListBySwitch(enterpriseAccount, status);
        logger.info("getCustomServiceAppListBySwitch ret:{} ", appList);
        return appList;
    }
}
