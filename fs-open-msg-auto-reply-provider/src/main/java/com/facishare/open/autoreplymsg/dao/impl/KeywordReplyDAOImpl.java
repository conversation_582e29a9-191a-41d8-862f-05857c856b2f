package com.facishare.open.autoreplymsg.dao.impl;

import com.facishare.open.autoreplymsg.dao.KeywordReplyDAO;
import com.facishare.open.autoreplymsg.dao.base.AutoReplyMsgBaseDAO;
import com.facishare.open.autoreplymsg.model.KeywordReplyDO;
import com.facishare.open.autoreplymsg.model.KeywordTypeInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by fengyh on 2016/3/5.
 */

@Service
public class KeywordReplyDAOImpl extends AutoReplyMsgBaseDAO<KeywordReplyDO> implements KeywordReplyDAO {

    private static final Logger logger = LoggerFactory.getLogger(KeywordReplyDAOImpl.class);

    @Override
    public long createKeywordReply(KeywordReplyDO defaultReplyDO) {
        logger.info("createKeywordReply param--defaultReplyDO:{} ", defaultReplyDO);
        Gson gson = new Gson();
        String tmpStr = gson.toJson(defaultReplyDO.getKeywordList());
        defaultReplyDO.setKeywordListJson(tmpStr);

        long affectedRows = this.save("createKeywordReply", defaultReplyDO);
        logger.info("createKeywordReply  defaultReplyDO:{}, get affectedRows:{} from save()", defaultReplyDO, affectedRows);

        if (affectedRows > 0) {
            return defaultReplyDO.getReplyMsgID();
        } else {
            return -1;
        }
    }

    @Override
    public int deleteKeywordReply(long replyMsgID, String enterpriseAccount, String appID) {
        logger.info("deleteKeywordReply param--appid:{},enterpriseAccount:{}, replyMsgID:{}",
                appID, enterpriseAccount, replyMsgID);
        KeywordReplyDO replyDO = new KeywordReplyDO();
        replyDO.setReplyMsgID(replyMsgID);
        replyDO.setEnterpriseAccount(enterpriseAccount);
        replyDO.setAppID(appID);

        int affectedRows =this.delete("deleteKeywordReply", replyDO);

        logger.info("deleteKeywordReply replyDO:{}, affectedRows:{} ",replyDO,  affectedRows);
        return (1 == affectedRows ? 0 : 1);
    }

    @Override
    public int updateKeywordReply(KeywordReplyDO defaultReplyDO) {
        Gson gson = new Gson();
        String tmpStr = gson.toJson(defaultReplyDO.getKeywordList());
        defaultReplyDO.setKeywordListJson(tmpStr);

        logger.info("updateKeywordReply param--defaultReplyDO:{} ",  defaultReplyDO);
        int affectedRows = this.update("updateKeywordReply", defaultReplyDO);
        logger.info("updateKeywordReply defaultReplyDO:{} affectedRows:{} ", defaultReplyDO, affectedRows);
        return (1 == affectedRows ? 0 : 1);
    }

    @Override
    public List<KeywordReplyDO> queryKeywordReply(String enterpriseAccount, String appID) {
        logger.info("queryKeywordReply param--appid:{},enterpriseAccount:{}",
                appID, enterpriseAccount);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("enterpriseAccount", enterpriseAccount);
        map.put("appID", appID);

        List<KeywordReplyDO> keywordReplyDOList  = this.getList("queryKeywordReply", map);

        Gson gson = new Gson();
        Iterator iterator  = keywordReplyDOList.iterator();
        while(iterator.hasNext()) {
            KeywordReplyDO tmp = (KeywordReplyDO)iterator.next();
            List<KeywordTypeInfo> retList = gson.fromJson(tmp.getKeywordListJson(),  new TypeToken<List<KeywordTypeInfo>>() {  }.getType());
            tmp.setKeywordList(retList);
        }

        logger.info("queryKeywordReply keywordReplyDOList:{}",  keywordReplyDOList);
        return keywordReplyDOList;
    }
}
