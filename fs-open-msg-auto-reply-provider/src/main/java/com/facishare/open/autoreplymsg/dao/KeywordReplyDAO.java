package com.facishare.open.autoreplymsg.dao;

import com.facishare.open.autoreplymsg.model.KeywordReplyDO;
import com.facishare.open.common.storage.mysql.dao.Pager;

import java.util.List;

/**
 * Created by fengyh on 2016/3/5.
 */
public interface KeywordReplyDAO {

    public long createKeywordReply(KeywordReplyDO defaultReplyDO);

    public int deleteKeywordReply(long replyMsgI, String enterpriseAccount, String appID);

    public int updateKeywordReply(KeywordReplyDO defaultReplyDO);

    public List<KeywordReplyDO> queryKeywordReply(String enterpriseAccount, String appID);
}
