package com.facishare.open.autoreplymsg.service.impl;

import com.facishare.open.autoreplymsg.manager.impl.MsgKeywordReplyManagerImpl;
import com.facishare.open.autoreplymsg.model.CreateKeywordReplyVO;
import com.facishare.open.autoreplymsg.model.KeywordTypeInfo;
import com.facishare.open.autoreplymsg.model.UpdateKeywordReplyVO;
import com.facishare.open.autoreplymsg.result.CreateKeywordReplyResult;
import com.facishare.open.autoreplymsg.result.QueryKeywordReplyListResult;
import com.facishare.open.autoreplymsg.service.MsgKeywordReplyService;
import com.facishare.open.msg.result.MsgBaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * Created by fengyh on 2016/3/5.
 */
//@Service("msgKeywordReplyService")
@Service
public class MsgKeywordReplyServiceImpl implements MsgKeywordReplyService {

    private static final Logger logger = LoggerFactory.getLogger(MsgKeywordReplyServiceImpl.class);

    @Autowired
    private MsgKeywordReplyManagerImpl msgKeywordReplyManagerImpl;


    /**
     * 消除关键词中的空白符
     *
     * @param list: 关键词列表
     * @return :
     */
    private void KeywordListTripBlank(List<KeywordTypeInfo>  list) {
        Iterator iterator = list.iterator();
        while (iterator.hasNext()) {
            KeywordTypeInfo keywordTypeInfo =  (KeywordTypeInfo)iterator.next();
            String s = keywordTypeInfo.getName();
            //去掉 全角，半角空格+tab.
            s = s.replaceAll(" |    ","");
            keywordTypeInfo.setName(s);
        }
    }

    /**
     * 创建关键词回复
     *
     * @param createKeywordReplyVO
     * @return : CreateKeywordReplyResult
     */
    @Override
    public CreateKeywordReplyResult createKeywordReply(CreateKeywordReplyVO createKeywordReplyVO) {
        logger.info("createKeywordReply param--createKeywordReplyVO:{} ",
                createKeywordReplyVO);

        KeywordListTripBlank(createKeywordReplyVO.getKeywordList());

        CreateKeywordReplyResult ret = msgKeywordReplyManagerImpl.createKeywordReply(createKeywordReplyVO);
        logger.info("createKeywordReply ret--CreateKeywordReplyResult:{} ", ret);
        return ret;
    }

    /**
     * 删除关键词回复
     *
     * @param replyMsgID createKeywordReply()返回的自动回复编号
     * @return :
     */
    @Override
    public MsgBaseResult deleteKeywordReply(String enterpriseAccount, String appID, long replyMsgID) {
        logger.info("deleteKeywordReply param--enterpriseAccount:{}, appID:{}, replyMsgID:{} ",
                enterpriseAccount, appID, replyMsgID);
        MsgBaseResult ret = msgKeywordReplyManagerImpl.deleteKeywordReply(enterpriseAccount, appID, replyMsgID);
        logger.info("deleteKeywordReply ret--MsgBaseResult:{} ", ret);
        return ret;
    }

    /**
     * 更新关键词回复
     *
     * @param updateKeywordReplyVO msgID: createKeywordReply()返回的自动回复消息ID
     *                             contentText: 文本消息内容
     *                             contentImgTxt:图文消息ID
     *                             activeStatus: 最后回复类型(文本或者图文)
     * @return :errCode, errMsg
     */
    @Override
    public MsgBaseResult updateKeywordReply(UpdateKeywordReplyVO updateKeywordReplyVO) {
        logger.info("updateKeywordReply param--updateKeywordReplyVO:{} ",
                updateKeywordReplyVO);

        KeywordListTripBlank(updateKeywordReplyVO.getKeywordList());

        MsgBaseResult ret = msgKeywordReplyManagerImpl.updateKeywordReply(updateKeywordReplyVO);
        logger.info("updateKeywordReply ret--MsgBaseResult:{} ", ret);
        return ret;
    }

    /**
     * 查询指定服务号的默认回复
     *
     * @param enterpriseAccount
     * @param appID             msgID: createDefaultAutoReply()返回的自动回复消息ID
     *                          contentText: 文本消息内容
     *                          contentImgTxt:图文消息ID
     */
    @Override
    public QueryKeywordReplyListResult queryKeywordReplyList(String enterpriseAccount, String appID) {
        logger.info("queryKeywordReplyList param--enterpriseAccount:{}, appID:{} ",
                enterpriseAccount, appID);
        QueryKeywordReplyListResult ret = msgKeywordReplyManagerImpl.queryKeywordReplyList(enterpriseAccount, appID);
        logger.info("queryKeywordReplyList ret--QueryKeywordReplyListResult:{} ", ret);
        return ret;
    }
}
