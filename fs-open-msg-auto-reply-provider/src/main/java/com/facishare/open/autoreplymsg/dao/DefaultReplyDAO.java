package com.facishare.open.autoreplymsg.dao;

import com.facishare.open.autoreplymsg.model.CreateDefaultReplyVO;
import com.facishare.open.autoreplymsg.model.DefaultReplyDO;
import com.facishare.open.autoreplymsg.model.UpdateDefaultReplyVO;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;

/**
 * Created by fengyh on 2016/3/4.
 */
public interface DefaultReplyDAO {

    public long createDefaultReply(DefaultReplyDO defaultReplyDO);

    public int deleteDefaultReply(long replyMsgID, String enterpriseAccount, String appID);

    public int updateDefaultReply(DefaultReplyDO defaultReplyDO);

    public DefaultReplyDO queryDefaultReply(String enterpriseAccount, String appID);

}
