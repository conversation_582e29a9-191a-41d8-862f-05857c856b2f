package com.facishare.open.autoreplymsg.service.impl;

import com.facishare.open.autoreplymsg.manager.impl.MsgDefaultReplyManagerImpl;
import com.facishare.open.autoreplymsg.model.CreateDefaultReplyVO;
import com.facishare.open.autoreplymsg.model.UpdateDefaultReplyVO;
import com.facishare.open.autoreplymsg.result.CreateDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;
import com.facishare.open.autoreplymsg.service.MsgDefaultReplyService;
import com.facishare.open.msg.result.MsgBaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fengyh on 2016/3/5.
 */
//@Service("msgDefaultReplyService")
@Service
public class MsgDefaultReplyServiceImpl  implements MsgDefaultReplyService {

    private static final Logger logger = LoggerFactory.getLogger(MsgAutoReplyServiceImpl.class);

    @Autowired
    private MsgDefaultReplyManagerImpl msgDefaultReplyManagerImpl;

    /**
     * 创建默认自动回复
     *
     * @param createDefaultReplyVO
     * @return : CreateDefaultAutoReplyResult
     */
    @Override
    public CreateDefaultReplyResult createDefaultReply(CreateDefaultReplyVO createDefaultReplyVO) {
        logger.info("createDefaultReply param--createDefaultReplyVO:{} ",
                createDefaultReplyVO);
        CreateDefaultReplyResult ret = msgDefaultReplyManagerImpl.createDefaultReply(createDefaultReplyVO);
        logger.info("createDefaultReply ret--CreateDefaultReplyResult:{} ", ret);
        return ret;
    }

    /**
     * 删除默认自动回复
     *
     * @param enterpriseAccount 企业在fs的账号
     * @param appID
     * @param replyMsgID
     * @return :MsgBaseResult
     */
    @Override
    public MsgBaseResult deleteDefaultReply(String enterpriseAccount, String appID, long replyMsgID) {
        logger.info("deleteDefaultReply param--enterpriseAccount:{}, appID:{}, replyMsgID:{} ",
                enterpriseAccount, appID, replyMsgID);
        MsgBaseResult ret = msgDefaultReplyManagerImpl.deleteDefaultReply(enterpriseAccount, appID, replyMsgID);
        logger.info("deleteDefaultReply ret--MsgBaseResult:{} ", ret);
        return ret;
    }

    /**
     * 更新默认回复
     *
     * @param updateDefaultAutoReplyVO
     * @return :MsgBaseResult
     */
    @Override
    public MsgBaseResult updateDefaultReply(UpdateDefaultReplyVO updateDefaultAutoReplyVO) {
        logger.info("updateDefaultReply param--updateDefaultAutoReplyVO:{}",
                updateDefaultAutoReplyVO);
        MsgBaseResult ret = msgDefaultReplyManagerImpl.updateDefaultReply(updateDefaultAutoReplyVO);
        logger.info("updateDefaultReply ret--MsgBaseResult:{} ", ret);
        return ret;
    }

    /**
     * 查询指定服务号的默认回复
     *
     * @param appID
     * @return QueryDefaultReplyResult
     */
    @Override
    public QueryDefaultReplyResult queryDefaultReply(String enterpriseAccount, String appID) {
        logger.info("deleteDefaultReply param--enterpriseAccount:{}, appID:{} ",
                enterpriseAccount, appID);
        QueryDefaultReplyResult ret = msgDefaultReplyManagerImpl.queryDefaultReply(enterpriseAccount, appID);

        logger.info("queryDefaultReply ret--MsgBaseResult:{} ", ret);

        return ret;
    }
}
