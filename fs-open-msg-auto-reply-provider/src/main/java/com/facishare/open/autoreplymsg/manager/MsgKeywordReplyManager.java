package com.facishare.open.autoreplymsg.manager;

import com.facishare.open.autoreplymsg.model.CreateKeywordReplyVO;
import com.facishare.open.autoreplymsg.model.UpdateKeywordReplyVO;
import com.facishare.open.autoreplymsg.result.CreateKeywordReplyResult;
import com.facishare.open.autoreplymsg.result.QueryKeywordReplyListResult;
import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/7.
 */
public interface MsgKeywordReplyManager {

    /**
     * 创建关键词回复
     *
     * @param createKeywordReplyVO
     * @return : CreateKeywordReplyResult
     */
    public CreateKeywordReplyResult createKeywordReply(CreateKeywordReplyVO createKeywordReplyVO);

    /**
     * 删除关键词回复
     *
     * @param replyMsgID createKeywordReply()返回的自动回复编号
     * @return :
     */
    public MsgBaseResult deleteKeywordReply(String enterpriseAccount, String appID, long replyMsgID);

    /**
     * 更新关键词回复
     *
     * @param updateKeywordReplyVO msgID: createKeywordReply()返回的自动回复消息ID
     *                             contentText: 文本消息内容
     *                             contentImgTxt:图文消息ID
     *                             activeStatus: 最后回复类型(文本或者图文)
     * @return :errCode, errMsg
     */
    public MsgBaseResult updateKeywordReply(UpdateKeywordReplyVO updateKeywordReplyVO);

    /**
     * 查询指定服务号的默认回复
     *
     * @param enterpriseAccount
     * @param appID             msgID: createDefaultAutoReply()返回的自动回复消息ID
     *                          contentText: 文本消息内容
     *                          contentImgTxt:图文消息ID
     */
    public QueryKeywordReplyListResult queryKeywordReplyList(String enterpriseAccount, String appID);
}
