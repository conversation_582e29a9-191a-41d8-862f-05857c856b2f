package com.facishare.open.autoreplymsg.manager.impl;

import com.facishare.open.autoreplymsg.dao.impl.KeywordReplyDAOImpl;
import com.facishare.open.autoreplymsg.manager.MsgKeywordReplyManager;
import com.facishare.open.autoreplymsg.model.CreateKeywordReplyVO;
import com.facishare.open.autoreplymsg.model.KeywordReplyDO;
import com.facishare.open.autoreplymsg.model.UpdateKeywordReplyVO;
import com.facishare.open.autoreplymsg.result.CreateKeywordReplyResult;
import com.facishare.open.autoreplymsg.result.QueryKeywordReplyListResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by fengyh on 2016/3/7.
 */
@Service
public class MsgKeywordReplyManagerImpl implements MsgKeywordReplyManager {

    private final int msgErrCode = 50000;

    @Autowired
    private KeywordReplyDAOImpl  keywordReplyDAOImpl;
    /**
     * 创建关键词回复
     *
     * @param createKeywordReplyVO
     * @return : CreateKeywordReplyResult
     */
    @Override
    public CreateKeywordReplyResult createKeywordReply(CreateKeywordReplyVO createKeywordReplyVO) {
        KeywordReplyDO keywordReplyDO = new KeywordReplyDO();
        keywordReplyDO.setAppID(createKeywordReplyVO.getAppID());
        keywordReplyDO.setEnterpriseAccount(createKeywordReplyVO.getEnterpriseAccount());
        keywordReplyDO.setActiveReplyType(createKeywordReplyVO.getActiveReplyType());
        keywordReplyDO.setContentImgTxtID(createKeywordReplyVO.getContentImgTxtID());
        keywordReplyDO.setContentTxt(createKeywordReplyVO.getContentTxt());
        keywordReplyDO.setKeywordList(createKeywordReplyVO.getKeywordList());
        keywordReplyDO.setRuleName(createKeywordReplyVO.getRuleName());
        int msgReplyID = (int)keywordReplyDAOImpl.createKeywordReply(keywordReplyDO);

        CreateKeywordReplyResult createKeywordReplyResult = new CreateKeywordReplyResult(0, "OK");
        if (msgReplyID > 0) {
            createKeywordReplyResult.setReplyMsgID(msgReplyID);
        } else   {
            createKeywordReplyResult.setErrCode(msgErrCode);
            createKeywordReplyResult.setErrorMsg("SQL FAIL");
        }
        return createKeywordReplyResult;
    }

    /**
     * 删除关键词回复
     *
     * @param replyMsgID createKeywordReply()返回的自动回复编号
     * @return :
     */
    @Override
    public MsgBaseResult deleteKeywordReply(String enterpriseAccount, String appID, long replyMsgID) {
        int errCode = keywordReplyDAOImpl.deleteKeywordReply(replyMsgID, enterpriseAccount, appID);
        if (0 != errCode) {
            return new MsgBaseResult(msgErrCode, "SQL FAIL");
        }else {
            return new MsgBaseResult(MsgCodeEnum.SUCCESS);
        }
    }

    /**
     * 更新关键词回复
     *
     * @param updateKeywordReplyVO msgID: createKeywordReply()返回的自动回复消息ID
     *                             contentText: 文本消息内容
     *                             contentImgTxt:图文消息ID
     *                             activeStatus: 最后回复类型(文本或者图文)
     * @return :errCode, errMsg
     */
    @Override
    public MsgBaseResult updateKeywordReply(UpdateKeywordReplyVO updateKeywordReplyVO) {
        KeywordReplyDO keywordReplyDO = new KeywordReplyDO();
        keywordReplyDO.setAppID(updateKeywordReplyVO.getAppID());
        keywordReplyDO.setEnterpriseAccount(updateKeywordReplyVO.getEnterpriseAccount());
        keywordReplyDO.setReplyMsgID(updateKeywordReplyVO.getReplyMsgID());
        keywordReplyDO.setRuleName(updateKeywordReplyVO.getRuleName());
        keywordReplyDO.setKeywordList(updateKeywordReplyVO.getKeywordList());
        keywordReplyDO.setContentImgTxtID(updateKeywordReplyVO.getContentImgTxtID());
        keywordReplyDO.setContentTxt(updateKeywordReplyVO.getContentTxt());
        keywordReplyDO.setKeywordList(updateKeywordReplyVO.getKeywordList());
        keywordReplyDO.setActiveReplyType(updateKeywordReplyVO.getActiveReplyType());
        int errCode = keywordReplyDAOImpl.updateKeywordReply(keywordReplyDO);
        if (0 != errCode) {
            return new MsgBaseResult(msgErrCode, "SQL FAIL");
        }else {
            return new MsgBaseResult(MsgCodeEnum.SUCCESS);
        }
    }

    /**
     * 查询指定服务号的默认回复
     *
     * @param enterpriseAccount
     * @param appID             msgID: createDefaultAutoReply()返回的自动回复消息ID
     *                          contentText: 文本消息内容
     */
    @Override
    public QueryKeywordReplyListResult queryKeywordReplyList(String enterpriseAccount, String appID) {
        List<KeywordReplyDO> keywordReplyDOList =  keywordReplyDAOImpl.queryKeywordReply(enterpriseAccount, appID);
        QueryKeywordReplyListResult queryKeywordReplyListResult =  new QueryKeywordReplyListResult(0,"OK", keywordReplyDOList);

        if (null == keywordReplyDOList || 0 == keywordReplyDOList.size()) {
            queryKeywordReplyListResult.setIsValid(false);
        }
        return queryKeywordReplyListResult;
    }
}
