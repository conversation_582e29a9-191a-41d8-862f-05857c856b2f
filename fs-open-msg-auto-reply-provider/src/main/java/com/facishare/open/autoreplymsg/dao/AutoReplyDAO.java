package com.facishare.open.autoreplymsg.dao;

import java.util.List;

/**
 * Created by fengyh on 2016/3/5.
 */
public interface AutoReplyDAO {

    /**
     *  返回 自动回复开关的值: 0-关闭，1-打开。
    */
    public int queryAutoReplySwitch(String appID, String enterpriseAccount);

    /**
     * 设置 自动回复开关的值:  0-关闭，1-打开。
    */
    public int setAutoReplySwitch(String appID, String enterpriseAccount, int status);

    /**
     * 获取一个用户的最后自动回复时间。返回0表示该用户没有被自动回复过. 返回的时间单位是秒, 使用unix时间戳。
    */
    public long getLastAutoReplyTime(String appID, String fsUserAccount);

    /**
     * 设置一个用户的最后自动回复时间。成功则返回0. lastReplyTime 单位是秒, 使用unix时间戳。
    */
    public int setLastAutoReplyTime(String appID, String fsUserAccount, long lastReplyTime);

    /**
     * 查询客服开关状态
     * */
    public int getCustomServiceReplySwitch(String enterpriseAccount, String appID);

    /**
     * 设置客服开关状态
     * */
    public int setCustomServiceReplySwitch(String appID, String enterpriseAccount, int switchState);

    /**
     * 获取一个企业下面，指定客服开关状态的应用列表.
     * 至少设置过一次客服开关的应用，才会出现在列表中。
     * */
    public List<String> getCustomServiceAppListBySwitch(String enterpriseAccount, int switchState);
}
