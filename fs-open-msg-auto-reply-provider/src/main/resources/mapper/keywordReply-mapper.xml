<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.facishare.open.autoreplymsg.dao.impl.KeywordReplyDAOImpl">

    <insert id="createKeywordReply" useGeneratedKeys="true" keyProperty="replyMsgID" parameterType="com.facishare.open.autoreplymsg.model.KeywordReplyDO">
        <selectKey resultType="java.lang.Long" keyProperty="replyMsgID" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into keyword_reply_content(rule_name, keyword, active_reply_type,content_txt,content_imgtxt_id, app_id, enterprise_account)
        values(#{ruleName}, #{keywordListJson}, #{activeReplyType},#{contentTxt},#{contentImgTxtID}, #{appID}, #{enterpriseAccount})
    </insert>

    <delete id="deleteKeywordReply" parameterType="com.facishare.open.autoreplymsg.model.KeywordReplyDO">
        DELETE FROM keyword_reply_content WHERE reply_id=#{replyMsgID} AND app_id=#{appID} AND enterprise_account=#{enterpriseAccount}
    </delete>

    <update id="updateKeywordReply" parameterType="com.facishare.open.autoreplymsg.model.KeywordReplyDO" >
        UPDATE keyword_reply_content SET   rule_name = #{ruleName},
         keyword=#{keywordListJson},
         active_reply_type=#{activeReplyType},
         content_txt=#{contentTxt},
         content_imgtxt_id = #{contentImgTxtID},
         app_id=#{appID},
         enterprise_account=#{enterpriseAccount}
        WHERE reply_id=#{replyMsgID} AND app_id=#{appID} AND enterprise_account=#{enterpriseAccount}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.KeywordReplyDO" id="queryKeywordReplyMap">
        <result column="reply_id" property="replyMsgID"/>
        <result column="rule_name" property="ruleName"/>
        <result column="keyword" property="keywordListJson"/>
        <result column="active_reply_type" property="activeReplyType"/>
        <result column="content_txt" property="contentTxt"/>
        <result column="content_imgtxt_id" property="contentImgTxtID"/>
    </resultMap>

    <select id="queryKeywordReply" parameterType="map" resultMap="queryKeywordReplyMap">
        SELECT reply_id,rule_name,keyword,active_reply_type,content_txt,content_imgtxt_id  FROM keyword_reply_content WHERE app_id=#{appID} AND enterprise_account=#{enterpriseAccount} ORDER BY reply_id DESC;
    </select>

    <select id="queryKeywordLastInsertReplyID" resultMap="queryKeywordReplyMap">
        SELECT max(reply_id) as reply_id FROM keyword_reply_content
    </select>

</mapper>