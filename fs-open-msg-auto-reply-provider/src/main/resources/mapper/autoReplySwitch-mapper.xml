<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.facishare.open.autoreplymsg.dao.impl.AutoReplyDAOImpl">

    <resultMap type="com.facishare.open.autoreplymsg.model.AutoReplyDO" id="queryAutoReplySwitchMap">
        <result column="status" property="status"/>
    </resultMap>

    <select id="queryAutoReplySwitch" parameterType="map" resultMap="queryAutoReplySwitchMap">
        SELECT status FROM reply_switch_status  WHERE app_id=#{appID} AND enterprise_account=#{enterpriseAccount}
    </select>

    <update id="setAutoReplySwitch"  parameterType="com.facishare.open.autoreplymsg.model.AutoReplyDO">
        INSERT INTO reply_switch_status(app_id, enterprise_account, status) VALUES(#{appID}, #{enterpriseAccount}, #{status}) ON DUPLICATE KEY UPDATE status=#{status}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.AutoReplyDO" id="queryLastAutoReplyTimeMap">
        <result column="last_reply_time" property="lastAutoReplyTime"/>
    </resultMap>

    <select id="queryLastAutoReplyTime" parameterType="map" resultMap="queryLastAutoReplyTimeMap">
        SELECT last_reply_time FROM reply_latest_time  WHERE app_id=#{appID} AND user=#{fsUserAccount}
    </select>

    <update id="setLastAutoReplyTime"  parameterType="com.facishare.open.autoreplymsg.model.AutoReplyDO">
        INSERT INTO reply_latest_time(app_id, user, last_reply_time) VALUES(#{appID}, #{fsUserAccount}, #{lastAutoReplyTime}) ON DUPLICATE KEY UPDATE last_reply_time=#{lastAutoReplyTime}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.AutoReplyDO" id="queryCustomServiceSwitchMap">
        <result column="status" property="status"/>
    </resultMap>

    <select id="queryCustomServiceSwitch" parameterType="map" resultMap="queryCustomServiceSwitchMap">
        SELECT status FROM customservice_switch_status  WHERE app_id=#{appID} AND enterprise_account=#{enterpriseAccount}
    </select>

    <update id="setCustomServiceSwitch"  parameterType="com.facishare.open.autoreplymsg.model.AutoReplyDO">
        INSERT INTO customservice_switch_status(app_id, enterprise_account, status) VALUES(#{appID}, #{enterpriseAccount}, #{status}) ON DUPLICATE KEY UPDATE status=#{status}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.AutoReplyDO" id="queryAppListMap">
        <result column="app_id" property="appID"/>
    </resultMap>

    <select id="getCustomServiceAppListBySwitch" parameterType="map" resultMap="queryAppListMap">
        SELECT app_id  FROM customservice_switch_status WHERE status=#{switchState} AND enterprise_account=#{enterpriseAccount};
    </select>

</mapper>
