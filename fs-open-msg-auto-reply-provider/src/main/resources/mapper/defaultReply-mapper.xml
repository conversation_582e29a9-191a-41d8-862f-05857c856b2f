<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.facishare.open.autoreplymsg.dao.impl.DefaultReplyDAOImpl">

    <insert id="createDefaultReply"  useGeneratedKeys="true" keyProperty="replyMsgID" parameterType="com.facishare.open.autoreplymsg.model.DefaultReplyDO">

        <selectKey resultType="java.lang.Long" keyProperty="replyMsgID" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>

        insert into default_reply_content(active_reply_type,content_txt,content_imgtxt_id, app_id, enterprise_account)
        values(#{activeReplyType},#{contentTxt},#{contentImgTxtID}, #{appID}, #{enterpriseAccount})

    </insert>

    <delete id="deleteDefaultReply" parameterType="com.facishare.open.autoreplymsg.model.DefaultReplyDO">
        DELETE FROM default_reply_content WHERE  app_id=#{appID} AND enterprise_account=#{enterpriseAccount} AND reply_id=#{replyMsgID}
    </delete>

    <update id="updateDefaultReply" parameterType="com.facishare.open.autoreplymsg.model.DefaultReplyDO" >
        UPDATE default_reply_content SET  active_reply_type=#{activeReplyType},
        content_txt=#{contentTxt},
        content_imgtxt_id = #{contentImgTxtID},
        app_id=#{appID},
        enterprise_account=#{enterpriseAccount}
        WHERE app_id=#{appID} AND enterprise_account=#{enterpriseAccount} AND reply_id=#{replyMsgID}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.DefaultReplyDO" id="queryDefaultReplyMap">
        <result column="reply_id" property="replyMsgID"/>
        <result column="active_reply_type" property="activeReplyType"/>
        <result column="content_txt" property="contentTxt"/>
        <result column="content_imgtxt_id" property="contentImgTxtID"/>
    </resultMap>

    <select id="queryDefaultReply" parameterType="map"  resultMap="queryDefaultReplyMap">
        SELECT reply_id, active_reply_type,content_txt,content_imgtxt_id FROM default_reply_content WHERE app_id=#{appID} AND enterprise_account=#{enterpriseAccount} ORDER BY reply_id ASC;
    </select>

    <select id="queryLastInsertReplyID" resultMap="queryDefaultReplyMap">
        SELECT max(reply_id) as reply_id FROM default_reply_content
    </select>

</mapper>