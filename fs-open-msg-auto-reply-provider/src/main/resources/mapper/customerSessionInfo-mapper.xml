<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.facishare.open.autoreplymsg.dao.impl.CustomerSessionInfoDAOImpl">

    <insert id="createCustomerSessionInfo" keyProperty="id" useGeneratedKeys="true" parameterType="com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into customer_session_info(app_id, enterprise_account,customer_name,customer_icon, customer_list, gmt_create, gmt_modified)
        values(#{appId}, #{enterpriseAccount},#{customerName},#{customerIcon}, #{customerList}, #{gmtCreate}, #{gmtModified})
    </insert>

    <update id="updateCustomerSessionInfo" parameterType="com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO" >
        UPDATE customer_session_info SET
         customer_name=#{customerName},
         customer_icon=#{customerIcon},
         customer_list = #{customerList},
         gmt_modified=#{gmtModified}
        WHERE id=#{id}
    </update>

    <resultMap type="com.facishare.open.autoreplymsg.model.CustomerSessionInfoDO" id="queryCustomerSessionMap">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="enterprise_account" property="enterpriseAccount"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_icon" property="customerIcon"/>
        <result column="customer_list" property="customerList"/>
    </resultMap>

    <select id="queryCustomerSessionInfo" parameterType="map" resultMap="queryCustomerSessionMap">
        SELECT * FROM customer_session_info WHERE app_id=#{appId} AND enterprise_account=#{enterpriseAccount}
    </select>

</mapper>
