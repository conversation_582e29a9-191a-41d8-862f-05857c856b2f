<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd"
       default-lazy-init="false">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.autoreplymsg"/>

    <import resource="classpath:/springreply/spring-dubbo-auto-reply-config.xml"/>
    <import resource="classpath:/springreply/fs-open-msg-auto-reply-provider.xml"/>
    <import resource="classpath:/springreply/fs-open-msg-auto-reply-consumer.xml"/>
    <import resource="classpath:/springreply/spring-db.xml"/>
    <import resource="classpath:/springreply/spring-aop.xml"/>

    <!--企业互联rest API-->
    <import resource="classpath:enterpriserelation2/enterpriserelation.xml"/>

    <!--监控及日志中心-->
<!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>-->
</beans>
