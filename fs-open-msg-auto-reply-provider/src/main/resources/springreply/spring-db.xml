<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/aop
			http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
			http://www.springframework.org/schema/tx
			http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
			http://www.springframework.org/schema/mvc
			http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <bean id="msg_dataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="db-open-msg"/>
    </bean>

    <bean id="msg_sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="msg_dataSource" />
        <property name="typeAliasesPackage" value="com.facishare.open.storage.model" />
        <property name="configLocation" value="classpath:springreply/spring-mybatis.xml" />
        <property name="mapperLocations">
            <array>
                <value>classpath*:/mapper/autoReplySwitch-mapper.xml</value>
                <value>classpath*:/mapper/customerSessionInfo-mapper.xml</value>
                <value>classpath*:/mapper/defaultReply-mapper.xml</value>
                <value>classpath*:/mapper/keywordReply-mapper.xml</value>
            </array>
        </property>
    </bean>

    <bean id="msg_jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource">
            <ref bean="msg_dataSource" />
        </property>
    </bean>

    <!--war包合并后这里去掉，保留其它地方的-->
<!--    <bean id="mongoSource" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="open-msg-mongo"/>-->

    <!-- 事务管理器   -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="msg_dataSource" />
    </bean>
    
    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />

</beans>