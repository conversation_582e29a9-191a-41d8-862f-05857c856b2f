<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 生产者      -->
    <dubbo:service interface="com.facishare.open.autoreplymsg.service.MsgAutoReplyService" ref="msgAutoReplyServiceImpl"
                   protocol="dubbo" version="1.0" retries="2"/>
    <dubbo:service interface="com.facishare.open.autoreplymsg.service.MsgCustomerService" ref="msgCustomerServiceImpl"
                   protocol="dubbo" version="1.0" retries="2"/>
    <dubbo:service interface="com.facishare.open.autoreplymsg.service.MsgDefaultReplyService" ref="msgDefaultReplyServiceImpl"
                   protocol="dubbo" version="1.0" retries="2"/>
    <dubbo:service interface="com.facishare.open.autoreplymsg.service.MsgKeywordReplyService" ref="msgKeywordReplyServiceImpl"
                   protocol="dubbo" version="1.0" retries="2"/>

</beans>