//package com.facishare.open.msg.test;
//
//
//import com.facishare.open.msg.export.model.ExportMsgResultVO;
//import com.facishare.open.msg.export.result.ExportMsgResult;
//import com.facishare.open.msg.export.service.ExportMsgService;
//import com.facishare.open.msg.service.MessageExhibitionService;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.MockitoAnnotations;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import javax.annotation.Resource;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * fte2消息导出excel单元测试
// * Created by zhong<PERSON> on 2016/5/26.
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:spring/union-fs-export.xml")
//public class ExcelTest {
//
//    @Resource
//    private ExportMsgService exportMsgService;
//
//    @Autowired
//    private MessageExhibitionService messageExhibitionService;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testInterface() {
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("appId", "FSAID_1313e5f");
//        paramMap.put("ea", "fsfte2a");
//        paramMap.put("starMark", null);
//        LocalDate localDate = LocalDate.now();
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//        String dateStr = localDate.format(formatter);
//        String fileName = "test".concat("-聊天记录-").concat(dateStr);
//        ExportMsgResult<ExportMsgResultVO> exportMsgResult = exportMsgService.exportMsgToExcel(null, null, "test",
//                paramMap);
//        Assert.assertEquals(fileName, exportMsgResult.getData().getFileName());
//        //写文件
//        ExportMsgServiceImplTest.writeData(exportMsgResult.getData().getBytes());
//    }
//}