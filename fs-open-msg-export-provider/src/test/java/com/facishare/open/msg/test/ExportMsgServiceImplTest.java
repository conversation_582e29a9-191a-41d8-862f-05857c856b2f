package com.facishare.open.msg.test;


import com.facishare.open.msg.export.model.ExportMsgResultVO;
import com.facishare.open.msg.export.result.ExportMsgResult;
import com.facishare.open.msg.export.service.impl.ExportMsgServiceImpl;
import com.facishare.open.msg.model.ExportMsgVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息导出excel单元测试
 * Created by zhongcy on 2016/5/26.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/test-fs-export.xml")
@Ignore
public class ExportMsgServiceImplTest {

    Long beginTime = System.currentTimeMillis();
    Long endTime = System.currentTimeMillis();
    Map<String, Object> paramMap = null;

    @InjectMocks
    @Resource
    private ExportMsgServiceImpl exportMsgService;

    @InjectMocks
    @Autowired
    private MessageExhibitionService messageExhibitionService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试没有查找到数据
     */
    @Test
    public void tesExportMsgService() throws IOException {
        //1.模拟调用查找服务
        MessageExhibitionResult<List<ExportMsgVO>> messageExhibitionResult = new MessageExhibitionResult<>();
        Mockito.when(messageExhibitionService
                .queryAllExportMessages(beginTime,
                        endTime, paramMap)).thenReturn(messageExhibitionResult);

        LocalDate localDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateStr = localDate.format(formatter);
        String fileName = "test".concat("-聊天记录-").concat(dateStr);
        ExportMsgResult<ExportMsgResultVO> exportMsgResult = exportMsgService.exportMsgToExcel(beginTime, endTime, "test", paramMap);
        Assert.assertEquals(fileName, exportMsgResult.getData().getFileName());
        Assert.assertEquals(0,exportMsgResult.getData().getMsgAmount());
        //写文件
        writeData(exportMsgResult.getData().getBytes());
    }

    /**
     * 测试查找到数据
     */
    @Test
    public void tesExportMsgServiceWithData() throws IOException {
        //1.模拟调用查找服务
        ExportMsgVO exportMsgVO1 = new ExportMsgVO();
        exportMsgVO1.setSessionId("1");
        exportMsgVO1.setCreateTime(System.currentTimeMillis());
        exportMsgVO1.setSessionUserName("黄虎平");
        exportMsgVO1.setContent("测试消息1");
        exportMsgVO1.setSenderName("(客服自动回复)");

        ExportMsgVO exportMsgVO2 = new ExportMsgVO();
        exportMsgVO2.setSessionId("1");
        exportMsgVO2.setCreateTime(System.currentTimeMillis());
        exportMsgVO2.setSessionUserName("黄虎平");
        exportMsgVO2.setContent("测试消息2");
        exportMsgVO2.setSenderName("小助手");

        ExportMsgVO exportMsgVO3 = new ExportMsgVO();
        exportMsgVO3.setSessionId("2");
        exportMsgVO3.setCreateTime(System.currentTimeMillis());
        exportMsgVO3.setSessionUserName("钟乘永");
        exportMsgVO3.setContent("测试消息3\n\r请选择\n\r1.\n\r2.\n\r3.");
        exportMsgVO3.setSenderName("小助手");

        ExportMsgVO exportMsgVO4 = new ExportMsgVO();
        exportMsgVO4.setSessionId("2");
        exportMsgVO4.setCreateTime(System.currentTimeMillis());
        exportMsgVO4.setSessionUserName("钟乘永");
        exportMsgVO4.setContent("测试消息4");
        exportMsgVO4.setSenderName("小助手");

        List<ExportMsgVO> voList = new ArrayList<>();
        voList.add(exportMsgVO1);
        voList.add(exportMsgVO2);
//        for(int i=0;i<500;i++){
//            ExportMsgVO exportMsgVO = new ExportMsgVO();
//            exportMsgVO.setSessionId("2");
//            exportMsgVO.setCreateTime(System.currentTimeMillis());
//            exportMsgVO.setSessionUserName("钟乘永");
//            exportMsgVO.setContent(i+"----循环测试测试消息");
//            exportMsgVO.setSenderName("小助手");
//            voList.add(exportMsgVO);
//        }
        voList.add(exportMsgVO3);
        voList.add(exportMsgVO4);

        ExportMsgVO exportMsgVO = new ExportMsgVO();
        exportMsgVO.setSessionId("3");
        exportMsgVO.setCreateTime(System.currentTimeMillis());
        exportMsgVO.setSessionUserName("袁杰");
        exportMsgVO.setContent("循环测试测试消息");
        exportMsgVO.setSenderName("自动回复");
        voList.add(exportMsgVO);
        MessageExhibitionResult<List<ExportMsgVO>> messageExhibitionResult = new MessageExhibitionResult<>();
        messageExhibitionResult.setData(voList);

        Mockito.when(messageExhibitionService.queryAllExportMessages(beginTime,
                        endTime, paramMap)).thenReturn(messageExhibitionResult);

        LocalDate localDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateStr = localDate.format(formatter);
        String fileName = "test".concat("-聊天记录-").concat(dateStr);
        ExportMsgResult<ExportMsgResultVO> exportMsgResult = exportMsgService.exportMsgToExcel(beginTime, endTime, "test", paramMap);
        Assert.assertEquals(fileName, exportMsgResult.getData().getFileName());
        Assert.assertEquals(voList.size(),exportMsgResult.getData().getMsgAmount());
        //写文件
        writeData(exportMsgResult.getData().getBytes());
    }

    /**
     * @param bytes 二进制流
     */
    public static void writeData(byte[] bytes) {
        //二进制文件导出excel
        File file = new File("D://workbook.xls");
        FileOutputStream outputStream;
        try {
            outputStream = new FileOutputStream(file);
            outputStream.write(bytes);
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}