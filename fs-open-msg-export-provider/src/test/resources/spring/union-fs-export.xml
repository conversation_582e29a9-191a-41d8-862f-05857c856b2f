<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context
         http://www.springframework.org/schema/context/spring-context.xsd
         http://code.alibabatech.com/schema/dubbo
         http://code.alibabatech.com/schema/dubbo/dubbo.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd" default-lazy-init ="true">

    <context:annotation-config />

<!--    <context:component-scan base-package="com.facishare.open.msg.export.service.impl"/>-->

    <!--dubbo consumer-->
    <dubbo:application name="testConsumer" />
    <dubbo:reference id="messageExhibitionService" interface="com.facishare.open.msg.service.MessageExhibitionService" protocol="dubbo" version="1.0"/>
    <dubbo:reference id="exportMsgService" interface="com.facishare.open.msg.export.service.ExportMsgService"
                     protocol="dubbo" version="1.0"/>
    <dubbo:registry address="zookeeper://**************:4180"/>
</beans>
