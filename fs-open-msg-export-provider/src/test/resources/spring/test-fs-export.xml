<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context
         http://www.springframework.org/schema/context/spring-context.xsd
         http://code.alibabatech.com/schema/dubbo
         http://code.alibabatech.com/schema/dubbo/dubbo.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd" default-lazy-init ="true">

    <context:annotation-config />

    <context:component-scan base-package="com.facishare.open.msg.export.service.impl"/>

    <!-- mock rcp services -->
    <bean id="messageExhibitionService" name="messageExhibitionService" class="org.mockito.Mockito" factory-method="mock">
        <constructor-arg value="com.facishare.open.msg.service.MessageExhibitionService"/>
    </bean>

</beans>
