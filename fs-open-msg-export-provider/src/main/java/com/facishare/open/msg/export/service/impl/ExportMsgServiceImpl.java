package com.facishare.open.msg.export.service.impl;

import com.facishare.open.msg.export.model.ExportMsgResultVO;
import com.facishare.open.msg.export.result.ExportMsgResult;
import com.facishare.open.msg.export.service.ExportMsgService;
import com.facishare.open.msg.model.ExportMsgVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.open.msg.service.MessageExhibitionService;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息记录导出到excel实现类
 * Created by huanghp on 2016/05/24.
 */
// IgnoreI18nFile
@Service("exportMsgService")
public class ExportMsgServiceImpl implements ExportMsgService {

    public static final Logger logger = LoggerFactory.getLogger(ExportMsgServiceImpl.class);

    private static final String EXTNAME = "xls";

    @Autowired
    private MessageExhibitionService messageExhibitionService;

    @Override
    public ExportMsgResult<ExportMsgResultVO> exportMsgToExcel(Long beginTime, Long endTime, String
            appName, Map<String, Object> paramMap) {
        MessageExhibitionResult<List<ExportMsgVO>> messageExhibitionResult = messageExhibitionService
                .queryAllExportMessages(beginTime, endTime, paramMap);
        return this.exportMsgToExcel(messageExhibitionResult, appName);
    }
    

    @Override
	public ExportMsgResult<ExportMsgResultVO> exportCrossMsgToExcel(Long beginTime, Long endTime, String appName, Map<String, Object> paramMap) {

         MessageExhibitionResult<List<ExportMsgVO>> messageExhibitionResult = messageExhibitionService.queryAllCrossExportMessages(beginTime, endTime, paramMap);
         return this.exportMsgToExcel(messageExhibitionResult, appName);
	}
    
    private ExportMsgResult<ExportMsgResultVO> exportMsgToExcel(MessageExhibitionResult<List<ExportMsgVO>> messageExhibitionResult, String appName) {
    	 ExportMsgResult<ExportMsgResultVO> exportMsgResult = null;
    	 if (messageExhibitionResult.getErrorCode() != MsgCodeEnum.SUCCESS.getErrorCode()) {
             logger.error("exportMsgToExcel,appName={},errorMsg={}", appName,messageExhibitionResult.getErrMessage());
             exportMsgResult = new ExportMsgResult<>();
             exportMsgResult.setErrCode(messageExhibitionResult.getErrCode());
             exportMsgResult.setErrorMsg(messageExhibitionResult.getErrMessage());
             return exportMsgResult;
         }

         LocalDate localDate = LocalDate.now();
         DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
         String dateStr = localDate.format(formatter);

         String fileName = appName.concat("-聊天记录-").concat(dateStr);

         return transferToExcel(messageExhibitionResult.getData(), fileName);
    }
    


	/**
     * 将传入的list按照sessionId分类，写入到表格中
     * 每条消息占一个单元格
     * @param list     查询结果
     * @param fileName 文件名称
     * @return 导出结果
     */
    protected ExportMsgResult<ExportMsgResultVO> transferToExcel(List<ExportMsgVO> list, String fileName) {
        Map<String, List<ExportMsgVO>> sessionMap = new LinkedHashMap<>();
        int rowNum = 1;
        int msgAmount = 0;
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(fileName);

        //创建换行样式
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_TOP);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);

        //创建样式，cell有下边框，用于隔离接收人的消息
        HSSFCellStyle allBorder = workbook.createCellStyle();
        allBorder.setWrapText(true);
        allBorder.setAlignment(HSSFCellStyle.ALIGN_LEFT);
        allBorder.setVerticalAlignment(HSSFCellStyle.VERTICAL_TOP);
        allBorder.setBorderLeft(CellStyle.BORDER_THIN);
        allBorder.setBorderRight(CellStyle.BORDER_THIN);
        allBorder.setBorderBottom(CellStyle.BORDER_THIN);

        //创建样式，顶部标题样式
        HSSFCellStyle topStyle = workbook.createCellStyle();
        topStyle.setWrapText(true);
        topStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        topStyle.setAlignment(HSSFCellStyle.VERTICAL_JUSTIFY);
        topStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        topStyle.setBorderLeft(CellStyle.BORDER_THIN);
        topStyle.setBorderRight(CellStyle.BORDER_THIN);
        topStyle.setBorderBottom(CellStyle.BORDER_THIN);

        //创建字体样式，加粗
        HSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.CORNFLOWER_BLUE.index);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);

        //设置宽度，根据当前需求设置默认宽度如下
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 40000);

        //创建表头
        Row row = sheet.createRow((short) 0);
        row.createCell(0).setCellValue("聊天对象");
        row.createCell(1).setCellValue("聊天记录");
        row.getCell(0).setCellStyle(topStyle);
        row.getCell(1).setCellStyle(topStyle);
        ByteArrayOutputStream os;
        byte[] bytes;
        ExportMsgResultVO exportMsgResultVO;
        try {
            if (list != null && list.size() > 0) {
                msgAmount=list.size();
                StringBuffer sb;
                for (ExportMsgVO exportMsgVO : list) {
                    //循环list获取map，key是sessionId，值是exportMsgVOList
                    if (sessionMap.keySet().contains(exportMsgVO.getSessionId())) {
                        sessionMap.get(exportMsgVO.getSessionId()).add(exportMsgVO);
                    } else {
                        List<ExportMsgVO> exportMsgVOList = new ArrayList<>();
                        exportMsgVOList.add(exportMsgVO);
                        sessionMap.put(exportMsgVO.getSessionId(), exportMsgVOList);
                    }
                }
                //循环map
                for (String sessionId : sessionMap.keySet()) {
                    int startRow = rowNum;
                    List<ExportMsgVO> exportMsgVOList = sessionMap.get(sessionId);
                    Row addRow = null;
                    for (int i = exportMsgVOList.size() - 1; i >= 0; i--) {
                        sb = new StringBuffer();
                        addRow = sheet.createRow(rowNum);
                        HSSFRichTextString richTextString;
                        ExportMsgVO exportMsgVO = exportMsgVOList.get(i);
                        String sessionUserName = exportMsgVOList.get(0).getSessionUserName();
                        if(StringUtils.isNotEmpty(sessionUserName) && StringUtils.isNotEmpty(exportMsgVOList.get(0).getEaName())){
                        	sessionUserName = sessionUserName.concat("-").concat(exportMsgVOList.get(0).getEaName());
                        }
                        if (exportMsgVO.isUplink()) {//是否上行
                            sb.append(exportMsgVO.getSenderName()).append("  ").append(exportMsgVO.getCreateTimeDesc()).
                                    append("\n").append(exportMsgVO.getContent()).append("\n");
                            int position = sb.indexOf("\n");
                            richTextString = new HSSFRichTextString(sb.toString());
                            richTextString.applyFont(0, position, font);
                            addRow.createCell(0).setCellValue(sessionUserName);
                            addRow.createCell(1).setCellValue(richTextString);
                            addRow.getCell(0).setCellStyle(cellStyle);
                            addRow.getCell(1).setCellStyle(cellStyle);
                            rowNum++;
                        } else {
                            sb.append(exportMsgVO.getAdminUserName()).append(exportMsgVO.getSendTypeName()).append("  ")
                                    .append(exportMsgVO.getCreateTimeDesc()).append("\n")
                                    .append(exportMsgVO.getContent()).append("\n");
                            int position = sb.indexOf("\n");
                            richTextString = new HSSFRichTextString(sb.toString());
                            richTextString.applyFont(0, position, font);
                            addRow.createCell(0).setCellValue(sessionUserName);
                            addRow.createCell(1).setCellValue(richTextString);
                            addRow.getCell(0).setCellStyle(cellStyle);
                            addRow.getCell(1).setCellStyle(cellStyle);
                            rowNum++;
                        }
                    }
                    //超过一行，合并单元格
                    if (startRow != rowNum) {
                        CellRangeAddress cra = new CellRangeAddress(startRow, rowNum - 1, 0, 0);
                        sheet.addMergedRegion(cra);
                    }
                    if (addRow != null) {
                        addRow.getCell(0).setCellStyle(allBorder);
                        addRow.getCell(1).setCellStyle(allBorder);
                    }
                }
            }

            os = new ByteArrayOutputStream();
            try {
                workbook.write(os);
            } catch (IOException e) {
                logger.error("transferToExcel:", e);
            }
            bytes = os.toByteArray();
            exportMsgResultVO = new ExportMsgResultVO(bytes, fileName, EXTNAME,msgAmount);
            return new ExportMsgResult<>(exportMsgResultVO);
        } catch (Exception e) {//处理导出业务过程中所有的异常，避免传null值到上层
            logger.error("transferToExcel:", e);
            os = new ByteArrayOutputStream();
            try {
                workbook.write(os);
            } catch (IOException ie) {
                logger.error("transferToExcel:", ie);
            }
            bytes = os.toByteArray();
            exportMsgResultVO = new ExportMsgResultVO(bytes, fileName, EXTNAME,msgAmount);
            return new ExportMsgResult<>(exportMsgResultVO);
        }
    }
}
