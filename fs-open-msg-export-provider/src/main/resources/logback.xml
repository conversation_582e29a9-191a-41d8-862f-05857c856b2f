<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder  class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%X{traceId}]-[%X{consumeProcessName}]-[%class:%line] - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="ROLLING-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.base}/logs/msgExport.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/msgExport-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder  class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%X{traceId}]-[%X{consumeProcessName}]-[%class:%line] - %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.facishare" level="DEBUG" additivity="false">
        <appender-ref ref="ROLLING-FILE"/>
<!--        <appender-ref ref="STDOUT"/>-->
    </logger>
    <root level="ERROR">
        <appender-ref ref="ROLLING-FILE" />
    </root>
</configuration>