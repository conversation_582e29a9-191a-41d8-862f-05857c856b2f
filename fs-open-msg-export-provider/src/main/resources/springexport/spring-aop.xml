<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-3.1.xsd">

    <bean id="timeCostProfiler" class="com.facishare.open.msg.export.aop.LogAspect"/>

    <!--蜂眼监控-->
    <aop:aspectj-autoproxy/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler" />
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler" >
            <aop:pointcut id="pointCutAround" expression="execution(* com.facishare.open.msg.export.service.impl.*.*(..))" />
            <aop:around method="profile" pointcut-ref="pointCutAround" />
        </aop:aspect>
    </aop:config>

</beans>