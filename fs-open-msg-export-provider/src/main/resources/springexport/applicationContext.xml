<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	   http://www.springframework.org/schema/beans/spring-beans.xsd
	   http://www.springframework.org/schema/context
	   http://www.springframework.org/schema/context/spring-context.xsd"
       default-lazy-init="false">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.msg.export"/>

    <import resource="classpath:/springexport/fs-open-msg-export-provider.xml"/>
    <import resource="classpath:/springexport/fs-open-msg-export-consumer.xml"/>
    <import resource="classpath:/springexport/spring-dubbo-msg-export-config.xml"/>
    <import resource="classpath:/springexport/spring-aop.xml"/>

</beans>
