<?xml version="1.0" encoding="UTF-8"?>
 <project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

   <parent>
    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-msg</artifactId>
    <version>2.0.0</version>
  </parent>
    <artifactId>fs-open-msg-export-provider</artifactId>
    <packaging>jar</packaging>
    <version>2.0.0-SNAPSHOT</version>
    <description>msg export service provider</description>

    <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

  <dependencies>
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-msg-api</artifactId>
          <version>0.0.19-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>jedis</artifactId>
                  <groupId>redis.clients</groupId>
              </exclusion>
              <exclusion>
                  <groupId>commons-pool</groupId>
                  <artifactId>commons-pool</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>org.mongodb</groupId>
                  <artifactId>mongo-java-driver</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>org.mongodb.morphia</groupId>
                  <artifactId>morphia</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
        <!-- msg export -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-export-api</artifactId>
            <version>0.0.2</version>
        </dependency>

      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi</artifactId>
      </dependency>

      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
      </dependency>

      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-context</artifactId>
      </dependency>

      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
      </dependency>

      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>config-core</artifactId>
      </dependency>

      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>spring-support</artifactId>
      </dependency>



      <dependency>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>org.slf4j</groupId>
                  <artifactId>slf4j-log4j12</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-all</artifactId>
          <version>1.10.19</version>
          <scope>test</scope>
      </dependency>
  </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>