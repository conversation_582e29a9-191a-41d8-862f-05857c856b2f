<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-msg</artifactId>
    <packaging>pom</packaging>
    <version>2.0.0</version>


    <url>https://maven.foneshare.cn/</url>
    <description></description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <spring-framework.version>4.3.21.RELEASE</spring-framework.version>
        <logback.version>1.1.1</logback.version>
        <slf4j.version>1.7.36</slf4j.version>
        <account-converter.version>1.1-SNAPSHOT</account-converter.version>
    </properties>

   
    <modules>
        <module>fs-open-msg-api</module>
        <module>fs-open-msg-common</module>
        <module>fs-open-msg-auto-reply-api</module>
        <module>fs-open-msg-export-api</module>
        <module>fs-open-msg-auto-reply-provider</module>
        <module>fs-open-msg-export-provider</module>
        <module>fs-open-msg-provider</module>
        <module>fs-open-msg-web</module>
    </modules>


    <dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>

        <!-- GSon -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring-framework.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!-- spring   -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <dependency>
            <artifactId>config-core</artifactId>
            <groupId>com.github.colin-lee</groupId>
            <version>7.5.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-utils</artifactId>
            <version>0.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>gray-release</artifactId>
            <version>1.0.4</version>
        </dependency>


        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-servicelib-fsi</artifactId>
            <version>1.3-PROTO-1.3.7-SNAPSHOT</version>
        </dependency>


        <!-- Javax servlet  -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
        </dependency>

        <!--poi excel-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.7</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-message-event</artifactId>
            <version>0.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-callback-api</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-custom-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- fs-open-app-center-api -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-center-api</artifactId>
            <version>1.0.41-SNAPSHOT</version>
        </dependency>

        <!-- 素材库服务 -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-material-api</artifactId>
            <version>1.0.38</version>
        </dependency>

        <!-- 埋点 -->
        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>datapersist</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-baichuan-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-proxy-core-api</artifactId>
            <version>0.1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-result</artifactId>
            <version>0.0.6</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>4.3.21.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-oauth-base-api</artifactId>
            <version>0.0.31-SNAPSHOT</version>
        </dependency>

        <!-- fs qixin -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>0.1.1-SNAPSHOT</version>
            <exclusions>
            	<exclusion>
            		<groupId>org.mongodb</groupId>
            		<artifactId>mongo-java-driver</artifactId>
            	</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-storage</artifactId>
            <version>0.0.5</version>
        </dependency>



        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.16</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    	<dependency>
               <groupId>com.facishare.qixin.plugin</groupId>
               <artifactId>fs-qixin-plugin-api</artifactId>
               <version>0.0.1-SNAPSHOT</version>
    	</dependency>
    	<dependency>
		    <groupId>com.facishare.open</groupId>
		    <artifactId>fs-wechat-union-core-api</artifactId>
		    <version>0.1.0-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.27.0-GA</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-configuration2</artifactId>
            <version>2.9.0</version>
        </dependency>

        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>1.10</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>mybatis-spring</artifactId>
            <groupId>org.mybatis</groupId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-auto-reply-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-auto-reply-provider</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-export-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-export-provider</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-common</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-msg-provider</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>
    </dependencyManagement>



    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals><goal>jar</goal></goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>