package com.facishare.open.autoreplymsg.result;

import com.facishare.open.autoreplymsg.constant.AutoReplyMsgTypeEnum;
import com.facishare.open.autoreplymsg.constant.AutoReplyTypeEnum;
import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/4.
 *
 * 消息中心消息上行时，调用此接口判断是否需要自动回复。
 */
public class GetAutoReplyResult extends MsgBaseResult {

    private static final long serialVersionUID = -6782677830237613774L;
    //needReply：true-需要做自动回复， false-不需要做自动回复.
    /*
    replyType如果是TXT, 用replyTxt回复用户。
    replyType如果是IMGTXT, 用素材ID编号replyImgTxtID回复用户。
     */
    private boolean needReply = false;
    private String replyTxt = null;
    private String replyImgTxtID = null;
    private AutoReplyMsgTypeEnum replyType = AutoReplyMsgTypeEnum.TXT;

    //回复的类型：默认自动回复或者关键字回复
    private AutoReplyTypeEnum type = AutoReplyTypeEnum.REPLY_DEFAULT;

    public AutoReplyTypeEnum getType() {
        return type;
    }

    public void setType(AutoReplyTypeEnum type) {
        this.type = type;
    }

    public GetAutoReplyResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public AutoReplyMsgTypeEnum getReplyType() {
        return replyType;
    }

    public void setReplyType(AutoReplyMsgTypeEnum replyType) {
        this.replyType = replyType;
    }

    public String getReplyTxt() {
        return replyTxt;
    }

    public void setReplyTxt(String replyTxt) {
        this.replyTxt = replyTxt;
    }

    public String getReplyImgTxtID() {
        return replyImgTxtID;
    }

    public void setReplyImgTxtID(String replyImgTxtID) {
        this.replyImgTxtID = replyImgTxtID;
    }

    public boolean isNeedReply() {
        return needReply;
    }

    public void setNeedReply(boolean needReply) {
        this.needReply = needReply;
    }

    @Override
    public String toString() {
        return "GetAutoReplyResult{" +
                "needReply=" + needReply +
                ", replyTxt='" + replyTxt + '\'' +
                ", replyImgTxtID='" + replyImgTxtID + '\'' +
                ", replyType=" + replyType +
                ", type=" + type +
                '}';
    }
}
