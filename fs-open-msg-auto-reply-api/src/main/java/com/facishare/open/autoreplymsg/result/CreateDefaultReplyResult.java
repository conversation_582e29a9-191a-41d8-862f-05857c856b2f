package com.facishare.open.autoreplymsg.result;

import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/1.
 */
public class CreateDefaultReplyResult extends MsgBaseResult {

    private static final long serialVersionUID = 4522472962230881740L;
    //自动回复编号
    private long replyMsgID;

    public CreateDefaultReplyResult(int errCode, String errMsg) {
        super(errCode, errMsg);
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }

    @Override
    public String toString() {
        return "CreateDefaultReplyResult{" +
                "replyMsgID=" + replyMsgID +
                '}';
    }
}