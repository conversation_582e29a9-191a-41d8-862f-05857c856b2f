package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 微联服务号二级session信息设置参数
 * <AUTHOR>
 * @date 2017-12-25
 */
public class WorkbenchSessionQueryVO implements Serializable {

    private static final long serialVersionUID = -6426718909462543399L;
    
    /**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 工作消息类型  1.工单  2.问卷  3.评论,  4.微信客服   5.外联服务号的外部联系人, 7-服务工单
     */
    private int workbenchType;
    

    /**   0-多客服， 1-微客服,2-互联客服   */
    private int sessionType = 0;

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getWorkbenchType() {
        return workbenchType;
    }

    public void setWorkbenchType(int workbenchType) {
        this.workbenchType = workbenchType;
    }
    
    @Override
    public String toString() {
        return "WorkbenchSessionVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", workbenchType=" + workbenchType +
                ", sessionType=" + sessionType +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
        
        Preconditions.checkArgument(0 == sessionType || 1 == sessionType || 2==sessionType, "sessionType is illegal");
    }
}
