package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/3/4.
 *
 * 创建关键词自动回复的入参类
 */
public class CreateKeywordReplyVO implements Serializable {

    private static final long serialVersionUID = -3652520944184850393L;
    //appID 开放平台AppID
    private String appID;
    //企业账号是 E.enterpriseAccount.员工ID 中间这一部分
    private String enterpriseAccount;

    //当前回复消息的状态，合法值： 0-文本，1-图文
    private int activeReplyType;
    //自动回复中的文本回复
    private String contentTxt;
    //素材库中的图文素材ID
    private String contentImgTxtID;
       //规则名
    private String ruleName;
    //关键词+包含关系 json字符串
    List<KeywordTypeInfo> keywordList;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public CreateKeywordReplyVO() {
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public List<KeywordTypeInfo> getKeywordList() {
        return keywordList;
    }

    public void setKeywordList(List<KeywordTypeInfo> keywordList) {
        this.keywordList = keywordList;
    }

    @Override
    public String toString() {
        return "CreateKeywordReplyVO{" +
                "appID='" + appID + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", activeReplyType=" + activeReplyType +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxtID='" + contentImgTxtID + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", keywordList=" + keywordList +
                '}';
    }
}
