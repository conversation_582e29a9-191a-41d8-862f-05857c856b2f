package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置微联服务号工作台一级session数据信息
 * <AUTHOR>
 * @date 2018-4-4
 */
public class UpdateWXPlatformExtraDataMapArg implements Serializable {
	
	private static final long serialVersionUID = -8746375263100582438L;

	/**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 客服人员列表
     */
    private List<Integer> customerIds;
    
    /**
     * 扩展数据信息
     */
    private Map<String, String> extraDataMap;
    
    
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Map<String, String> getExtraDataMap() {
		return extraDataMap;
	}

	public void setExtraDataMap(Map<String, String> extraDataMap) {
		this.extraDataMap = extraDataMap;
	}

	public List<Integer> getCustomerIds() {
		return customerIds;
	}

	public void setCustomerIds(List<Integer> customerIds) {
		this.customerIds = customerIds;
	}

	@Override
    public String toString() {
        return "UpdateWXCustomerExtraDataMapArg{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", customerIds=" + customerIds +
                ", extraDataMap=" + extraDataMap +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
    }
}
