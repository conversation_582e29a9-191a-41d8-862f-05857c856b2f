package com.facishare.open.autoreplymsg.service;

import java.util.List;

import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceAppListResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceSwitchResult;
import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/2.
 *
 * 查询和设置自动回复的状态，
 * 消息中心收到上行消息时查询自动回复的内容
 */
public interface MsgAutoReplyService {

     /**
     * 查询自动回复开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @return : 0-关闭， 1-打开
     */
    public int queryAutoReplySwitch(String enterpriseAccount, String appID);

    /**
     *设置自动回复开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param status:
     * @return : MsgBaseResult
     */
    public MsgBaseResult setAutoReplySwitch(String enterpriseAccount, String appID, int status);



    /**
     * 查询 移动客服 开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @return : GetCustomServiceSwitchResult
     */
    public GetCustomServiceSwitchResult queryCustomServiceReplySwitch(String enterpriseAccount, String appID);

    /**
     *设置 移动客服 开关
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param status:
     * @return : MsgBaseResult
     */
    public MsgBaseResult setCustomServiceReplySwitch(String enterpriseAccount, String appID, int status);
    
    /**
     *设置 移动客服 开关New。 只支持多客服。
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param status:
     * @return : MsgBaseResult
     */
    @Deprecated
    public MsgBaseResult setCustomServiceReplySwitchNew(String enterpriseAccount, String appID, int status);


    /**
     *设置 移动客服 开关New, 支持 多客服+微客服
     * @param enterpriseAccount： 企业的纷享账号， E.企业账号.员工ID 中间这部分
     * @param appID: 开放平台分配的应用ID号
     * @param customerSessionType:0-多客服， 1-微客服,2-互联服务号
     * @param status:
     * @return : MsgBaseResult
     */
    public MsgBaseResult setCustomServiceReplySwitchWithType(String enterpriseAccount, String appID, int status, Integer customerSessionType);

      /**
       * 获取自动回复的内容。自动回复的内容可能是以下形式：
       *（1）一段文本。
       * (2)一个图文消息ID
       * 用户在服务号中上行的每条消息，都要调用这个接口检查是否要自动回复。
       * 如果自动回复开关没打开，回复内容为空。
       * 如果打开了，分两种情况：
       * （1）关键字匹配成功，用关键字回复。
       * （2）关键字匹配失败，用默认回复。
       *
     * @param fsUserAccount： 用户的纷享账号，格式为  E.企业账号.员工ID
     * @param appID: 开放平台分配的应用ID号
     * @param userInput: 用户的输入
     * @return : MsgBaseResult
     */
    public GetAutoReplyResult getAutoReply(String fsUserAccount, String appID, String userInput);


    /**
     * 获取上游企业的自动回复内容
     * @param upEnterpriseAccount
     * @param fsUserAccount
     * @param appID
     * @param userInput
     * @return
     */
    public GetAutoReplyResult getCrossAutoReply(String crossEa,String fsUserAccount, String appID, String userInput);

    /**
              获取一个企业下面打开了客服开关的应用列表.
    *@param enterpriseAccount： 企业的纷享账号，是 E.企业账号.员工ID 中间这部分。
    *@param status: 1:获取打开客服开关的列表，0-获取关闭客服开关的列表
    *@return : AppID列表
    * */
    public GetCustomServiceAppListResult getCustomServiceAppListBySwitch(String enterpriseAccount, int status);


    /**
     * 开启客服会话
     *
     * @param ea: 企业账号
     * @param appId 开放平台分配的appID
     * @param customServiceRepresentives:客服人员的 ID， E.企业账号.员工账号 后面这一部分
     * @param sessionName: 会话名称
     * @return
     */
    public MsgBaseResult createCustomServiceRepresentiveList(String ea, String appId, List<String> customServiceRepresentives, String sessionName);

    /**
     * 更新客服会话
     *
     * @param ea: 企业账号
     * @param appId 开放平台分配的appID
     * @param customServiceRepresentives: 最新的客服人员的 ID列表， E.企业账号.员工账号 后面这一部分
     * @return
     */

    public MsgBaseResult updateCustomServiceRepresentiveList(String ea, String appId, List<String> customServiceRepresentives);



    /**
     * 更新客服会话session名称和客服人员列表
     *
     * @param ea: 企业账号
     * @param appId 开放平台分配的appID
     * @param customServiceRepresentives: 最新的客服人员的 ID列表， E.企业账号.员工账号 后面这一部分
     * @param sessionName: 最新的客服会话session名称
     * @return
     */
    public MsgBaseResult updateCustomServiceRepresentiveListAndSessionName(String ea, String appId, List<String> customServiceRepresentives, String sessionName);

    /**
     * 删除客服会话
     *
     * @param ea: 企业账号
     * @param appId 开放平台分配的appID
     * @return
     */
    public MsgBaseResult deleteCustomServiceRepresentiveList(String ea, String appId);
}
