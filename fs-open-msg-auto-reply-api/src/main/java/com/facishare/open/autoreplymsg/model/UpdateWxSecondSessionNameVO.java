package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置工单、问卷session信息参数实体
 * <AUTHOR>
 * @date 2016-6-13
 */
public class UpdateWxSecondSessionNameVO implements Serializable {

    private static final long serialVersionUID = -6426718909462543399L;
    
    /**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 微信用户openId
     */
    private String senderOpenId;
    
    /**
     * 微信用户名称
     */
    private String senderName;
    
    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getSenderOpenId() {
		return senderOpenId;
	}

	public void setSenderOpenId(String senderOpenId) {
		this.senderOpenId = senderOpenId;
	}

	public String getSenderName() {
		return senderName;
	}

	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}

	@Override
    public String toString() {
        return "UpdateWxSecondSessionVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", senderOpenId=" + senderOpenId +
                ", senderName=" + senderName +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
    }
}
