package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

/**
 * 工作台消息session信息
 * <AUTHOR> 
 * @Date 2016/09/08
 */
public class WorkbenchSessionInfoVO implements Serializable {
    
    private static final long serialVersionUID = -2356989854853505590L;

    private String sessionId;
    
    private int notReadCount;
    
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getNotReadCount() {
        return notReadCount;
    }

    public void setNotReadCount(int notReadCount) {
        this.notReadCount = notReadCount;
    }

    @Override
    public String toString() {
        return "WorkbenchSessionInfoVO{" +
                "sessionId='" + sessionId + '\'' +
                ", notReadCount='" + notReadCount + '\'' +
                '}';
    }
    
}
