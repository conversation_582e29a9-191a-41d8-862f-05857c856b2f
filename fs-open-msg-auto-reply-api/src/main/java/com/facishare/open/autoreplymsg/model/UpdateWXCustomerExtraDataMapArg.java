package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.Map;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置微联服务号二级session数据信息
 * <AUTHOR>
 * @date 2018-4-4
 */
public class UpdateWXCustomerExtraDataMapArg implements Serializable {

	private static final long serialVersionUID = -4935199483245167573L;

	/**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 微信用户openId
     */
    private String senderOpenId;
    
    /**
     * 扩展数据信息
     */
    private Map<String, String> extraDataMap;
    
    
	public String getEnterpriseAccount() {
		return enterpriseAccount;
	}

	public void setEnterpriseAccount(String enterpriseAccount) {
		this.enterpriseAccount = enterpriseAccount;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getSenderOpenId() {
		return senderOpenId;
	}

	public void setSenderOpenId(String senderOpenId) {
		this.senderOpenId = senderOpenId;
	}

	public Map<String, String> getExtraDataMap() {
		return extraDataMap;
	}

	public void setExtraDataMap(Map<String, String> extraDataMap) {
		this.extraDataMap = extraDataMap;
	}

	@Override
    public String toString() {
        return "UpdateWXCustomerExtraDataMapArg{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", senderOpenId=" + senderOpenId +
                ", extraDataMap=" + extraDataMap +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
    }
}
