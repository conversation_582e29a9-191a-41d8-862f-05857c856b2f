package com.facishare.open.autoreplymsg.service;


import com.facishare.open.autoreplymsg.model.CreateDefaultReplyVO;
import com.facishare.open.autoreplymsg.model.UpdateDefaultReplyVO;
import com.facishare.open.autoreplymsg.result.CreateDefaultReplyResult;
import com.facishare.open.autoreplymsg.result.QueryDefaultReplyResult;
import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/1.
 *
 * 默认自动回复的增删改查
 */
public interface MsgDefaultReplyService {
    /**
     *创建默认自动回复
     * @param createDefaultReplyVO
     * @return : CreateDefaultAutoReplyResult
     */
    public CreateDefaultReplyResult createDefaultReply(CreateDefaultReplyVO createDefaultReplyVO);

    /**
     *删除默认自动回复
     * @param enterpriseAccount 企业在fs的账号
     * @param appID
     * @return :MsgBaseResult
     */
    public MsgBaseResult deleteDefaultReply(String enterpriseAccount, String appID, long replyMsgID);

    /**
     * 更新默认回复
     * @param updateDefaultAutoReplyVO
     * @return :MsgBaseResult
     */
    public MsgBaseResult updateDefaultReply(UpdateDefaultReplyVO updateDefaultAutoReplyVO);

    /**
    * 查询指定服务号的默认回复
     * @param enterpriseAccount 企业在fs的账号
     * @param appID
     * @return QueryDefaultReplyResult
    */
    public QueryDefaultReplyResult queryDefaultReply(String enterpriseAccount, String appID);
}
