package com.facishare.open.autoreplymsg.result;

import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/4/1.
 */
public class GetCustomServiceSwitchResult extends MsgBaseResult {
    private static final long serialVersionUID = 8612363891017565480L;

    /**
     * 默认构造
     */
    public GetCustomServiceSwitchResult() {
        super();
    }

    /**
     * 构造方法(返回码, 返回描叙)
     *
     * @param errorCode
     * @param errorMsg
     */
    public GetCustomServiceSwitchResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public int getReplySwitch() {
        return replySwitch;
    }

    public void setReplySwitch(int replySwitch) {
        this.replySwitch = replySwitch;
    }

    /**
     * 0-关闭， 1-打开 ，2-未设置
    */
    private int replySwitch;

    @Override
    public String toString() {
        return "GetCustomServiceSwitchResult{" +
                "replySwitch=" + replySwitch +
                '}';
    }
}
