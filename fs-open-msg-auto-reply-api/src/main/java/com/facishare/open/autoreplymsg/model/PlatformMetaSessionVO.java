package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

import com.google.common.base.MoreObjects;

/**
 * 工作台设置参数实体
 * 属性为 null 表示不更新该项值
 * 需要更新为空时传 "" 或者 list.size()==0
 * <AUTHOR>
 * @date 2016-6-6
 */
public class PlatformMetaSessionVO implements Serializable {

    private static final long serialVersionUID = 8453683241356403372L;
    
    /**
     * 工作台session名称
     */
    private String customerSessionName;
    
    /**
     * 工作台副标题
     */
    private String customerSessionSubName;

    /**
     * 工作台session 头像
     */
    private String customerSessionPortrait;
    
    /**
     * 管理员列表E.fs.123 的最后一部分
     * 传null，表示不更新这个值。
     */
    private List<Integer> admins; 
    
    /**
     * 客服列表E.fs.123 的最后一部分
     * 传null，表示不更新这个值。
     */
    private List<Integer> customers;

    /**
     * 管理员工作台描述
     */
    private String adminDescription;
    
    /**
     * 客服工作台描述
     */
    private String customerDescription;
    
    /**
     * 身份和显示名称映射关系
     */
    private List<CustomerNameVO> customerNames;
    
    public String getCustomerSessionPortrait() {
        return customerSessionPortrait;
    }

    public void setCustomerSessionPortrait(String customerSessionPortrait) {
        this.customerSessionPortrait = customerSessionPortrait;
    }

    public List<Integer> getAdmins() {
        return admins;
    }

    public void setAdmins(List<Integer> admins) {
        this.admins = admins;
    }

    public List<Integer> getCustomers() {
        return customers;
    }

    public void setCustomers(List<Integer> customers) {
        this.customers = customers;
    }

    public String getCustomerSessionName() {
        return customerSessionName;
    }

    public void setCustomerSessionName(String customerSessionName) {
        this.customerSessionName = customerSessionName;
    }

    public String getCustomerSessionSubName() {
        return customerSessionSubName;
    }

    public void setCustomerSessionSubName(String customerSessionSubName) {
        this.customerSessionSubName = customerSessionSubName;
    }

    public String getAdminDescription() {
        return adminDescription;
    }

    public void setAdminDescription(String adminDescription) {
        this.adminDescription = adminDescription;
    }

    public String getCustomerDescription() {
        return customerDescription;
    }

    public void setCustomerDescription(String customerDescription) {
        this.customerDescription = customerDescription;
    }
    
	public List<CustomerNameVO> getCustomerNames() {
		return customerNames;
	}

	public void setCustomerNames(List<CustomerNameVO> customerNames) {
		this.customerNames = customerNames;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("customerSessionName", customerSessionName).add("customerSessionSubName", customerSessionSubName)
                .add("customerSessionPortrait", customerSessionPortrait).add("admins", admins)
                .add("customers", customers).add("adminDescription", adminDescription)
                .add("customerNames", customerNames).add("customerDescription", customerDescription).toString();
    }
    
    public void checkParams() {
        
    }
    
}
