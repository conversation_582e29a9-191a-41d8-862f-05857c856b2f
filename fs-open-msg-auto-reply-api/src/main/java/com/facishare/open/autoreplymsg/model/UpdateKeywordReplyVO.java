package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/3/4.
 */
public class UpdateKeywordReplyVO implements Serializable {

    private static final long serialVersionUID = 6257119751151636954L;
    //appID 开放平台AppID
    private String appID;
    //企业账号是 E.enterpriseAccount.员工ID 中间这一部分
    private String enterpriseAccount;

    //自动回复编号
    private long replyMsgID;
    //自动回复中的文本回复
    private String contentTxt;
    //素材库中的图文素材ID
    private String contentImgTxtID;
    //当前回复消息的状态，合法值： 0-文本，1-图文
    private int activeReplyType;
    //规则名
    private String ruleName;
    //关键词+包含关系 json字符串
    List<KeywordTypeInfo> keywordList;


    public UpdateKeywordReplyVO() {
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }

    public List<KeywordTypeInfo> getKeywordList() {
        return keywordList;
    }

    public void setKeywordList(List<KeywordTypeInfo> keywordList) {
        this.keywordList = keywordList;
    }

    @Override
    public String toString() {
        return "UpdateKeywordReplyVO{" +
                "appID='" + appID + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", replyMsgID=" + replyMsgID +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxtID='" + contentImgTxtID + '\'' +
                ", activeReplyType=" + activeReplyType +
                ", ruleName='" + ruleName + '\'' +
                ", keywordList=" + keywordList +
                '}';
    }


}
