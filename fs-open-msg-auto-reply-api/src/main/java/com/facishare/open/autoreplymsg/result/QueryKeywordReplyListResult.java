package com.facishare.open.autoreplymsg.result;

import com.facishare.open.autoreplymsg.constant.AutoReplyMsgTypeEnum;
import com.facishare.open.autoreplymsg.model.KeywordReplyDO;
import com.facishare.open.msg.result.MsgBaseResult;

import java.util.List;

/**
 * Created by fengyh on 2016/3/4.
 */
public class QueryKeywordReplyListResult extends MsgBaseResult {

    private static final long serialVersionUID = 8571645306062751873L;

    //此result结果是否有效
    private boolean isValid = true;

    private List<KeywordReplyDO> keywordList;

    public boolean isValid() {
        return isValid;
    }

    public void setIsValid(boolean isValid) {
        this.isValid = isValid;
    }

    /**
     * 构造方法(返回码, 返回描叙)
     *
     * @param errorCode
     * @param errorMsg
     */
    public QueryKeywordReplyListResult(int errorCode, String errorMsg, List<KeywordReplyDO> keywordReplys) {
        super(errorCode, errorMsg);
        this.keywordList = keywordReplys;
    }

    /**
     * 构造方法(返回码, 返回描叙)
     *
     * @param errorCode
     * @param errorMsg
     */
    public QueryKeywordReplyListResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public List<KeywordReplyDO> getKeywordList() {
        return keywordList;
    }

    public void setKeywordList(List<KeywordReplyDO> keywordList) {
        this.keywordList = keywordList;
    }

    @Override
    public String toString() {
        return "QueryKeywordReplyListResult{" +
                "keywordList=" + keywordList +
                '}';
    }
}
