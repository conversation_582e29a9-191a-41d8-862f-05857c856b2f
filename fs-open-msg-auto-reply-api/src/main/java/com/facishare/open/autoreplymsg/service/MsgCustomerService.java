package com.facishare.open.autoreplymsg.service;


import java.util.List;

import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.model.UpdateWXCustomerExtraDataMapArg;
import com.facishare.open.autoreplymsg.model.UpdateWXPlatformExtraDataMapArg;
import com.facishare.open.autoreplymsg.model.UpdateWxSecondSessionNameVO;
import com.facishare.open.autoreplymsg.model.UpdateWxSecondSessionVO;
import com.facishare.open.autoreplymsg.model.WorkbenchMessageVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionInfoVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionQueryVO;
import com.facishare.open.autoreplymsg.model.WorkbenchSessionVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;

/**
 * Created by huanghp on 2016/6/3.
 * 查询和设置多客服的相关信息
 */
public interface MsgCustomerService {
    
    /**
     * 设置客服信息,包括人工客服名称、icon 客服人员列表
     * @return
     */
    public CustomerSessionResult<Boolean> setCustomerSessionInfo(CustomerSessionInfoVO customerSessionInfoArg);
    
    /**
     * 获取客服设置信息
     * @return
     */
    public CustomerSessionResult<CustomerSessionInfoVO> getCustomerSessionInfo(String appId, String enterpriseAccount);
    
    /**
     *只支持多客服
     *
     * * 设置工作台session信息,包括人工客服信息、管理员信息、session信息
     * @param updateType 0 开启 1 关闭 2 update 客服成员或客服会话信息 3 禁用(禁用多客服)
     *  0 开启  //创建时候调用
     *  1 关闭 //删除时候调用（终端同步删除该session,session所有设置信息(包括人员名称，头像)删除）
     *  2 update // 修改信息的时候调用
     *  3 禁用多客服功能  //禁用后客服不可回复(客服输入框隐藏，提示禁用)
     *  4 启用多客服功能  //禁用后可以启用（启用后输入框显示，可以回复）
     *  5 隐藏session //session 所有设置信息和历史消息都在，只是不显示session 不接收信消息
     *  6 显示session  //显示后所有设置信息肯历史消息显示，session 可以接收消息
     *
     *
     * @param platformMetaSessionVO: 在type为0 或 2 时需提供
     * @return
     */
    @Deprecated
    public CustomerSessionResult<Boolean> updatePlatformMetaSession(String appId, String enterpriseAccount, int updateType, PlatformMetaSessionVO platformMetaSessionVO);

    /**
     *
     * 该接口支持多客服，微客服。
     *
     * 设置工作台session信息,包括人工客服信息、管理员信息、session信息
     * @param updateType 0 开启 1 关闭 2 update 客服成员或客服会话信息 3 禁用(禁用多客服)
     *  0 开启  //创建时候调用
     *  1 关闭 //删除时候调用（终端同步删除该session,session所有设置信息(包括人员名称，头像)删除）
     *  2 update // 修改信息的时候调用
     *  3 禁用多客服功能  //禁用后客服不可回复(客服输入框隐藏，提示禁用)
     *  4 启用多客服功能  //禁用后可以启用（启用后输入框显示，可以回复）
     *  5 隐藏session //session 所有设置信息和历史消息都在，只是不显示session 不接收信消息
     *  6 显示session  //显示后所有设置信息肯历史消息显示，session 可以接收消息
     *  7 删除指定客服一级session.  只支持微客服,不支持多客服。
     *
     * updatetype在两种场景下的区分：
     * 多客服场景：0，2 需要传客服列表，其余的类型影响的范围是所有客服。
     * 微客服场景：0，1，2 不需要传客服列表，其余的类型可以指定客服人员，如果不指定，就是指所有的客服人员。
     * 微客服的客服人员变更在sendwechatmsg的参数中指定，由企信帮忙控制。
     *
     * @param appId: 纷享服务号AppID
     * @param enterpriseAccount: 企业账号 ， E.xx.xxxx中间部分,如果是外互联号 企业帐号为上游
     * @param customerSessionType:0-多客服， 1-微客服, 2-互联服务号客服
     * @param platformMetaSessionVO: 多客服在type为0 或 2 时需提供, 微客服0,1,2时不需要传客服人员列表。
     *
     * @return
     */
    public CustomerSessionResult<Boolean> updatePlatformMetaSessionNew(String appId, String enterpriseAccount, int updateType,
                                                                       PlatformMetaSessionVO platformMetaSessionVO, Integer customerSessionType);
    /**
     * 发送工单,问卷,评论,微信客服通知 消息
     * 支持多客服，微客服 的场景deleteWxCustomSession
     * @param workbenchMessageVO @see WorkbenchMessageVO
     * @return
     */
    public CustomerSessionResult<Void> sendWorkbenchMessage(WorkbenchMessageVO workbenchMessageVO);
    
    /**
     * 设置工单、问卷session信息接口
     * @param workbenchSessionVO @see WorkbenchSessionVO
     */
    public CustomerSessionResult<Boolean> updateWorkbenchSession(WorkbenchSessionVO workbenchSessionVO);

    /**
     * 设置服务号状态
     * @param enterpriseAccount
     * @param appId
     * @param status true:启用 false:停用
     * @return
     */
    @Deprecated
    public CustomerSessionResult<Void> setAppServiceStatus(String enterpriseAccount,String appId, boolean status);


    /**
     * 设置服务号状态,支持 多客服，微客服
     * @param enterpriseAccount
     * @param appId
     * @param status true:启用 false:停用
     * @param type :0-多客服， 1-微客服,2-互联客服
     * @return
     */
    public CustomerSessionResult<Void> setAppServiceStatus(String enterpriseAccount,String appId, boolean status, int type);
    
    /**
     * 获取工作台session信息
     * @param enterpriseAccount
     * @param appId
     * @param workbenchType 工作消息类型  1.工单  2.问卷  3.评论
     * @return
     */
    public CustomerSessionResult<WorkbenchSessionInfoVO> getWorkbenchSessionInfo(String enterpriseAccount,String appId, int workbenchType);
    
    /**获取工作台session信息
     * @param workbenchSessionQueryVO.sessionType 0-多客服， 1-微客服,2-互联客服
     * @return
     */
    CustomerSessionResult<WorkbenchSessionInfoVO> getWorkbenchSessionInfo(WorkbenchSessionQueryVO workbenchSessionQueryVO);

    /**
     * 删除外联服务号指定客服人员的工作台session
     * @param appID : 这里要用我们纷享的服务号AppID, 该AppID已经和微信的服务号关联。
     * @param ea ：服务号所属企业
     * @param useIDs ：客服人员的ID列表.
     * */
    CustomerSessionResult<Void> deleteWxCustomSession(String appID, String ea, List<Integer> useIDs);
    
    /**
     * 更新微联服务号工作台二级session信息
     * */
    CustomerSessionResult<Void> updateWxSecondSession(UpdateWxSecondSessionVO updateWxSecondSessionVO);
    
    /**
     * 更新微联服务号工作台二级session名称信息
     * */
    CustomerSessionResult<Void> updateWxSecondSessionName(UpdateWxSecondSessionNameVO updateWxSecondSessionNameVO);
    
    /**
     * 更新微联服务号工作台二级会话session附加数据信息
     * */
    CustomerSessionResult<Void> updateWXCustomerExtraDataMap(UpdateWXCustomerExtraDataMapArg updateWXCustomerExtraDataMapArg);
    
    /**
     * 更新微联服务号工作台一级session附加数据信息
     * */
    CustomerSessionResult<Void> updateWXPlatformExtraDataMap(UpdateWXPlatformExtraDataMapArg updateWXPlatformExtraDataMapArg);

}
