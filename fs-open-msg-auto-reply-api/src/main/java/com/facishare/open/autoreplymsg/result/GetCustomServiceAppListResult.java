package com.facishare.open.autoreplymsg.result;

import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;

import java.util.List;

/**
 * Created by fengyh on 2016/4/6.
 */
public class GetCustomServiceAppListResult extends MsgBaseResult {

    private static final long serialVersionUID = 8946035098158252741L;

    private int switchState;//开关状态
    private List<String> appList; //根据开关状态查询到的应用列表

    /**
     * 默认构造
     */
    public GetCustomServiceAppListResult() {
        super(MsgCodeEnum.SUCCESS.getErrorCode(), MsgCodeEnum.SUCCESS.getErrorMsg());
    }

    /**
     * 构造方法(返回码, 返回描叙)
     *
     * @param errorCode
     * @param errorMsg
     */
    public GetCustomServiceAppListResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public int getSwitchState() {
        return switchState;
    }

    public void setSwitchState(int switchState) {
        this.switchState = switchState;
    }

    public List<String> getAppList() {
        return appList;
    }

    public void setAppList(List<String> appList) {
        this.appList = appList;
    }

    @Override
    public String toString() {
        return "GetCustomServiceAppListResult{" +
                "switchState=" + switchState +
                ", appList=" + appList +
                '}';
    }
}
