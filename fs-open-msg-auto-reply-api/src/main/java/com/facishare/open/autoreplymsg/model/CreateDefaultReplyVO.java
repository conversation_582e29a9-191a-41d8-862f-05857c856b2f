package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/3/1.
 *
 * 创建默认自动回复的入参类
 */
public class CreateDefaultReplyVO implements Serializable {

    private static final long serialVersionUID = 8423679632545067483L;

    //appID 开放平台AppID
    private String appID;
    //企业账号是 E.enterpriseAccount.员工ID 中间这一部分
    private String enterpriseAccount;

    //自动回复中的文本回复
    private String contentTxt;
    //素材库中的图文素材ID
    private String contentImgTxtID;
    //当前回复消息的状态，合法值： 0-文本，1-图文
    private int activeReplyType;

    public CreateDefaultReplyVO() {
    }

    @Override
    public String toString() {
        return "CreateDefaultReplyVO{" +
                "appID='" + appID + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxtID='" + contentImgTxtID + '\'' +
                ", activeReplyType=" + activeReplyType +
                '}';
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

}
