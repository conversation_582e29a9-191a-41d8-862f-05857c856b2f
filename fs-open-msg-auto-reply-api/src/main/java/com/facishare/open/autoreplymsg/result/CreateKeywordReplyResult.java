package com.facishare.open.autoreplymsg.result;

import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/4.
 */
public class CreateKeywordReplyResult extends MsgBaseResult {

    private static final long serialVersionUID = -4110505698351197559L;
    //自动回复编号
    private long replyMsgID;

    public CreateKeywordReplyResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }

    @Override
    public String toString() {
        return "CreateKeywordReplyResult{" +
                "replyMsgID=" + replyMsgID +
                '}';
    }
}
