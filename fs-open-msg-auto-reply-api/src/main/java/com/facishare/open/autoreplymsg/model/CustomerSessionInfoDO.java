package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by huanghp on 2016/6/3.
 */
public class CustomerSessionInfoDO implements Serializable{

    private static final long serialVersionUID = 7209152279007964123L;

    private long id;
    
    //appID 开放平台AppID
    private String appId;

    //E.enterpriseAccount
    private String enterpriseAccount;

    /**
     *人工客服名称
     */
    private String customerName;
    
    /**
     * 人工客服icon
     */
    private String customerIcon;
    
    /**
     * 人工客服人员列表;逗号分割的字符串（233,3432,44234）
     */
    private String customerList;
    
    /**
     * 创建时间
     */
    private Date gmtCreate;
    
    /**
     * 更新时间
     */
    private Date gmtModified;

    
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerIcon() {
        return customerIcon;
    }

    public void setCustomerIcon(String customerIcon) {
        this.customerIcon = customerIcon;
    }

    public String getCustomerList() {
        return customerList;
    }

    public void setCustomerList(String customerList) {
        this.customerList = customerList;
    }
    
    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    @Override
    public String toString() {
        return "CustomerSessionInfDO{" +
                "id='" + id + '\'' +
                "appId='" + appId + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerIcon=" + customerIcon +
                ", customerList=" + customerList +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                '}';
    }

}
