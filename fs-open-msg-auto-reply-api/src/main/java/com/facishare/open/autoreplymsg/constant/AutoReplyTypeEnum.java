package com.facishare.open.autoreplymsg.constant;

/**
 * Created by fengyh on 2016/3/23.
 */
public enum AutoReplyTypeEnum {
    //默认自动回复
    REPLY_DEFAULT(0),
    //关键字回复
    REPLY_KEYWORD(1);

    private int type;

    AutoReplyTypeEnum(int type) {
        this.type = type;
    }

    public int value() {
        return  this.type;
    }

    public static AutoReplyTypeEnum valueOf(int type) {
        switch (type) {
            case 1:
                return AutoReplyTypeEnum.REPLY_KEYWORD;

            default:
                return AutoReplyTypeEnum.REPLY_DEFAULT;
        }
    }

    @Override
    public String toString() {
        return "AutoReplyTypeEnum{" +
                "type=" + type +
                '}';
    }
}
