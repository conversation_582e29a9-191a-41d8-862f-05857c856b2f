package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2016/3/5.
 */
public class KeywordReplyDO<T> implements Serializable {

    private static final long serialVersionUID = -2361709180416816921L;
    //appID 开放平台AppID
    private String appID;
    //员工纷享账号为  E.enterpriseAccount.员工ID
    private String enterpriseAccount;

    //自动回复消息ID
    private long replyMsgID;
    //自动回复中的文本回复
    private String contentTxt;
    //素材库中的图文素材ID
    private String contentImgTxtID;
    /**
     * 携带图文消息泛型结果。由外部使用者(当前是应用中心web层)用素材ID从素材库换取素材详细结果后，
     * 写到到这里返回给前端。
     */
    private T contentImgTxtDetail;

    public T getContentImgTxtDetail() {
        return contentImgTxtDetail;
    }

    public void setContentImgTxtDetail(T contentImgTxtDetail) {
        this.contentImgTxtDetail = contentImgTxtDetail;
    }

    //当前回复消息的状态，合法值： 0-文本，1-图文
    private int activeReplyType;
    //keyword和包含关系说明, json字符串
    private List<KeywordTypeInfo> keywordList;

    public String getKeywordListJson() {
        return keywordListJson;
    }

    public void setKeywordListJson(String keywordListJson) {
        this.keywordListJson = keywordListJson;
    }

    private String ruleName;

    private transient String keywordListJson;
    //规则名

    public List<KeywordTypeInfo> getKeywordList() {
        return keywordList;
    }

    public void setKeywordList(List<KeywordTypeInfo> keywordList) {
        this.keywordList = keywordList;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }


    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    @Override
    public String toString() {
        return "KeywordReplyDO{" +
                "appID='" + appID + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", replyMsgID=" + replyMsgID +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxtID='" + contentImgTxtID + '\'' +
                ", contentImgTxtDetail=" + contentImgTxtDetail +
                ", activeReplyType=" + activeReplyType +
                ", keywordList=" + keywordList +
                ", keywordListJson='" + keywordListJson + '\'' +
                ", ruleName='" + ruleName + '\'' +
                '}';
    }
}
