package com.facishare.open.autoreplymsg.constant;

/**
 * Created by fengyh on 2016/3/4.
 */
public enum AutoReplyMsgTypeEnum {
    TXT(0),
    IMGTXT(1);

    private int replyType;

    AutoReplyMsgTypeEnum(int replyType) {
        this.replyType = replyType;
    }

    public static AutoReplyMsgTypeEnum valueOf(int type) {
        switch (type) {
            default:
                return AutoReplyMsgTypeEnum.TXT;
            case 1:
                return AutoReplyMsgTypeEnum.IMGTXT;
        }
    }

    public int value() {
        return this.replyType;
    }

    @Override
    public String toString() {
        return "AutoReplyMsgTypeEnum{" +
                "replyType=" + replyType +
                '}';
    }
}
