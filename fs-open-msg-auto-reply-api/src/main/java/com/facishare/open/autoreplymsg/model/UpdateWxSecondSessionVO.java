package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

import com.facishare.open.msg.model.CustomerMembersVO;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置工单、问卷session信息参数实体
 * <AUTHOR>
 * @date 2016-6-13
 */
public class UpdateWxSecondSessionVO implements Serializable {

    private static final long serialVersionUID = -6426718909462543399L;
    
    /**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 微信用户openId
     */
    private String senderOpenId;
    
    /**
     * 微信用户名称
     */
    private String senderName;
    
    /**
     * 微信用户头像
     */
    private String senderPortrait;
    
    /**
     * 接收客服人员及身份列表
     */
    private List<CustomerMembersVO> customerMembers;
    
    /**
     * 接收专家人员及身份列表
     */
    private List<CustomerMembersVO> expertMembers;

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getSenderOpenId() {
		return senderOpenId;
	}

	public void setSenderOpenId(String senderOpenId) {
		this.senderOpenId = senderOpenId;
	}

	public String getSenderName() {
		return senderName;
	}

	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}

	public String getSenderPortrait() {
		return senderPortrait;
	}

	public void setSenderPortrait(String senderPortrait) {
		this.senderPortrait = senderPortrait;
	}

	public List<CustomerMembersVO> getCustomerMembers() {
		return customerMembers;
	}

	public void setCustomerMembers(List<CustomerMembersVO> customerMembers) {
		this.customerMembers = customerMembers;
	}

	public List<CustomerMembersVO> getExpertMembers() {
		return expertMembers;
	}

	public void setExpertMembers(List<CustomerMembersVO> expertMembers) {
		this.expertMembers = expertMembers;
	}

	@Override
    public String toString() {
        return "UpdateWxSecondSessionVO{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", senderOpenId=" + senderOpenId +
                ", senderName=" + senderName +
                ", senderPortrait=" + senderPortrait +
                ", customerMembers=" + customerMembers +
                ", expertMembers=" + expertMembers +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
    }
}
