package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

/**
 * Created by fengyh on 2016/3/1.
 *
 * 更新默认自动回复的入参类
 */
public class UpdateDefaultReplyVO implements Serializable {

    private static final long serialVersionUID = 64519138145332581L;
    //appID 开放平台AppID
    private String appID;
    //企业账号是 E.enterpriseAccount.员工ID 中间这一部分
    private String enterpriseAccount;

    //createDefaultAutoReply()返回的自动回复消息ID
    private long replyMsgID;
    //文本消息内容
    private String contentTxt;
    //图文消息ID, 由素材库返回
    private String contentImgTxtID;
    //最后回复类型(文本或者图文)
    private int activeReplyType;

    public UpdateDefaultReplyVO() {
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

    @Override
    public String toString() {
        return "UpdateDefaultReplyVO{" +
                "appID='" + appID + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", replyMsgID=" + replyMsgID +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxt='" + contentImgTxtID + '\'' +
                ", activeReplyType=" + activeReplyType +
                '}';
    }
}
