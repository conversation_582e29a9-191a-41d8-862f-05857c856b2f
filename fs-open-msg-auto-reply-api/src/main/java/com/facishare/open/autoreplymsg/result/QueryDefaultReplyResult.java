package com.facishare.open.autoreplymsg.result;

import com.facishare.open.msg.result.MsgBaseResult;

/**
 * Created by fengyh on 2016/3/1.
 */
public class QueryDefaultReplyResult extends MsgBaseResult {
    private static final long serialVersionUID = -4200158482748941502L;
    //此result结果是否有效
    private boolean isValid = true;
   //createDefaultAutoReply()返回的自动回复消息ID
    private long replyMsgID;
    //自动回复中的文本回复
    private String contentTxt;
    //素材库中的图文素材ID
    private String contentImgTxtID;
    //当前回复消息的状态，合法值： 0-文本，1-图文
    private int activeReplyType;

    public QueryDefaultReplyResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public boolean isValid() {
        return isValid;
    }

    public void setIsValid(boolean isValid) {
        this.isValid = isValid;
    }

    public long getReplyMsgID() {
        return replyMsgID;
    }

    public void setReplyMsgID(long replyMsgID) {
        this.replyMsgID = replyMsgID;
    }

    public String getContentTxt() {
        return contentTxt;
    }

    public void setContentTxt(String contentTxt) {
        this.contentTxt = contentTxt;
    }

    public String getContentImgTxtID() {
        return contentImgTxtID;
    }

    public void setContentImgTxtID(String contentImgTxtID) {
        this.contentImgTxtID = contentImgTxtID;
    }

    public int getActiveReplyType() {
        return activeReplyType;
    }

    public void setActiveReplyType(int activeReplyType) {
        this.activeReplyType = activeReplyType;
    }

    @Override
    public String toString() {
        return "QueryDefaultReplyResult{" +
                "replyMsgID=" + replyMsgID +
                ", contentTxt='" + contentTxt + '\'' +
                ", contentImgTxtID=" + contentImgTxtID +
                ", activeReplyType=" + activeReplyType +
                '}';
    }
}
