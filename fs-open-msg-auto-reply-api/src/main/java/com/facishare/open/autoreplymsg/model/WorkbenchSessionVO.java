package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;

import org.apache.commons.lang.StringUtils;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置工单、问卷session信息参数实体
 * <AUTHOR>
 * @date 2016-6-13
 */
public class WorkbenchSessionVO implements Serializable {

    private static final long serialVersionUID = -6426718909462543399L;
    
    private String upEnterpriseAccount;
    
    /**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 工作消息类型  1.工单  2.问卷  3.评论,  4.微信客服   5.外联服务号的外部联系人, 7-服务工单  8-服务通【微联服务号工作台中】
     */
    private int workbenchType;
    
    /**
     * session 名称
     */
    private String sessionName;
    
    /**
     * session icon
     */
    private String sessionIcon;
    
    /**
     * 跳转url
     */
    private String url;

    /**   0-多客服， 1-微客服,2-互联客服   */
    private int sessionType = 0;

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getWorkbenchType() {
        return workbenchType;
    }

    public void setWorkbenchType(int workbenchType) {
        this.workbenchType = workbenchType;
    }
    
    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public String getSessionIcon() {
        return sessionIcon;
    }

    public void setSessionIcon(String sessionIcon) {
        this.sessionIcon = sessionIcon;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}

	@Override
    public String toString() {
        return "WorkbenchSessionVO{" +
        		" upEnterpriseAccount='" + upEnterpriseAccount + '\'' +
                " enterpriseAccount='" + enterpriseAccount + '\'' +
                ", appId='" + appId + '\'' +
                ", workbenchType=" + workbenchType +
                ", sessionName='" + sessionName + '\'' +
                ", sessionIcon='" + sessionIcon + '\'' +
                ", url='" + url + '\'' +
                ", sessionType=" + sessionType +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!StringUtils.isEmpty(enterpriseAccount) || !StringUtils.isEmpty(upEnterpriseAccount) , "enterpriseAccount and upEnterpriseAccount is null or empty");
        
        Preconditions.checkArgument((!Strings.isNullOrEmpty(url) || (workbenchType == 5)), "url is null or empty and workbenchType is not 5");
                
        Preconditions.checkArgument(!Strings.isNullOrEmpty(sessionName) , "sessionName is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(sessionIcon) , "sessionIcon is null or empty");

        Preconditions.checkArgument(0 == sessionType || 1 == sessionType || 2==sessionType, "sessionType is illegal");
    }
}
