package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 发送提醒消息参数实体
 * <AUTHOR>
 * @date 2016-6-6
 */
public class WorkbenchMessageVO implements Serializable {

    private static final long serialVersionUID = -6426718909462543399L;
    
    /**
     * 企业账号E.fs.123中间部分
     */
    private String enterpriseAccount;
    
    private String upEnterpriseAccount;
    
    /**
     * 应用Id
     */
    private String appId;
    
    /**
     * 以下几类消息是角色共享的：
     * 1.工单  2.问卷  3.评论:
     *
     * 以下几类消息是每个接收者专有的：
     * 4.微信客服通知(被设置为客服人员的人会收到一条通知消息). 5.微客服外部联系人
     * 7. 服务工单
     * 8-服务通【微联服务号工作台中】
     */
    private int workbenchType;
    
    /**
     * 发送者Id
     */
    private int senderId;


    /**
     * 消息提示语（用于第二行显示）
     */
    private String lastSummary;

    /**
     * 0-多客服工作台
     * 1-微信客服工作台
     * 2-互联客服工作台
     * */
    private int sessionType;

    /**
     * sessionType为1时有效。
     *
     * 如果是往多客服工作台发送消息，则serderId有效， 则senderOpenId无效。
     * 如果是往微客服工作台发送消息，则senderOpenId有效， serderId无效。
     * 往哪种工作台发送消息，由sessionType字段指定
     *  */
    private String senderOpenId;

    /**
     * sessionType为1时有效。
     * 或者sessiontype为0 并且 workbenchType=7时也有效。
     *
     * 要接受这条消息的客服人员列表。
     *  */
    private List<Integer> customServiceRepresentiveIDs;

    public List<Integer> getCustomServiceRepresentiveIDs() {
        return customServiceRepresentiveIDs;
    }

    public void setCustomServiceRepresentiveIDs(List<Integer> customServiceRepresentiveIDs) {
        this.customServiceRepresentiveIDs = customServiceRepresentiveIDs;
    }

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public String getSenderOpenId() {
        return senderOpenId;
    }

    public void setSenderOpenId(String senderOpenId) {
        this.senderOpenId = senderOpenId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getWorkbenchType() {
        return workbenchType;
    }

    public void setWorkbenchType(int workbenchType) {
        this.workbenchType = workbenchType;
    }

    public int getSenderId() {
        return senderId;
    }

    public void setSenderId(int senderId) {
        this.senderId = senderId;
    }

    public String getLastSummary() {
        return lastSummary;
    }

    public void setLastSummary(String lastSummary) {
        this.lastSummary = lastSummary;
    }
    
    public String getUpEnterpriseAccount() {
		return upEnterpriseAccount;
	}

	public void setUpEnterpriseAccount(String upEnterpriseAccount) {
		this.upEnterpriseAccount = upEnterpriseAccount;
	}

	@Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("upEnterpriseAccount", upEnterpriseAccount).add("enterpriseAccount", enterpriseAccount).add("appId", appId)
                .add("workbenchType", workbenchType).add("senderId", senderId).add("customServiceRepresentiveIDs", customServiceRepresentiveIDs)
                .add("lastSummary", lastSummary).add("sessionType", sessionType).toString();
    }
    
    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!StringUtils.isEmpty(enterpriseAccount) || !StringUtils.isEmpty(upEnterpriseAccount) , "enterpriseAccount and upEnterpriseAccount is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(lastSummary) , "lastSummary is null or empty");
                
        Preconditions.checkArgument(senderId != 0, "senderId is illegal");
    }
}
