package com.facishare.open.autoreplymsg.result;

import org.apache.commons.lang.builder.ToStringBuilder;

import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;

/**
 * Created by huanghp on 2016/6/2.
 */
public class CustomerSessionResult<T> extends MsgBaseResult {
    
    private static final long serialVersionUID = -5345408174318354512L;

    public CustomerSessionResult() {
        super();
    }

    public CustomerSessionResult(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }
    
    public CustomerSessionResult(MsgCodeEnum resultCode) {
        super(resultCode);
    }

    public CustomerSessionResult(T data) {
        super();
        this.data = data;
    }

    protected T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
        .append("errorCode", this.errorCode)
        .append("errormsg", this.errorMsg)
        .append("data", this.data)
        .toString();
    }
    
}
