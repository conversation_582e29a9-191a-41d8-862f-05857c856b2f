package com.facishare.open.autoreplymsg.model;

import java.io.Serializable;
import java.util.List;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 设置客服信息参数实体
 * <AUTHOR> 
 * @Date 2016/06/02
 */
public class CustomerSessionInfoVO implements Serializable {

    private static final long serialVersionUID = -3738441051198920796L;
    
    //appID 开放平台AppID
    private String appId;

    //E.enterpriseAccount
    private String enterpriseAccount;
    
    private Integer userId;
    
    /**
     *人工客服名称
     */
    private String customerName;
    
    /**
     * 人工客服icon
     */
    private String customerIcon;
    
    /**
     * 人工客服人员列表;逗号分割的字符串（233,3432,44234）
     */
    private List<String> customerList;
    
    private Integer customerSessionType;
    
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerIcon() {
        return customerIcon;
    }

    public void setCustomerIcon(String customerIcon) {
        this.customerIcon = customerIcon;
    }

    public List<String> getCustomerList() {
        return customerList;
    }

    public void setCustomerList(List<String> customerList) {
        this.customerList = customerList;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCustomerSessionType() {
		return customerSessionType;
	}

	public void setCustomerSessionType(Integer customerSessionType) {
		this.customerSessionType = customerSessionType;
	}

	@Override
    public String toString() {
        return "setCustomerSessionInfoArg{" +
                "appId='" + appId + '\'' +
                ", enterpriseAccount='" + enterpriseAccount + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerIcon=" + customerIcon +
                ", customerList=" + customerList +
                ", userId=" + userId +
                ", customerSessionType=" + customerSessionType +
                '}';
    }

    public void checkParams() {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(appId) , "appId is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(enterpriseAccount) , "enterpriseAccount is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customerName) , "customerName is null or empty");
        
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customerIcon) , "customerIcon is null or empty");
        
    }
    
}
