<?xml version="1.0" encoding="UTF-8"?>
 <project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-open-msg</artifactId>
        <version>2.0.0</version>
    </parent>
    <artifactId>fs-open-msg-provider</artifactId>
    <packaging>jar</packaging>
    <version>2.0.0-SNAPSHOT</version>
    <description>msg service provider</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
  
  <dependencies>
      <dependency>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>jedis-spring-support</artifactId>         
          <exclusions>
              <exclusion>
                  <artifactId>fastjson</artifactId>
                  <groupId>com.alibaba</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jedis</artifactId>
                  <groupId>redis.clients</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-pool</artifactId>
                  <groupId>commons-pool2</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>zstd-jni</artifactId>
                  <groupId>com.github.luben</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>lz4-java</artifactId>
                  <groupId>org.lz4</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <artifactId>jedis</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>commons-pool2</artifactId>
                  <groupId>org.apache.commons</groupId>
              </exclusion>
          </exclusions>
          <groupId>redis.clients</groupId>
      </dependency>
      <dependency>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-pool2</artifactId>
          <version>2.10.0</version>
      </dependency>

      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-enterpriserelation-rest-api2</artifactId>
          <version>2.0.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>okio</artifactId>
                  <groupId>com.squareup.okio</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>okhttp</artifactId>
                  <groupId>com.squareup.okhttp3</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>rpc-trace</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-servicelib-fsi</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>xstream</artifactId>
                  <groupId>com.thoughtworks.xstream</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.squareup.okhttp3</groupId>
          <artifactId>okhttp</artifactId>
      </dependency>

      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>logconfig-core</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>fastjson</artifactId>
                  <groupId>com.alibaba</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
          <exclusions>
          	<exclusion>
          		<artifactId>fs-eye-j4log</artifactId>
          		<groupId>com.fxiaoke</groupId>
          	</exclusion>
              <exclusion>
                  <artifactId>okhttp</artifactId>
                  <groupId>com.squareup.okhttp3</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fastjson</artifactId>
                  <groupId>com.alibaba</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>cglib-nodep</artifactId>
                  <groupId>cglib</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-annotations</artifactId>
                  <groupId>com.fxiaoke.common</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-kafka-support</artifactId>
                  <groupId>com.fxiaoke.common</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>okio</artifactId>
                  <groupId>com.squareup.okio</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-codec</artifactId>
                  <groupId>commons-codec</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-io</artifactId>
                  <groupId>commons-io</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-logging</artifactId>
                  <groupId>commons-logging</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>httpclient</artifactId>
                  <groupId>org.apache.httpcomponents</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>httpcore</artifactId>
                  <groupId>org.apache.httpcomponents</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>objenesis</artifactId>
                  <groupId>org.objenesis</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>ping-monitor-api</artifactId>
                  <groupId>com.fxiaoke.pms</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>ping-monitor</artifactId>
                  <groupId>com.fxiaoke.pms</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
          <version>2.8.0</version>
      </dependency>
		
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-msg-api</artifactId>
          <version>1.0.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>cglib-nodep</artifactId>
                  <groupId>cglib</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-qixin-message-event</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-circuit-breaker</artifactId>
                  <groupId>com.fxiaoke.circuit.breaker</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>java-utils</artifactId>
                  <groupId>com.fxiaoke.common</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jsr305</artifactId>
                  <groupId>com.google.code.findbugs</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-configuration</artifactId>
                  <groupId>commons-configuration</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-pool</artifactId>
                  <groupId>commons-pool</groupId>
              </exclusion>
              <exclusion>
                  <groupId>org.mongodb.morphia</groupId>
                  <artifactId>morphia</artifactId>
              </exclusion>
              <exclusion>
              <artifactId>jedis</artifactId>
              <groupId>redis.clients</groupId>
          </exclusion>
              <exclusion>
                  <artifactId>jedis-spring-support</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-qixin-api</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>mongo-java-driver</artifactId>
                  <groupId>org.mongodb</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>morphia</artifactId>
                  <groupId>org.mongodb.morphia</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
          <version>3.10.2</version>
      </dependency>
      <!-- auto reply -->
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-msg-auto-reply-api</artifactId>
          <version>2.0.0-SNAPSHOT</version>
      </dependency>

	  <dependency>
		  <groupId>com.facishare.open</groupId>
		  <artifactId>fs-wechat-union-core-api</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>fs-enterprise-relation-outapi</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-customer-app-outapi</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-lang3</artifactId>
                  <groupId>org.apache.commons</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>spring-support</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-wechat-proxy-core-api</artifactId>
                  <groupId>com.facishare.open</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>datapersist</artifactId>
                  <groupId>com.fxiaoke.cloud</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-open-app-center-api</artifactId>
                  <groupId>com.facishare.open</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-app-center-api</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.fasterxml.jackson.core</groupId>
                  <artifactId>jackson-core</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>org.apache.httpcomponents</groupId>
                  <artifactId>httpclient</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-open-msg-api</artifactId>
                  <groupId>com.facishare.open</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jcl-over-slf4j</artifactId>
                  <groupId>org.slf4j</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-api</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.google.code.gson</groupId>
                  <artifactId>gson</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-message-event</artifactId>
          <version>0.0.9-SNAPSHOT</version>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-oauth-base-api</artifactId>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-custom-api</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-wechat-baichuan-api</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.alibaba</groupId>
                  <artifactId>fastjson</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-wechat-proxy-common</artifactId>
                  <groupId>com.facishare.open</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jsr305</artifactId>
                  <groupId>com.google.code.findbugs</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-lang3</artifactId>
                  <groupId>org.apache.commons</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>httpcore</artifactId>
                  <groupId>org.apache.httpcomponents</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <artifactId>xstream</artifactId>
          <groupId>com.thoughtworks.xstream</groupId>
          <version>1.4.20</version>
      </dependency>
      <dependency>
          <groupId>com.fxiaoke.cloud</groupId>
          <artifactId>datapersist</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>com.alibaba</groupId>
                  <artifactId>fastjson</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-eye-j4log</artifactId>
                  <groupId>com.fxiaoke</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-lang3</artifactId>
                  <groupId>org.apache.commons</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-configuration</artifactId>
                  <groupId>commons-configuration</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>commons-text</artifactId>
                  <groupId>org.apache.commons</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-enterprise-id-account-converter</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>fs-enterprise-id-account-converter</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>java-utils</artifactId>
                  <groupId>com.fxiaoke.common</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>spring-support</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jsr305</artifactId>
                  <groupId>com.google.code.findbugs</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>rpc-trace</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jedis-spring-support</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-servicelib-fsi</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>javassist</groupId>
                  <artifactId>javassist</artifactId>
              </exclusion>
              <exclusion>
                  <artifactId>fastjson</artifactId>
                  <groupId>com.alibaba</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>curator-client</artifactId>
                  <groupId>org.apache.curator</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>curator-framework</artifactId>
                  <groupId>org.apache.curator</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>httpclient</artifactId>
                  <groupId>org.apache.httpcomponents</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>ehcache</artifactId>
                  <groupId>org.ehcache</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>javassist-3.14.0-GA</artifactId>
                  <groupId>org.ow2.util.bundles</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
          <exclusions>
              <exclusion>
                  <groupId>org.slf4j</groupId>
                  <artifactId>slf4j-log4j12</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

	  <dependency>
		  <groupId>com.facishare</groupId>
		  <artifactId>fs-organization-api</artifactId>
		  <version>1.0.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>fastjson</artifactId>
                  <groupId>com.alibaba</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <groupId>com.facishare</groupId>
          <artifactId>fs-organization-adapter-api</artifactId>
          <version>1.0.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>okhttp</artifactId>
                  <groupId>com.squareup.okhttp3</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>cglib-nodep</artifactId>
                  <groupId>cglib</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-organization-api</artifactId>
                  <groupId>com.facishare</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jackson-annotations</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jsr305</artifactId>
                  <groupId>com.google.code.findbugs</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>rpc-trace</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>groovy-all</artifactId>
                  <groupId>org.codehaus.groovy</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>objenesis</artifactId>
                  <groupId>org.objenesis</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>fs-circuit-breaker</artifactId>
                  <groupId>com.fxiaoke.circuit.breaker</groupId>
              </exclusion>
          </exclusions>
      </dependency>

	  <dependency>
		  <groupId>com.facishare</groupId>
		  <artifactId>fs-fsi-proxy</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>config-core</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>rpc-trace</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>httpclient</artifactId>
                  <groupId>org.apache.httpcomponents</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <!--jvm监控-->
      <dependency>
          <groupId>com.fxiaoke.common</groupId>
          <artifactId>metrics-oss</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>rpc-trace</artifactId>
                  <groupId>com.github.colin-lee</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jcl-over-slf4j</artifactId>
                  <groupId>org.slf4j</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>java-utils</artifactId>
                  <groupId>com.fxiaoke.common</groupId>
              </exclusion>
          </exclusions>
      </dependency>

	<dependency>
		<groupId>com.github.colin-lee</groupId>
		<artifactId>spring-support</artifactId>
        <exclusions>
            <exclusion>
                <artifactId>java-utils</artifactId>
                <groupId>com.fxiaoke.common</groupId>
            </exclusion>
            <exclusion>
                <artifactId>config-core</artifactId>
                <groupId>com.github.colin-lee</groupId>
            </exclusion>
            <exclusion>
                <artifactId>joda-time</artifactId>
                <groupId>joda-time</groupId>
            </exclusion>
            <exclusion>
                <artifactId>jcl-over-slf4j</artifactId>
                <groupId>org.slf4j</groupId>
            </exclusion>
        </exclusions>
    </dependency>
	
	<dependency>
        <groupId>com.facishare.qixin.plugin</groupId>
        <artifactId>fs-qixin-plugin-api</artifactId>
        <exclusions>
            <exclusion>
                <artifactId>fs-qixin-api</artifactId>
                <groupId>com.facishare</groupId>
            </exclusion>
        </exclusions>
    </dependency>

      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>i18n-client</artifactId>
      </dependency>

      <dependency>
          <groupId>joda-time</groupId>
          <artifactId>joda-time</artifactId>
          <version>2.10.14</version>
      </dependency>

      <dependency>
          <groupId>org.apache.rocketmq</groupId>
          <artifactId>rocketmq-client</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>okio-jvm</artifactId>
                  <groupId>com.squareup.okio</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-rocketmq-support</artifactId>
      </dependency>

      <dependency>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-configuration2</artifactId>
      </dependency>

      <dependency>
          <groupId>commons-configuration</groupId>
          <artifactId>commons-configuration</artifactId>
          <exclusions>
              <exclusion>
                  <artifactId>commons-logging</artifactId>
                  <groupId>commons-logging</groupId>
              </exclusion>
          </exclusions>
      </dependency>

      <dependency>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
      </dependency>


      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>fs-open-msg-export-api</artifactId>
      </dependency>

      <dependency>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-all</artifactId>
          <version>1.10.19</version>
          <scope>test</scope>
      </dependency>

  </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
