<?xml version="1.0" encoding="UTF-8"?>
<beans
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd">


    <!--<context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.msg"/>

    <import resource="classpath:/spring/spring-dubbo-msg-config.xml"/>
    <import resource="classpath:/spring/fs-open-msg-provider.xml"/>
    <import resource="classpath:/spring/fs-open-msg-service.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>
    <import resource="classpath:/spring/spring-db.xml"/>
    <import resource="classpath:/spring/spring-aop.xml"/>

    <bean id="redisFactory" class="com.facishare.open.common.storage.redis.RedisTemplate">
        <property name="hosts" value="${fs.open.redis.url}"></property>
        <property name="password" value="${fs.open.redis.password}"></property>
        <property name="masterName" value="${fs.open.redis.mastername}"></property>
    </bean>

    <bean id="jmxConfig" class="com.facishare.open.msg.jmx.JmxConfig">
        <property name="jmxPort" value="${fs.open.apps.heart.beat.port}"></property>
    </bean>


    <beans profile="default">
        <context:property-placeholder ignore-resource-not-found="true" location="classpath:FTE2/*.properties"/>
    </beans>
    <beans profile="FTE2">
        <context:property-placeholder ignore-resource-not-found="true" location="classpath:FTE2/*.properties"/>
    </beans>
    <beans profile="FTE">
        <context:property-placeholder ignore-resource-not-found="true" location="classpath:FTE/*.properties"/>
    </beans>
    <beans profile="SDE">
        <context:property-placeholder ignore-resource-not-found="true" location="classpath:SDE/*.properties"/>
    </beans>
    <beans profile="PRODUCE">
        <context:property-placeholder ignore-resource-not-found="true" location="classpath:PRODUCE/*.properties"/>
    </beans>-->

</beans>