<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-lazy-init="false">

    <!--客服的消息从企信转给微信用户，开平消息系统把消息丢到这个MQ, 然后由微信对接模块从MQ中消费下来，转换成微信识别的消息类型，用微信接口推送给用户-->
    <!--fixme 专属云调用rest 钟兴-->
<!--    <bean id="qixinMsgToWechatSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">-->
<!--        &lt;!&ndash;        <constructor-arg name="configName" value="open-msg-wechat-proxy-mq"/>&ndash;&gt;-->
<!--        <constructor-arg index="0" value="open-msg-wechat-proxy-mq"/>-->
<!--    </bean>-->

<!--    <bean class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
<!--        <constructor-arg name="configName" value="fs-open-msg-receive-mq"/>-->
<!--        <constructor-arg name="listener" ref="receiveUpMsgServiceImpl"/>-->
<!--    </bean>-->
    <bean  id="receiveUpMsgServiceImpl" class="com.facishare.open.msg.listener.ReceiveUpMsgServiceImpl" init-method="init">
        <constructor-arg index="0" value="fs-open-msg-receive-mq"></constructor-arg>
        <constructor-arg index="1" value="name.server"></constructor-arg>
        <constructor-arg index="2" value="consumer.group"></constructor-arg>
        <constructor-arg index="3" value="consume.topic"></constructor-arg>
    </bean>

    <!--接收加号插件事件-->
<!--    <bean class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
<!--        <constructor-arg name="configName" value="fs-open-plugin-receive-mq"/>-->
<!--        <constructor-arg name="listener" ref="receivePluginMsgServiceImpl"/>-->
<!--    </bean>-->
    <bean  id="receivePluginMsgServiceImpl" class="com.facishare.open.msg.listener.ReceivePluginMsgServiceImpl" init-method="init">
        <constructor-arg index="0" value="fs-open-plugin-receive-mq"></constructor-arg>
        <constructor-arg index="1" value="name.server"></constructor-arg>
        <constructor-arg index="2" value="consumer.group"></constructor-arg>
        <constructor-arg index="3" value="consume.topic"></constructor-arg>
    </bean>


    <!--发送消息后用于接收消息的执行结果获取sessionId和messageId-->
   <!-- <bean class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">
        <constructor-arg name="configName" value="fs-open-msg-callback"/>
        <constructor-arg name="listener" ref="receiveOpenMessageCallbackService"/>
    </bean>-->

    <bean  id="receiveOpenMessageCallbackService" class="com.facishare.open.msg.listener.ReceiveOpenMessageCallbackServiceImpl" init-method="init">
        <constructor-arg index="0" value="fs-open-msg-callback"></constructor-arg>
        <constructor-arg index="1" value="name.server"></constructor-arg>
        <constructor-arg index="2" value="consumer.group"></constructor-arg>
        <constructor-arg index="3" value="consume.topic"></constructor-arg>
    </bean>

</beans>