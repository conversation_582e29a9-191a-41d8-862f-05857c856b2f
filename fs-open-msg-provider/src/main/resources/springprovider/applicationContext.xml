<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:p="http://www.springframework.org/schema/p"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	   http://www.springframework.org/schema/beans/spring-beans.xsd
	   http://www.springframework.org/schema/context
	   http://www.springframework.org/schema/context/spring-context.xsd"
	   default-lazy-init="false">

	<context:annotation-config/>
	<context:component-scan base-package="com.facishare.open.msg"/>

	<import resource="classpath:spring/ei-ea-converter.xml"/>

	<import resource="classpath:springprovider/fs-open-msg-provider.xml"/>
	<import resource="classpath:springprovider/fs-open-msg-service.xml"/>
	<import resource="classpath:springprovider/spring-mq.xml"/>
	<import resource="classpath:springprovider/spring-db.xml"/>
	<import resource="classpath:springprovider/spring-redis.xml"/>
	<import resource="classpath:springprovider/spring-aop.xml"/>
	<import resource="classpath:springprovider/fs-fsi-proxy-service.xml"/>

	<!--企业互联rest API-->
	<import resource="classpath:enterpriserelation2/enterpriserelation.xml"/>

	<!--fs-orgainzation-adapter-api -->
	<import resource="classpath:spring/fs-organization-adapter-api-client.xml"/>

	<!--fs-orgainzation-api -->
	<import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

	<!--监控及日志中心-->
	<bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
</beans>
