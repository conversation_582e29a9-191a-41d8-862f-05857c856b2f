<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 消费者角色   -->

    <!-- 企信服务 -->
    <dubbo:reference id="openSessionService" interface="com.facishare.qixin.api.open.OpenSessionService" protocol="dubbo" />
    <dubbo:reference id="openCrossDefinitionService" interface="com.facishare.qixin.api.open.OpenCrossDefinitionService" protocol="dubbo" />
    <dubbo:reference id="openCrossMessageService" interface="com.facishare.qixin.api.open.OpenCrossMessageService" protocol="dubbo" timeout="20000"/>
    <dubbo:reference id="openCrossSessionService" interface="com.facishare.qixin.api.open.OpenCrossSessionService" protocol="dubbo" />
    <dubbo:reference id="openSendMessageService" interface="com.facishare.qixin.plugin.service.OpenSendMessageService" protocol="dubbo" />
    <!--企信老接口-->
    <dubbo:reference id="openSessionServiceDeprecated" interface="com.facishare.qixin.api.service.SessionService" protocol="dubbo"  />
    <dubbo:reference id="openMessageService" interface="com.facishare.qixin.api.open.OpenMessageService" protocol="dubbo" timeout="10000" retries="0" />
    <dubbo:reference id="openMessageBatchService" interface="com.facishare.qixin.api.open.OpenMessageBatchService" protocol="dubbo" timeout="10000"  retries="0"/>
    <dubbo:reference id="avMessageService" interface="com.facishare.qixin.api.open.AvMessageService" protocol="dubbo" />
    <dubbo:reference id="openMessageC2CService" interface="com.facishare.qixin.api.open.OpenMessageC2CService" protocol="dubbo" />
    <dubbo:reference id="uniformOpenMessageService" interface="com.facishare.qixin.api.open.UniformOpenMessageService" protocol="dubbo" />
    <dubbo:reference id="openCustomService" interface="com.facishare.qixin.api.open.OpenCustomService" protocol="dubbo" />
    <dubbo:reference id="openCrossCustomerService" interface="com.facishare.qixin.api.open.OpenCrossCustomerService" protocol="dubbo" timeout="5000" check="false" />
    <!--群会话管理-->
    <dubbo:reference id="weixinSessionService" interface="com.facishare.qixin.api.weixin.WeixinSessionService" protocol="dubbo"  check="false"/>
    <!--群消息投递-->
    <dubbo:reference id="weixinMessageService" interface="com.facishare.qixin.api.weixin.WeixinMessageService" protocol="dubbo" check="false"/>

    <!-- 应用中心  -->
    <dubbo:reference id="templateService" interface="com.facishare.open.app.center.api.service.template.TemplateService" protocol="dubbo" version="1.0"/>
    <dubbo:reference id="openAppNameService" interface="com.facishare.open.app.center.api.service.OpenAppOrComponentNameService" protocol="dubbo" version="1.0"/>
    <dubbo:reference id="openAppAdminService" interface="com.facishare.open.app.center.api.service.OpenAppAdminService"  version="1.3" />

    <!-- 应用中心回调事件 service -->
    <dubbo:reference id="customMenuService" interface="com.facishare.open.custom.menu.api.service.CustomMenuService" protocol="dubbo" version="1.0" />


    <!-- 素材库 -->
    <dubbo:reference id="materialMessageService" interface="com.facishare.open.material.api.service.MaterialMessageService" protocol="dubbo" version="1.0"/>

    <!-- oauth -->
    <dubbo:reference id="eaAuthService" interface="com.facishare.open.oauth.service.EaAuthService" protocol="dubbo" version="1.1"/>
    <dubbo:reference id="openUserIdService" interface="com.facishare.open.oauth.service.OpenUserIdService" protocol="dubbo" version="1.1"/>
    <dubbo:reference id="appService" interface="com.facishare.open.oauth.service.AppService" protocol="dubbo" timeout="5000" version="1.1" check="false" />
    <dubbo:reference id="callBackService" interface="com.facishare.open.callback.service.CallBackService" protocol="dubbo" version="1.0"/>

</beans>
