<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

<!--    <bean id="jedisSupport" class="com.github.jedis.support.JedisFactoryBean" p:configName="redis-open-msg"/>-->
<!--    <bean id="redisFactory" class="com.facishare.open.common.storage.redis.RedisTemplate">-->
<!--        <property name="jedisCmd" ref="jedisSupport"/>-->
<!--    </bean>-->

 <!--   <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="redis-open-msg" />
    </bean>
-->
</beans>
