<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <appender name="ROLLING-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.base}/logs/msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/msg-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
            <maxHistory>30</maxHistory>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%X{traceId}]-[%X{consumeProcessName}]-[%class:%line] - %msg%n</pattern>
        </encoder>
    </appender>
    
    <appender name="LISTENER-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.base}/logs/eventListener.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/eventListener-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
            <maxHistory>30</maxHistory>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>1GB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%X{traceId}]-[%X{consumeProcessName}]-[%class:%line] - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.facishare.open.msg.listener" level="info" additivity="false">
        <appender-ref ref="LISTENER-FILE"/>
    </logger>

    <logger name="com.facishare" level="info" additivity="false">
        <appender-ref ref="ROLLING-FILE"/>
    </logger>
    
    <root level="ERROR">
        <appender-ref ref="ROLLING-FILE" />
    </root>
</configuration>