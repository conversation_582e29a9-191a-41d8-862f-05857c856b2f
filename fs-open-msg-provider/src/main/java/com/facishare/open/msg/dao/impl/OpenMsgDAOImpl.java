package com.facishare.open.msg.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constants.MsgOperConstants;
import com.facishare.open.msg.dao.OpenMsgDAO;
import com.facishare.open.msg.dao.base.MsgBaseDAO;
import com.facishare.open.msg.model.OpenMsgDO;
import com.mongodb.AggregationOptions;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.Cursor;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import com.mongodb.QueryOperators;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.aggregation.AggregationPipeline;
import org.mongodb.morphia.aggregation.Group;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2016年3月1日
 */
@Component
public class OpenMsgDAOImpl extends MsgBaseDAO<OpenMsgDO> implements OpenMsgDAO {

    private static final Logger logger = LoggerFactory.getLogger(OpenMsgDAOImpl.class);

    @Override
    public boolean addOpenMsg(OpenMsgDO openMsgDO) {
        openMsgDO.setCreateTimeForExpire(new Date());
        Key<OpenMsgDO> key = getDatastore().save(openMsgDO);
        return key.getId() == null ? false : true;
    }

    @Override
    public boolean addOpenMsgBatch(List<OpenMsgDO> openMsgs) {
        Iterable<Key<OpenMsgDO>> keys = getDatastore().save(openMsgs);
        return keys == null ? false : true;
    }

    @Override
    public OpenMsgDO getOpenMsgByMsgId(long msgId) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("msgId").equal(msgId);
        return query.get();
    }

    @Override
    public OpenMsgDO updateOpenMsgByPostId(OpenMsgDO openMsgDO) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("postId").equal(openMsgDO.getPostId());
/*        // 过滤撤销的消息
        query.criteria("status").notEqual(3);*/

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("status", openMsgDO.getStatus());
        update.set("msgId", openMsgDO.getMsgId());
        update.set("updateTime", openMsgDO.getUpdateTime());
        update.set("messageTime", openMsgDO.getMessageTime());
        update.set("response", openMsgDO.getResponse());

        return getDatastore().findAndModify(query, update, false, false);
    }

    @Override
    public OpenMsgDO getOpenMsgByPostId(String ea, String appId, String postId) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("postId").equal(postId);
        query.criteria("enterpriseAccount").equal(ea);
        //这里发送者是appid
        query.criteria("sender").equal(appId);
        //只要完全发送成功的
        query.criteria("status").equal(1);
        return query.get();
    }

    @Override
    public OpenMsgDO getOpenMsgByRecentlyByPostId(String ea, String appId, String postId) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("postId").equal(postId);
        query.criteria("enterpriseAccount").equal(ea);
        //这里发送者是appid
        query.criteria("sender").equal(appId);
        //只要完全发送成功的
        query.criteria("status").equal(1);
        //查询最近的数据，太久说明已经出问题了
        query.criteria("updateTime").greaterThan(System.currentTimeMillis()-6L*3600*1000);
        return query.get();
    }

    @Override
    public OpenMsgDO updateOpenMsg(OpenMsgDO openMsgDO) {
        if (Objects.isNull(openMsgDO) || Objects.isNull(openMsgDO.getId())) {
            return openMsgDO;
        }
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("id").equal(openMsgDO.getId());

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        if (!Objects.isNull(openMsgDO.getMsgId()) && openMsgDO.getMsgId() > 0) {
            update.set("msgId", openMsgDO.getMsgId());
        }
        if (!Objects.isNull(openMsgDO.getMessageTime())) {
            update.set("messageTime", openMsgDO.getMessageTime());
        }
        if (!Objects.isNull(openMsgDO.getSessionId())) {
            update.set("sessionId", openMsgDO.getSessionId());
        }
        if (StringUtils.isNotEmpty(openMsgDO.getResponse())) {
            update.set("response", openMsgDO.getResponse());
        }
        if (StringUtils.isNotEmpty(openMsgDO.getContent())) {
            update.set("content", openMsgDO.getContent());
        }
        update.set("updateTime", openMsgDO.getUpdateTime());

        return getDatastore().findAndModify(query, update, false, false);
    }

    @Override
    public boolean updateOpenMsgStatus(String id, int status) {
        if (!ObjectId.isValid(id)) {
            return false;
        }

        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("id").equal(new ObjectId(id));
        /*// 过滤撤销的消息
        query.criteria("status").notEqual(3);*/

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("status", status);
        update.set("updateTime", System.currentTimeMillis());

        UpdateResults result = getDatastore().update(query, update, false);
        return result.getUpdatedExisting();
    }

    @Override
    public Pager<OpenMsgDO> queryAllUserSessions(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();
        int status = (int) params.get("status");

        long startTime = System.currentTimeMillis();
        // 1. 获取每个单人会话消息最新的msgId
        String regex = String.format("^%s", params.get("appId"));
        Pattern pattern = Pattern.compile(regex);

        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());

        BasicDBObject match = new BasicDBObject();
        match.append("enterpriseAccount", params.get("ea"));
        match.append("sessionId", pattern);
        match.put("sendType", new BasicDBObject(QueryOperators.IN, validSendTypes));

        /*// 过滤撤销的消息
        match.append("status", new BasicDBObject(QueryOperators.NE, 3));*/
        // 过滤企信发送失败的消息
        match.append("msgId", new BasicDBObject(QueryOperators.GT, 0L));
        if (pager.getCurrentPage() > 1) {
            match.append("msgId", new BasicDBObject(QueryOperators.LTE, params.get("lastMessageId")));
        }

        BasicDBObject group = new BasicDBObject();
        BasicDBObject _id = new BasicDBObject();
        _id.put("sessionId", "$sessionId");
        group.put("_id", _id);
        group.put("maxMsgId", new BasicDBObject(QueryOperators.MAX, "$msgId"));

        BasicDBObject sort = new BasicDBObject();
        sort.put("maxMsgId", -1);

        List<DBObject> pipeline = new ArrayList<>();
        pipeline.add(new BasicDBObject("$match", match));
        pipeline.add(new BasicDBObject("$group", group));
        pipeline.add(new BasicDBObject("$sort", sort));


        AggregationOutput output = getCollection().aggregate(pipeline);

        // 2. 根据第一步获得的msgId获取详细记录
        List<Long> msgIdsList = new ArrayList<>();
        long totalRecordSize = 0;
        for (DBObject dbObject : output.results()) {
            msgIdsList.add(Long.valueOf(dbObject.get("maxMsgId").toString()));
        }

        long aggregationTime = System.currentTimeMillis();
        logger.info("queryAllUserSessions aggregation param={}, msgIdsList.size={} timecost={}", pager, msgIdsList.size(), (aggregationTime - startTime));

        // 3. 封装查询结果
        List<OpenMsgDO> data = new ArrayList<>();
        if (!msgIdsList.isEmpty()) {
            Query<OpenMsgDO> query = this.createQuery();
            query.criteria("enterpriseAccount").equal(params.get("ea"));
            query.criteria("sessionId").startsWith(params.get("appId") + "-");
            query.criteria("msgId").in(msgIdsList);

            if (status == MsgOperConstants.PROCESS_STATUS_UNREAD) {
                query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
            } else if (status == MsgOperConstants.PROCESS_STATUS_UNREPLIED) {
                List<Integer> validProcessStatus = Lists.newArrayList();
                validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
                validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_READ_UNREPLIED);

                query.criteria("processStatus").in(validProcessStatus);
            }

            query.order("-msgId");
            query.offset(pager.offset());
            query.limit(pager.getPageSize());

            data = query.asList();
            totalRecordSize = query.countAll();
        }

        long queryMaxTime = System.currentTimeMillis();
        logger.info("queryAllUserSessions queryMaxMsg param={}, msgIdsList.size={} timecost={}", pager, msgIdsList.size(), (queryMaxTime - aggregationTime));

        pager.setData(data);
        pager.setRecordSize(Long.valueOf(totalRecordSize).intValue());

        return pager;
    }


    @Override
    public Pager<OpenMsgDO> queryEaConnAllUserSessions(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();
        int status = (int) params.get("status");

        // 1. 获取每个单人会话消息最新的msgId
        String regex = String.format("^%s", params.get("appId"));
        Pattern pattern = Pattern.compile(regex);

        BasicDBList validSendTypes = new BasicDBList();
        //validSendTypes.add(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType());

        BasicDBObject match = new BasicDBObject();
        match.append("upEnterpriseAccount", params.get("upEnterpriseAccount"));
        match.put("sendType", new BasicDBObject(QueryOperators.IN, validSendTypes));

       /* // 过滤撤销的消息
        match.append("status", new BasicDBObject(QueryOperators.NE, 3))*/;
        // 过滤企信发送失败的消息
        match.append("msgId", new BasicDBObject(QueryOperators.GT, 0L));
        if (pager.getCurrentPage() > 1) {
            match.append("msgId", new BasicDBObject(QueryOperators.LTE, params.get("lastMessageId")));
        }
        match.append("sessionId", pattern);

        BasicDBObject group = new BasicDBObject();
        BasicDBObject _id = new BasicDBObject();
        _id.put("sessionId", "$sessionId");
        group.put("_id", _id);
        group.put("maxMsgId", new BasicDBObject(QueryOperators.MAX, "$msgId"));

        BasicDBObject sort = new BasicDBObject();
        sort.put("maxMsgId", -1);

        List<DBObject> pipeline = new ArrayList<>();
        pipeline.add(new BasicDBObject("$match", match));
        pipeline.add(new BasicDBObject("$group", group));
        pipeline.add(new BasicDBObject("$sort", sort));

        AggregationOutput output = getCollection().aggregate(pipeline);

        // 2. 根据第一步获得的msgId获取详细记录
        List<Long> msgIdsList = new ArrayList<>();
        long totalRecordSize = 0;
        for (DBObject dbObject : output.results()) {
            msgIdsList.add(Long.valueOf(dbObject.get("maxMsgId").toString()));
        }

        // 3. 封装查询结果
        List<OpenMsgDO> data = new ArrayList<>();
        if (!msgIdsList.isEmpty()) {
            Query<OpenMsgDO> query = this.createQuery();
            query.criteria("upEnterpriseAccount").equal(params.get("upEnterpriseAccount"));
            query.criteria("msgId").in(msgIdsList);

            if (status == MsgOperConstants.PROCESS_STATUS_UNREAD) {
                query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
            } else if (status == MsgOperConstants.PROCESS_STATUS_UNREPLIED) {
                List<Integer> validProcessStatus = Lists.newArrayList();
                validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
                validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_READ_UNREPLIED);

                query.criteria("processStatus").in(validProcessStatus);
            }

            query.order("-msgId");
            query.offset(pager.offset());
            query.limit(pager.getPageSize());

            data = query.asList();
            totalRecordSize = query.countAll();
        }

        pager.setData(data);
        pager.setRecordSize(Long.valueOf(totalRecordSize).intValue());

        return pager;
    }


    @Override
    public Pager<OpenMsgDO> querySingleUserSessionMessages(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();

        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("enterpriseAccount").equal(params.get("ea"));
        query.criteria("sessionId").equal(params.get("sessionId"));

        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        // 过滤企信发送失败的消息
        query.criteria("msgId").greaterThan(0L);
        if (pager.getCurrentPage() > 1) {
            query.criteria("msgId").lessThan(params.get("lastMessageId"));
        }

        List<Integer> validSendTypes = new ArrayList<>();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());
        query.criteria("sendType").in(validSendTypes);

        query.order("-msgId");
        query.limit(pager.limit());

        List<OpenMsgDO> data = query.asList();
        long currentRecordSize = query.countAll();

        // 由于每次后一页的分页范围比上一次缩小,需加上前一次的offset
        int totalRecordSize = Long.valueOf(currentRecordSize).intValue() + pager.offset();

        pager.setData(data);
        pager.setRecordSize(totalRecordSize);

        return pager;
    }

    @Override
    public Pager<OpenMsgDO> queryEaConnSingleUserSessionMessages(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();

        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("upEnterpriseAccount").equal(params.get("upEnterpriseAccount"));
        query.criteria("sessionId").equal(params.get("sessionId"));

        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        // 过滤企信发送失败的消息
        query.criteria("msgId").greaterThan(0L);
        if (pager.getCurrentPage() > 1) {
            query.criteria("msgId").lessThan(params.get("lastMessageId"));
        }

        List<Integer> validSendTypes = new ArrayList<>();
        // validSendTypes.add(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType());
        query.criteria("sendType").in(validSendTypes);

        query.order("-msgId");
        query.limit(pager.limit());

        List<OpenMsgDO> data = query.asList();
        long currentRecordSize = query.countAll();

        // 由于每次后一页的分页范围比上一次缩小,需加上前一次的offset
        int totalRecordSize = Long.valueOf(currentRecordSize).intValue() + pager.offset();

        pager.setData(data);
        pager.setRecordSize(totalRecordSize);

        return pager;
    }

    @Override
    public boolean updateSingleUserSessionStatus(String ea, String sessionId, long lastMessageId, int status) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sessionId").equal(sessionId);
        query.criteria("msgId").lessThanOrEq(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        List<Integer> validProcessStatus = Lists.newArrayList();
        validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
        validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_READ_UNREPLIED);
        query.criteria("processStatus").in(validProcessStatus);

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("processStatus", status);
        update.set("updateTime", System.currentTimeMillis());

        UpdateResults result = getDatastore().update(query, update, false);
        return result.getUpdatedExisting();
    }

    @Override
    public boolean updateEaConnSingleUserSessionStatus(String ea, String sessionId, long lastMessageId, int status) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("upEnterpriseAccount").equal(ea);
        query.criteria("sessionId").equal(sessionId);
        query.criteria("msgId").lessThanOrEq(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        List<Integer> validProcessStatus = Lists.newArrayList();
        validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
        validProcessStatus.add(MsgOperConstants.PROCESS_STATUS_READ_UNREPLIED);
        query.criteria("processStatus").in(validProcessStatus);

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("processStatus", status);
        update.set("updateTime", System.currentTimeMillis());

        UpdateResults result = getDatastore().update(query, update, false);
        return result.getUpdatedExisting();
    }

    @Override
    public long countUnReadUserSessions(String ea, String appId, long lastMessageId) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(ea);
        query.or(query.criteria("sender").equal(appId), query.criteria("receiver").equal(appId));
        query.criteria("msgId").greaterThan(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);

        return query.countAll();
    }


    @Override
    public long countEaConnUnReadUserSessions(String ea, String appId, long lastMessageId) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("upEnterpriseAccount").equal(ea);
        query.or(query.criteria("sender").equal(appId), query.criteria("receiver").equal(appId));
        query.criteria("msgId").greaterThan(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);

        return query.countAll();
    }

    @Override
    public long countSingleUserSessionUnReadMessages(String ea, String sessionId, long lastMessageId) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sessionId").equal(sessionId);
        query.criteria("msgId").greaterThan(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);

        return query.countAll();
    }

    @Override
    public long countEaConnSingleUserSessionUnReadMessages(String upStreamEa, String sessionId, long lastMessageId) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("upEnterpriseAccount").equal(upStreamEa);
        query.criteria("sessionId").equal(sessionId);
        query.criteria("msgId").greaterThan(lastMessageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);

        return query.countAll();
    }


    private Boolean doQueryUnReadUserSessionsByAppId(String ea, String appId) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("processStatus").equal(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
        query.criteria("receiver").equal(appId);
        query.hintIndex("processStatus_1_enterpriseAccount_1");
        query.limit(1);

        List<OpenMsgDO> data = query.asList();
        if(CollectionUtils.isEmpty(data)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public Map<String, Boolean> queryUnReadUserSessionsByAppIds(String ea, List<String> appIds) {
        Map<String, Boolean> result = Maps.newHashMap();
       for(String appid: appIds) {
           boolean existUnreadSession = doQueryUnReadUserSessionsByAppId(ea, appid);
           result.put(appid, existUnreadSession);
       }
        return result;
    }

    @Override
    public Map<String, Boolean> queryEaConnUnReadUserSessionsByAppIds(String upStreamEa, List<String> appIds) {
        Map<String, Boolean> result = Maps.newHashMap();

        BasicDBObject match = new BasicDBObject();
        match.append("processStatus", MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
        match.append("upEnterpriseAccount", upStreamEa);
        match.put("receiver", new BasicDBObject(QueryOperators.IN, appIds));
        // 过滤撤销的消息
        //match.append("status", new BasicDBObject(QueryOperators.NE, 3));

        BasicDBObject group = new BasicDBObject();
        group.put("_id", "$receiver");
        group.put("count", new BasicDBObject("$sum", 1));

        List<DBObject> pipeline = new ArrayList<>();
        pipeline.add(new BasicDBObject("$match", match));
        pipeline.add(new BasicDBObject("$group", group));

        AggregationOutput output = getCollection().aggregate(pipeline);

        for (DBObject object : output.results()) {
            result.put(object.get("_id").toString(), Boolean.TRUE);
        }

        return result;
    }

    @Override
    public boolean updateMsgStarStatus(String ea, long messageId, String markUserId, int starStatus, boolean isEaUpStream) {
        Query<OpenMsgDO> query = this.createQuery();

        if (isEaUpStream) {
            query.criteria("upEnterpriseAccount").equal(ea);
        } else {
            query.criteria("enterpriseAccount").equal(ea);
        }
        query.criteria("msgId").equal(messageId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("starMark", starStatus);
        update.set("updateTime", System.currentTimeMillis());
        update.set("markUserId", markUserId);

        UpdateResults result = getDatastore().update(query, update, false);
        return result.getUpdatedExisting();
    }


    @Override
    public Pager<OpenMsgDO> queryAllStarMessages(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();

        Query<OpenMsgDO> query = this.createQuery();

        // 1. 获取每个单人会话消息最新的msgId
        String appId = (String) params.get("appId");

        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());


        // 过滤企信发送失败的消息
        query.criteria("enterpriseAccount").equal(params.get("ea"));
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").greaterThan(0L);
        query.criteria("starMark").equal(1);
        query.criteria("sendType").in(validSendTypes);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        // 3. 封装查询结果
        List<OpenMsgDO> data = new ArrayList<>();

        query.order("-msgId");
        query.offset(pager.offset());
        query.limit(pager.getPageSize());

        data = query.asList();
        long totalRecordSize = query.countAll();

        pager.setData(data);
        pager.setRecordSize(Long.valueOf(totalRecordSize).intValue());

        return pager;
    }


    @Override
    public Pager<OpenMsgDO> queryEaConnAllStarMessages(Pager<OpenMsgDO> pager) {
        Map<String, Object> params = pager.getParams();

        Query<OpenMsgDO> query = this.createQuery();

        // 1. 获取每个单人会话消息最新的msgId
        String appId = (String) params.get("appId");

        BasicDBList validSendTypes = new BasicDBList();
        // validSendTypes.add(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType());


        // 过滤企信发送失败的消息
        query.criteria("upEnterpriseAccount").equal(params.get("upEnterpriseAccount"));
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").greaterThan(0L);
        query.criteria("starMark").equal(1);
        query.criteria("sendType").in(validSendTypes);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        // 3. 封装查询结果
        List<OpenMsgDO> data = new ArrayList<>();

        query.order("-msgId");
        query.offset(pager.offset());
        query.limit(pager.getPageSize());

        data = query.asList();
        long totalRecordSize = query.countAll();

        pager.setData(data);
        pager.setRecordSize(Long.valueOf(totalRecordSize).intValue());

        return pager;
    }

    @Override
    public List<OpenMsgDO> queryAllExportMessages(Long beginTime, Long endTime,
                                                  Map<String, Object> paramMap) {
        // TODO Auto-generated method stub

        Query<OpenMsgDO> query = this.createQuery();

        // 1. 获取每个单人会话消息最新的msgId
        String appId = (String) paramMap.get("appId");

        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());
        query.criteria("enterpriseAccount").equal(paramMap.get("ea"));

        // 过滤企信发送失败的消息
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").greaterThan(0L);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        if (beginTime != null) {
            query.criteria("messageTime").greaterThanOrEq(beginTime);
        }

        if (endTime != null) {
            query.criteria("messageTime").lessThanOrEq(endTime);
        }

        if (paramMap.get("starMark") != null) {
            query.criteria("starMark").equal((Integer) paramMap.get("starMark"));
        }
        query.criteria("sendType").in(validSendTypes);

        query.order("-messageTime");
        // 3. 封装查询结果
        query.offset(0);
        query.limit(10000);

        List<OpenMsgDO> dataList = query.asList();

        return dataList;
    }

    @Override
    public List<OpenMsgDO> queryAllCrossExportMessages(Long beginTime, Long endTime,
                                                       Map<String, Object> paramMap) {
        // TODO Auto-generated method stub

        Query<OpenMsgDO> query = this.createQuery();

        // 1. 获取每个单人会话消息最新的msgId
        String appId = (String) paramMap.get("appId");

        BasicDBList validSendTypes = new BasicDBList();
        //validSendTypes.add(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType());
        query.criteria("upEnterpriseAccount").equal(paramMap.get("ea"));

        // 过滤企信发送失败的消息
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").greaterThan(0L);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        if (beginTime != null) {
            query.criteria("messageTime").greaterThanOrEq(beginTime);
        }

        if (endTime != null) {
            query.criteria("messageTime").lessThanOrEq(endTime);
        }

        if (paramMap.get("starMark") != null) {
            query.criteria("starMark").equal((Integer) paramMap.get("starMark"));
        }
        query.criteria("sendType").in(validSendTypes);

        query.order("-messageTime");
        // 3. 封装查询结果
        query.offset(0);
        query.limit(10000);

        List<OpenMsgDO> dataList = query.asList();

        return dataList;
    }

    @Override
    public long countExportMessages(Long beginTime, Long endTime, Map<String, Object> paramMap) {

        Query<OpenMsgDO> query = this.createQuery();

        // 1. 获取每个单人会话消息最新的msgId
        String appId = (String) paramMap.get("appId");

        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());


        // 过滤企信发送失败的消息
        query.criteria("enterpriseAccount").equal(paramMap.get("ea"));
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").greaterThan(0L);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        if (beginTime != null) {
            query.criteria("messageTime").greaterThanOrEq(beginTime);
        }

        if (endTime != null) {
            query.criteria("messageTime").lessThanOrEq(endTime);
        }

        if (paramMap.get("starMark") != null) {
            query.criteria("starMark").equal((Integer) paramMap.get("starMark"));
        }
        query.criteria("sendType").in(validSendTypes);

        // 3. 封装查询结果
        long totalRecordSize = query.countAll();

        return totalRecordSize;
    }

    @Override
    public long countSendNumByAppId(String ea, String appId, int sendType) {
        Query<OpenMsgDO> query = this.createQuery();

        // 过滤企信发送失败的消息
        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sender").equal(appId);
        query.criteria("sendType").equal(sendType);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        // 3. 封装查询结果
        long totalRecordSize = query.countAll();

        return totalRecordSize;
    }

    @Override
    public long countReceiveNumByAppId(String ea, String appId) {
        Query<OpenMsgDO> query = this.createQuery();

        // 过滤企信发送失败的消息
        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);

        /**一个AppID要么是内部服务号，要么是外联服务号。所以这里的or不会有有问题。*/
        query.or(query.criteria("sendType").equal(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType()),
                query.criteria("sendType").equal(MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL.getType()));

        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        // 3. 封装查询结果
        long totalRecordSize = query.countAll();

        return totalRecordSize;
    }


    @Override
    @Deprecated
    public boolean updateOpenMsgRevoke(String ea, String appId, Long msgId, boolean isCross) {

        Query<OpenMsgDO> query = this.createQuery();
        if (isCross) {
            query.criteria("upEnterpriseAccount").equal(ea);
        } else {
            query.criteria("enterpriseAccount").equal(ea);
        }
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.criteria("msgId").equal(msgId);

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("status", 3);
        update.set("updateTime", System.currentTimeMillis());

        UpdateResults result = getDatastore().update(query, update, false);
        return result.getUpdatedExisting();
    }

    @Override
    public void deleteOpenMsgRevoke(String ea, String appId, Long msgId, boolean isCross) {
        Query<OpenMsgDO> query = this.createQuery();
        if (isCross) {
            query.criteria("upEnterpriseAccount").equal(ea);
        } else {
            query.criteria("enterpriseAccount").equal(ea);
        }
        query.criteria("sessionId").startsWith(appId + "-");
        query.criteria("msgId").equal(msgId);

        OpenMsgDO deletedEntity = getDatastore().findAndDelete(query);
        logger.info("trace deleteOpenMsgRevoke by msgid success, deleted entity = {}", deletedEntity);
    }

    /**
     * 删除撤销的消息, 根据postid来删除。
     * 这类消息是 群发消息，一条记录中记录了多个消息接受者，msgid为-1.
     *
     * @param ea
     * @param appId
     * @param postId
     */
    @Override
    public void deleteOpenMsgRevoke(String ea, String appId, String postId) {
        Query<OpenMsgDO> query = this.createQuery();
        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sender").equal(appId);
        query.criteria("postId").equal(postId);

        OpenMsgDO deletedEntity = getDatastore().findAndDelete(query);
        logger.info("trace deleteOpenMsgRevoke by postid success, deleted entity = {}", deletedEntity);
    }

    @Override
    protected Class<OpenMsgDO> getEntityClazz() {
        return OpenMsgDO.class;
    }


    @Override
    public Map<String, Long> countSendNumBySender(String appId, String ea, List<String> senders) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("sessionId").startsWith(appId + "-");//.equal(appId);
        query.or(query.criteria("sendType").equal(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType()),
                query.criteria("sendType").equal(MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL.getType()),
                query.criteria("sendType").equal(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType()));

        if (senders.size() == 1) {
            logger.info("countSendNumBySender senders.size()==1. ea:{}, appId:{}, senders:{}",ea, appId, senders);
            query.criteria("sender").equal(senders.get(0));
            return Collections.singletonMap(senders.get(0), getDatastore().getCount(query));
        }

        query.criteria("sender").in(senders);
        DBObject key = new BasicDBObject("sender", 1);
        DBObject cond = query.getQueryObject();
        DBObject initial = new BasicDBObject("total", 0);
        String reduce = "function(curr, result){result.total+=1;}";
        DBCollection collection = query.getCollection();

        DBObject dbObject = collection.group(key, cond, initial, reduce);
        logger.info("countSendNumBySender trace dbobjet: {}", dbObject);
        Map<String, Long> map = new HashMap<>();
        for (String k : dbObject.keySet()) {
            BasicDBObject basicDBObject = (BasicDBObject) dbObject.get(k);
            String sender = (String) basicDBObject.get("sender");
            Double upMsgNum = (Double) basicDBObject.get("total");
            map.put(sender, upMsgNum.longValue());
        }

        logger.info("countSendNumBySender trace map: {}", map);
        return map;
    }

    @Override
    public Map<String, Long> getLastUpMsgTime(String appId, String ea, String sender) {
        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(ea);
        query.criteria("receiver").equal(appId);
        query.criteria("sendType").equal(-3);
        query.criteria("sender").equal(sender);
        query.order("-createTime");
        query.limit(1);


        OpenMsgDO openMsgDO = query.get();
        logger.info("getLastUpMsgTime trace openMsgDO: {}", openMsgDO);

        Map<String, Long> map = new HashMap<>();
        map.put(sender, openMsgDO.getCreateTime());

        logger.info("getLastUpMsgTime trace map: {}", map);
        return map;
    }

    @Override
    public List<String> getAppIdList(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList) {

        Query<OpenMsgDO> query = this.createQuery();

        query.and(query.criteria("messageTime").greaterThanOrEq(startTime),
                query.criteria("messageTime").lessThanOrEq(endTime));
        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.addAll(sendTypeEnumList.stream().map(v -> v.getType()).collect(Collectors.toList()));
        query.criteria("sendType").in(validSendTypes);

        Iterator<MessageSendTypeEnum> iterator = sendTypeEnumList.iterator();
        for (MessageSendTypeEnum sendTypeEnum : sendTypeEnumList) {
            validSendTypes.add(sendTypeEnum.getType());
        }

        /**只获取appid字段*/
        List<String> appIDList = query.asList().stream().map(v -> v.getSender()).distinct().collect(Collectors.toList());

        return appIDList;
    }

    @Override
    public Long countMsgNum(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList) {

        Query<OpenMsgDO> query = this.createQuery();

        query.and(query.criteria("messageTime").greaterThanOrEq(startTime),
                query.criteria("messageTime").lessThanOrEq(endTime));
        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.addAll(sendTypeEnumList.stream().map(v -> v.getType()).collect(Collectors.toList()));
        query.criteria("sendType").in(validSendTypes);

        Long msgNum = query.countAll();
        return msgNum;
    }

    @Override
    public boolean updateUserMessageSessionStatus(String enterpriseAccount, String sessionId, int status) {

        Query<OpenMsgDO> query = this.createQuery();

        query.criteria("enterpriseAccount").equal(enterpriseAccount);
        query.criteria("sessionId").equal(sessionId);
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);

        List<Integer> validSendTypes = new ArrayList<>();
        validSendTypes.add(MessageSendTypeEnum.DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.KEYWORDS_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL.getType());
        validSendTypes.add(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY.getType());

        query.criteria("sendType").in(validSendTypes);
        query.order("-msgId");
        query.limit(1);


        OpenMsgDO openMsgDO = query.get();
        logger.info("getLastSessionMsg ea={} sessionId={} status={} openMsgDO: {}", enterpriseAccount, sessionId, status, openMsgDO);

        if (openMsgDO == null) {
            return true;
        }
        Query<OpenMsgDO> query2 = this.createQuery();
        query2.criteria("id").equal(openMsgDO.getId());
        // 过滤撤销的消息
        //query2.criteria("status").notEqual(3);

        UpdateOperations<OpenMsgDO> update = createUpdateOperations();
        update.set("processStatus", status);
        update.set("updateTime", System.currentTimeMillis());

        UpdateResults result = getDatastore().update(query2, update, false);
        return result.getUpdatedExisting();
    }

    public List<OpenMsgDO> queryDialogMessages(String enterpriseAccount, String appId, Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList, boolean isCross) {
        Query<OpenMsgDO> query = this.createQuery();
        //设置要返回的字段,方便数据返回
        if (isCross) {
            query.retrievedFields(true, "enterpriseAccount", "sendType", "sessionId", "adminUserId");
        } else {
            query.retrievedFields(true, "sendType", "sender", "adminUserId");
        }
        
        if (isCross) {
            query.criteria("upEnterpriseAccount").equal(enterpriseAccount);
        } else {
            query.criteria("enterpriseAccount").equal(enterpriseAccount);
        }
        query.criteria("sessionId").startsWith(appId + "-");
        // 过滤撤销的消息
        //query.criteria("status").notEqual(3);
        // 过滤失败消息
        query.criteria("msgId").greaterThan(0L);

        BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.addAll(sendTypeEnumList.stream().map(v -> v.getType()).collect(Collectors.toList()));

        query.criteria("sendType").in(validSendTypes);

        query.and(query.criteria("messageTime").greaterThanOrEq(startTime),
                query.criteria("messageTime").lessThanOrEq(endTime));

        return query.asList();
    }

    public Long countMsgNumByConditon(String enterpriseAccount, String appId, Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList, boolean distinctSessionId) {
        long statNum = 0L;
    	BasicDBList validSendTypes = new BasicDBList();
        validSendTypes.addAll(sendTypeEnumList.stream().map(v -> v.getType()).collect(Collectors.toList()));
        
    	if (distinctSessionId) {
            String regex = String.format("^%s", appId);
            Pattern pattern = Pattern.compile(regex);

            BasicDBObject matchQuery = new BasicDBObject();
            matchQuery.append("sessionId", pattern);
            matchQuery.append("enterpriseAccount", enterpriseAccount);
            matchQuery.append("messageTime", new BasicDBObject("$gte", startTime));
            matchQuery.append("messageTime", new BasicDBObject("$lte", endTime));
            matchQuery.put("sendType", new BasicDBObject(QueryOperators.IN, validSendTypes));
            
            DBObject match = new BasicDBObject("$match", matchQuery);
            DBObject groupAction = new BasicDBObject("_id", "$sessionId");
            groupAction.put("ea", new BasicDBObject("$first", "$enterpriseAccount"));
            DBObject group = new BasicDBObject("$group", groupAction);
            DBObject countGroupAction = new BasicDBObject("_id", "$ea");
            countGroupAction.put("num", new BasicDBObject("$sum", 1));
            DBObject countGroup = new BasicDBObject("$group", countGroupAction);
            Cursor cursor = getDatastore().getCollection(OpenMsgDO.class).aggregate(Lists.newArrayList(match, group, countGroup), AggregationOptions.builder().build());
            if (cursor.hasNext()) {
            	statNum = (long) (int) cursor.next().get("num");
            }
        } else {
            Query<OpenMsgDO> query = this.createQuery();

            query.criteria("enterpriseAccount").equal(enterpriseAccount);
            query.criteria("sessionId").startsWith(appId + "-");

            query.and(query.criteria("messageTime").greaterThanOrEq(startTime),
                    query.criteria("messageTime").lessThanOrEq(endTime));
            query.criteria("sendType").in(validSendTypes);
            
            statNum = query.countAll();
        }
    	
    	return statNum;
    }

}
