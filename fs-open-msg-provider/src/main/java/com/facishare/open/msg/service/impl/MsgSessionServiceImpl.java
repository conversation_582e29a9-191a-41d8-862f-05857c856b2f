package com.facishare.open.msg.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.common.storage.redis.RedisTemplate;
import com.facishare.open.msg.common.constants.CustomerSessionTypeEnum;
import com.facishare.open.msg.constants.MsgOperConstants;
import com.facishare.open.msg.dao.MsgSessionDAO;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.*;
import com.facishare.open.msg.service.MsgSessionService;
import com.facishare.open.msg.util.ImageUrlAdapter;
import com.facishare.open.msg.util.ThreadPoolUtils;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.OSS1SessionDefinition;
import com.facishare.qixin.api.model.open.arg.*;
import com.facishare.qixin.api.model.open.result.*;
import com.facishare.qixin.api.model.session.Session;
import com.facishare.qixin.api.model.session.arg.UpdateUserPropertiesArg;
import com.facishare.qixin.api.open.OpenCrossDefinitionService;
import com.facishare.qixin.api.open.OpenCrossMessageService;
import com.facishare.qixin.api.open.OpenCrossSessionService;
import com.facishare.qixin.api.open.OpenSessionService;
import com.facishare.qixin.api.service.SessionService;
import com.fxiaoke.enterpriserelation2.arg.ListAllDownstreamEasArg;
import com.fxiaoke.enterpriserelation2.arg.ListDownstreamEnterpriseSimpleInfosArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.DownstreamEnterpriseDetailVo;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 处理session
 * 
 * <AUTHOR>
 * @date 2015/8/28 14:34
 */
@Service
public class MsgSessionServiceImpl implements MsgSessionService {

	private static final Logger logger = LoggerFactory.getLogger(MsgSessionServiceImpl.class);

	@Autowired
	private OpenSessionService openSessionService;

	@Autowired
	private SessionService openSessionServiceDeprecated;

	@Autowired
	private MsgSessionDAO msgSessionDao;

	@Autowired
	RedisTemplate redisTemplate;
	@Autowired
	private EIEAConverter eIEAConverter;

	// @Value("${fs.open.apps.image.source.ea}")
	@ReloadableProperty("fs.open.apps.image.source.ea")
	String ea = "fs";

	// @Value("${fs.open.apps.image.needChangeImage}")
	@ReloadableProperty("fs.open.apps.image.needChangeImage")
	boolean needChangeImage = false;
	@Autowired
	private OpenCrossDefinitionService openCrossDefinitionService;
	@Autowired
	private OpenCrossSessionService openCrossSessionService;
	@Autowired
	private OpenCrossMessageService openCrossMessageService;

	@Resource
	private EnterpriseRelationService enterpriseRelationService;
	@Resource
	private UpstreamService upstreamService;

	@Override
	public SessionResult findSession(String appID, String ea, int userID) {
		OpenFindSessionArg openFindSessionArg = new OpenFindSessionArg();
		openFindSessionArg.setAppId(appID);
		openFindSessionArg.setEnterpriseAccount(ea);
		openFindSessionArg.setEmployeeId(userID);

		OpenSessionInfoResult openSessionInfoResult = null;
		try {
			openSessionInfoResult = openSessionService.findSession(openFindSessionArg);
		} catch (Exception e) {
			logger.error("openSessionService.findSessionInfo excute error:{}", e);
		}

		SessionResult sessionResult = new SessionResult();
		if ((null != openSessionInfoResult) && (null != openSessionInfoResult.getSession())) {
			sessionResult.setSessionId(openSessionInfoResult.getSession().getSessionId());
		} else {
			sessionResult.setErrCode(MsgCodeEnum.SYSTEM_SESSION_ID_ERROR.getErrorCode());
		}
		return sessionResult;
	}

	/**
	 * 查找企业互联下 互联服务号sesssion
	 *
	 * @param appID
	 * @param upEa
	 * @param downEa
	 * @param userID
	 */
	@Override
	public SessionResult findEaConnSession(String appID, String upEa, String downEa, int userID) {
		OpenFindCrossSessionArg openFindCrossSessionArg = new OpenFindCrossSessionArg();
		openFindCrossSessionArg.setAppId(appID);
		openFindCrossSessionArg.setUpstreamEnterprise(upEa);
		openFindCrossSessionArg.setEnterpriseAccount(downEa);
		openFindCrossSessionArg.setEmployeeId(userID);
		OpenSessionInfoResult openSessionInfoResult = openCrossSessionService.findCrossSession(openFindCrossSessionArg);

		SessionResult sessionResult = new SessionResult();
		if(null != openSessionInfoResult && null != openSessionInfoResult.getSession()) {
			sessionResult.setSessionId(openSessionInfoResult.getSession().getSessionId());
		} else {
			sessionResult.setErrCode(MsgCodeEnum.SYSTEM_SESSION_ID_ERROR.getErrorCode());
		}
		return sessionResult;
	}

	/**
	 * 创建或者查询session
	 */
	@Override
	public SessionResult findOrCreateSession(String appId, String enterpriseAccount, Integer userId) {
		try {
			logger.info("findOrCreateSession param--appid:{},enterpriseAccount:{},userid:{}", appId, enterpriseAccount, userId);
			Assert.notNull(appId, "appId is null");
			Assert.notNull(enterpriseAccount, "enterpriseAccount is null");
			Assert.notNull(userId, "userId is null");
			SessionResult vo = null;
			long createtime = System.currentTimeMillis();
			Session session = openSessionService.findOrCreateSession(appId, enterpriseAccount, userId);
			String sessionId = "";
			String response = "";
			int responseStatus = MsgOperConstants.RESPONSE_FAIL;
			if (session != null) {
				vo = new SessionResult(MsgCodeEnum.SUCCESS);
				vo.setSessionId(session.getSessionId());
				sessionId = session.getSessionId();
				response = new Gson().toJson(session);
				responseStatus = MsgOperConstants.RESPONSE_SUCCESS;
			} else {
				vo = new SessionResult(MsgCodeEnum.SYSTEM_SESSION_ID_ERROR);
			}
			// 异步存储 请求信息
			this.addMsgSession(appId, enterpriseAccount, userId, createtime, sessionId, MsgOperConstants.FIND_SESSION_ACTION, response, responseStatus);
			return vo;
		} catch (IllegalArgumentException e) {
			logger.error("updateUserProperties error", e);
			return new SessionResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("excute error:{}", e);
			return new SessionResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 删除session
	 */
	@Override
	public MessageResult deleteSession(String appId, String enterpriseAccount, Integer userId, String sessionId, boolean deleteAllMessage, boolean hide) {
		try {
			logger.info("deleteSession param--appId:{},enterpriseAccount:{},userId:{}", appId, enterpriseAccount, userId);
			long createtime = System.currentTimeMillis();

			openSessionService.deleteSession(appId, enterpriseAccount, userId, sessionId, deleteAllMessage, hide);
			// 异步存储 请求信息
			this.addMsgSession(appId, enterpriseAccount, userId, createtime, sessionId, MsgOperConstants.DELETE_SESSION_ACTION, "", MsgOperConstants.RESPONSE_SUCCESS);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (Exception e) {
			logger.error("excute error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult deleteSessionSingleEnterprise(String enterpriseAccount, String appId, boolean deleteAllMessage, boolean hide) {
		DeleteSessionSingleEaVO arg = new DeleteSessionSingleEaVO();
		arg.setEnterpriseAccount(enterpriseAccount);
		arg.setAppId(appId);
		arg.setDeleteAllMessage(deleteAllMessage);
		arg.setHide(hide);
		return deleteSessionSingleEnterprise(arg);
	}

	@Override
	public MessageResult deleteSessionSingleEnterprise(DeleteSessionSingleEaVO deleteSessionSingleEaVO) {
		try {
			logger.info("deleteSessionSingleEnterprise arg:{}", deleteSessionSingleEaVO);
			long createtime = System.currentTimeMillis();
			if (CustomerSessionTypeEnum.cross.getType() == deleteSessionSingleEaVO.getSessionType()) {
				OpenDeleteAllCrossSessionArg arg = new OpenDeleteAllCrossSessionArg();
				arg.setAppId(deleteSessionSingleEaVO.getAppId());
				arg.setDeleteAllMessage(deleteSessionSingleEaVO.isDeleteAllMessage());
				arg.setHide(deleteSessionSingleEaVO.isHide());
				openCrossSessionService.deleteAllCrossSession(arg);
				
				arg.setUpstreamEnterprise(deleteSessionSingleEaVO.getEnterpriseAccount());// 上游有值，表示清除下游。没值就只清除上游
				openCrossSessionService.deleteAllCrossSession(arg);
			} else {
				OpenDeleteSessionSingleEnterpriseArg arg = new OpenDeleteSessionSingleEnterpriseArg();
				arg.setAppid(deleteSessionSingleEaVO.getAppId());
				arg.setDeleteAllMessage(deleteSessionSingleEaVO.isDeleteAllMessage());
				arg.setEnterpriseAccount(deleteSessionSingleEaVO.getEnterpriseAccount());
				arg.setHide(deleteSessionSingleEaVO.isHide());
				openSessionService.deleteSessionSingleEnterprise(arg);
			}
			// 异步存储 请求信息
			this.addMsgSession(deleteSessionSingleEaVO.getAppId(), deleteSessionSingleEaVO.getEnterpriseAccount(), 0, createtime, "--", MsgOperConstants.DELETE_SESSION_ACTION, "", MsgOperConstants.RESPONSE_SUCCESS);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (Exception e) {
			logger.error("deleteSessionSingleEnterprise error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 更换头像，session名字
	 */
	@Override
	public MessageResult updateUniversalSessionDefinition(String appId, String sessionName, String portraitPath) {

		logger.info("updateUniversalSessionDefinition param--appid:{},sessionName:{},portraitPath:{}", appId, sessionName, portraitPath);

		try {
			Assert.notNull(appId, "appId is null");
			// Assert.notNull(sessionName, "sessionName is null");
			// Assert.notNull(portraitPath, "portraitPath is null");

			String newPath = null;
			if (StringUtils.isNotEmpty(portraitPath)) {
				newPath = ImageUrlAdapter.changeUrl(portraitPath, ea, needChangeImage);
			}

			openSessionService.updateUniversalSessionDefinition(appId, sessionName, newPath);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateUserProperties error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateUniversalSessionDefinition error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult updatePublicAppServiceConfig(PublicAppServiceConfig config) {
		try {
			if (null == config.getAppId()) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}
			openSessionService.updateSessionInfo(config.getAppId(), config.getDescription(), false, true, null);
		} catch (Exception e) {
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}

		return updateUniversalSessionDefinition(config.getAppId(), config.getName(), config.getPortrait());
	}

	@Override
	public MessageResult updateSessionInfo(String appId, String description, boolean hideInputPanel, boolean showSwitch, Set<String> sendMessgeTypes) {
		logger.info("updateSessionInfo param--appid:{},description:{},hideInputPanel:{} showSwitch:{} " + "sendMessgeTypes:{}", appId, description, hideInputPanel, showSwitch, sendMessgeTypes);
		try {

			openSessionService.updateSessionInfo(appId, description, hideInputPanel, showSwitch, sendMessgeTypes);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateSessionInfo error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateSessionInfo error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult updateAppUsable(String appId, boolean usable) {
		logger.info("updateAppUsable param--appid:{},usable:{} ", appId, usable);
		try {

			openSessionService.updateAppUsable(appId, usable);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateSessionInfo error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateSessionInfo error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 往客户端推送数据
	 * 
	 */
	@Override
	public MessageResult updateUserProperties(String appId, String enterpriseAccount, Integer userId, Integer key, String value) {

		try {
			Assert.notNull(appId, "appId is null");
			Assert.notNull(enterpriseAccount, "enterpriseAccount is null");
			Assert.notNull(userId, "userId is null");
			Assert.notNull(key, "key is null");
			Assert.notNull(value, "value is null");
			openSessionService.updateUserProperties(appId, enterpriseAccount, userId, key, value);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateUserProperties error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateUserProperties error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 批量查询/创建session
	 * 
	 */
	@Override
	public BatchSessionResult findOrCreateSessionBatch(CreateSessionBatchVO createSessionBatchVO) {
		logger.info("findOrCreateSessionBatch param--CreateSessionBatchVO:{}", createSessionBatchVO);
		try {
			long createtime = System.currentTimeMillis();
			BatchSessionResult result = new BatchSessionResult();
			OpenBatchSessionResult sessionResult = null;
			if (createSessionBatchVO != null && createSessionBatchVO.validateParams()) {
				// 异步存储
				this.addMsgSession(createSessionBatchVO, createSessionBatchVO.getAppId(), createSessionBatchVO.getEnterpriseAccount(), createtime, MsgOperConstants.FIND_SESSION_BATCH_ACTION, new Gson().toJson(sessionResult), MsgOperConstants.RESPONSE_SUCCESS);
				OpenCreateSessionBatchArg arg = new OpenCreateSessionBatchArg();
				arg.setAppid(createSessionBatchVO.getAppId());
				arg.setEnterpriseAccount(createSessionBatchVO.getEnterpriseAccount());

				List<OpenSessionArg> userList = new ArrayList<OpenSessionArg>();
				OpenSessionArg sessionArg = null;

				for (UserVO vo : createSessionBatchVO.getUserList()) {
					sessionArg = new OpenSessionArg();
					sessionArg.setEmployeeNumber(vo.getEmployeeNumber());
					userList.add(sessionArg);
				}
				arg.setUserList(userList);

				sessionResult = openSessionService.findOrCreateSessionBatch(arg);

				result = transferOpenBatchSessionResult(sessionResult);
			} else {
				logger.error("findOrCreateSessionBatch param is null");
				result.setResultCodeEnum(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}
			return result;
		} catch (Exception e) {
			logger.error("findOrCreateSessionBatch error", e);
			return new BatchSessionResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/*
	 * 批量删除session
	 */
	@Override
	public BatchSessionResult deleteSessionBatch(DeleteSessionBatchVO vo) {
		logger.info("deleteSessionBatch param--DeleteSessionBatchVO:{}", vo);
		try {
			BatchSessionResult result = null;
			OpenBatchSessionResult sessionResult = null;
			long createtime = System.currentTimeMillis();
			if (vo != null && vo.validateParam()) {
				// 异步存储
				this.addMsgSession(vo, vo.getAppId(), vo.getEnterpriseAccount(), createtime, MsgOperConstants.DELETE_SESSION_BATCH_ACTION, new Gson().toJson(sessionResult), MsgOperConstants.RESPONSE_SUCCESS);

				OpenDeleteSessionBatchArg arg = new OpenDeleteSessionBatchArg();
				arg.setAppid(vo.getAppId());
				arg.setDeleteAllMessage(vo.isDeleteAllMessage());
				arg.setEnterpriseAccount(vo.getEnterpriseAccount());
				arg.setHide(vo.isHide());

				List<OpenSessionArg> userList = new ArrayList<OpenSessionArg>();
				OpenSessionArg sessionArg = null;
				for (UserVO userVo : vo.getUserList()) {
					sessionArg = new OpenSessionArg();
					sessionArg.setEmployeeNumber(userVo.getEmployeeNumber());
					userList.add(sessionArg);
				}
				arg.setUserList(userList);
				sessionResult = openSessionService.deleteSessionBatch(arg);
				result = transferOpenBatchSessionResult(sessionResult);
			} else {
				result = new BatchSessionResult();
				result.setResultCodeEnum(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}

			return result;
		} catch (Exception e) {
			logger.error("deleteSessionBatch error", e);
			return new BatchSessionResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/*
	 * 批量推送用户数据更新数据
	 * 
	 */
	@Override
	public MessageResult updateUserPropertiesBatch(String appId, String enterpriseAccont, List<Integer> ownerIds, Integer key, String value) {
		logger.info("updateUserPropertiesBatch param: appId:{} , enterpriseAccount:{} , ownerIds :{} ,key :{} ,value:{}", appId, enterpriseAccont, ownerIds, key, value);
		try {
			Assert.notNull(appId, "appId is null");
			Assert.notNull(enterpriseAccont, "enterpriseAccont is null");
			Assert.notNull(ownerIds, "ownerIds is null");
			Assert.notNull(key, "key is null");
			Assert.notNull(value, "value is null");
			logger.info("updateUserPropertiesBatch update appId :" + appId);
			openSessionService.updateUserPropertiesBatch(appId, enterpriseAccont, ownerIds, key, value);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateUserProperties error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateUserProperties error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult updateUserPropertiesBatchAsync(String appId, String enterpriseAccont, List<Integer> ownerIds, Integer key, String value) {
		logger.info("updateUserPropertiesBatchAsync param: appId:{} , enterpriseAccount:{} , ownerIds :{} ,key :{} ,value:{}", appId, enterpriseAccont, ownerIds, key, value);
		try {
			Assert.notNull(appId, "appId is null");
			Assert.notNull(enterpriseAccont, "enterpriseAccont is null");
			Assert.notNull(ownerIds, "ownerIds is null");
			Assert.notNull(key, "key is null");
			Assert.notNull(value, "value is null");
			logger.info("updateUserPropertiesBatchAsync update appId :" + appId);

			ThreadPoolUtils.execute(new AsyncUpdateUserPropertiesBatch(appId, enterpriseAccont, ownerIds, key, value));

			/** 立即返回成功给调用者 */
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateUserPropertiesBatchAsync error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateUserPropertiesBatchAsync error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 异步操作，把session操作记录入库 -- 批量操作
	 * 
	 * @param request
	 * @param appId
	 * @param enterpriseAccount
	 * @param createTime
	 * @param action
	 * @param response
	 * @param responseStatus
	 */
	private void addMsgSession(Object request, String appId, String enterpriseAccount, long createTime, int action, String response, int responseStatus) {
		ThreadPoolUtils.execute(new Runnable() {
			@Override
			public void run() {
				MsgSessionDO msgSession = new MsgSessionDO();
				msgSession.setAction(action);
				msgSession.setAppId(appId);
				msgSession.setCreateTime(createTime);
				msgSession.setEnterpriseAccount(enterpriseAccount);
				msgSession.setRequest(new Gson().toJson(request));
				msgSession.setResponse(response);
				msgSession.setResponseStatus(responseStatus);
				msgSession.setUpdateTime(System.currentTimeMillis());
				msgSession.setUserId(0);
				msgSessionDao.addMsgSession(msgSession);
			}
		});
	}

	/**
	 * 异步操作，把session操作记录入库 -- 单独操作
	 * 
	 * @param appId
	 *            应用ID
	 * @param enterpriseAccount
	 *            企业号
	 * @param userId
	 *            纷享帐号
	 * @param createTime
	 *            创建时间
	 * @param sessionId
	 *            会话ID
	 * @param action
	 *            操作 MsgOperConstants常量定义
	 * @param response
	 *            接口响应内容
	 * @param responseStatus
	 *            调用接口状态
	 */
	private void addMsgSession(String appId, String enterpriseAccount, Integer userId, long createTime, String sessionId, int action, String response, int responseStatus) {
		ThreadPoolUtils.execute(new Runnable() {

			@Override
			public void run() {
				MsgSessionDO msgSession = new MsgSessionDO();
				msgSession.setAction(action);
				msgSession.setAppId(appId);
				msgSession.setCreateTime(createTime);
				msgSession.setEnterpriseAccount(enterpriseAccount);
				msgSession.setRequest("");
				msgSession.setResponse(response);
				msgSession.setResponseStatus(responseStatus);
				msgSession.setSessionId(sessionId);
				msgSession.setUpdateTime(System.currentTimeMillis());
				msgSession.setUserId(userId);
				msgSessionDao.addMsgSession(msgSession);
			}
		});
	}

	/**
	 * 转换批量删除session的结果
	 * 
	 * @param sessionResult
	 * @return
	 */
	private BatchSessionResult transferOpenBatchSessionResult(OpenBatchSessionResult sessionResult) {
		BatchSessionResult result = new BatchSessionResult();

		// OpenBatchSessionResult 转换成 BatchSessionResult
		if (sessionResult != null) {

			result.setAppId(sessionResult.getAppId());
			result.setEnterpriseAccount(sessionResult.getEnterpriseAccount());
			result.setResultCodeEnum(MsgCodeEnum.SUCCESS);
			List<SessionUserResult> sessionUserList = new ArrayList<SessionUserResult>();
			SessionUserResult sur = null;
			for (OpenSessionResult sr : sessionResult.getSessionList()) {
				sur = new SessionUserResult();
				sur.setEmployeeNumber(sr.getEmployeeNumber());
				sur.setSessionId(sr.getSessionId());
				sessionUserList.add(sur);
			}
			result.setSessionUserList(sessionUserList);
		} else {
			result.setResultCodeEnum(MsgCodeEnum.SYSTEM_ERROR);
		}
		return result;
	}

	/**
	 * 自建应用修改自定义菜单，并通知ea下的所有终端来拉取新菜单
	 */
	@Override
	public MessageResult updatePrivateAppCustomMenu(String ea, CustomMenuVO custumMenuVO) {
		try {
			// 判断字段appid是否为空
			if (StringUtils.isNotBlank(custumMenuVO.getAppId())) {
				List<OSS1SessionDefinition.OSS1CustomMenuItme> customMenu = custumMenuVO.getMenuVO(custumMenuVO.getMenuList());
				/** 此接口只能自建应用使用 */
				openSessionService.updateEnterpriseCustomAppMenu(ea, custumMenuVO.getAppId(), customMenu);
				redisTemplate.set(custumMenuVO.getAppId(), new Gson().toJson(custumMenuVO));
				return new MessageResult(MsgCodeEnum.SUCCESS);
			} else {
				return new MessageResult(MsgCodeEnum.PARAM_APPID_ILLEGAL);
			}
		} catch (Exception e) {

			logger.error("updateCustomMenuError:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 更新应用的自定义菜单。 如果一个应用有多个ea在用，ea没有私有拷贝数据，全部ea公用一份菜单数据。 服务端不会主动通知客户端来拉取新菜单
	 */
	@Override
	public MessageResult updateCustomMenu(CustomMenuVO custumMenuVO) {

		try {
			// 判断字段appid是否为空
			if (StringUtils.isNotBlank(custumMenuVO.getAppId())) {
				List<OSS1SessionDefinition.OSS1CustomMenuItme> customMenu = custumMenuVO.getMenuVO(custumMenuVO.getMenuList());
				openSessionService.updateCustomMenu(custumMenuVO.getAppId(), customMenu);
				redisTemplate.set(custumMenuVO.getAppId(), new Gson().toJson(custumMenuVO));
				return new MessageResult(MsgCodeEnum.SUCCESS);
			} else {
				return new MessageResult(MsgCodeEnum.PARAM_APPID_ILLEGAL);
			}
		} catch (Exception e) {

			logger.error("updateCustomMenuError:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 一个应用多个企业在用，每个ea有自己的自定义菜单拷贝。 服务端会通知ea下的客户端来拉取新菜单
	 */
	@Override
	public MessageResult updateEaCustomMenu(String ea, CustomMenuVO custumMenuVO) {
		try {

			// 判断字段appid是否为空
			if (StringUtils.isNotBlank(custumMenuVO.getAppId()) && StringUtils.isNotBlank(ea)) {
				List<OSS1SessionDefinition.OSS1CustomMenuItme> customMenu = custumMenuVO.getMenuVO(custumMenuVO.getMenuList());
				openSessionService.updateCustomMenu(custumMenuVO.getAppId(), ea, customMenu);
				return new MessageResult(MsgCodeEnum.SUCCESS);
			} else {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}
		} catch (Exception e) {

			logger.error("updateCustomMenuError:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public CustomMenuResult getCustomMenu(String appId) {
		// 判断appId
		if (StringUtils.isNotBlank(appId)) {
			String menuJson = redisTemplate.get(appId, null);
			CustomMenuVO customMenuVO = new Gson().fromJson(menuJson, CustomMenuVO.class);
			CustomMenuResult result = new CustomMenuResult(MsgCodeEnum.SUCCESS, customMenuVO);
			return result;
		} else {
			return new CustomMenuResult(MsgCodeEnum.PARAM_APPID_ILLEGAL);
		}

	}

	@Override
	public MessageResult updateUserPropertiesDeprecated(String enterpriseAccount, String fsEmployID, Integer key) {
		logger.info("updateUserPropertiesDeprecated param: enterpriseAccount:{} , fsEmployID :{} ,key :{}", enterpriseAccount, fsEmployID, key);
		try {
			Assert.notNull(enterpriseAccount, "enterpriseAccount is null");
			Assert.notNull(fsEmployID, "fsEmployID is null");
			Assert.notNull(key, "key is null");

			UpdateUserPropertiesArg arg = new UpdateUserPropertiesArg();
			arg.setKey(key);
			arg.setValue("{\"timeStamp\" : " + System.currentTimeMillis() + "}");
			arg.setOwnerId(Integer.parseInt(fsEmployID));

			AuthInfo authInfo = new AuthInfo();
			authInfo.setEmployeeId("E." + enterpriseAccount + "." + fsEmployID);
			authInfo.setAppId("");
			arg.setAuthInfo(authInfo);

			openSessionServiceDeprecated.updateUserProperties(arg);
			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (IllegalArgumentException e) {
			logger.error("updateUserPropertiesDeprecated error", e);
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		} catch (Exception e) {
			logger.error("updateUserPropertiesDeprecated error", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult updatePrivateAppServiceConfig(PrivateAppServiceConfig config) {
		try {
			if ((null == config.getAppId()) || (null == config.getEa())) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}
			openSessionService.updateCustomNameAndPortrait(config.getAppId(), config.getEa(), config.getName(), config.getPortrait());
		} catch (Exception e) {
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}

		return new MessageResult(MsgCodeEnum.SUCCESS);
	}

	@Override
	public MessageResult updateSessionStatus(UpdateSessionStatusVO updateSessionStatusVO) {
		if (StringUtils.isBlank(updateSessionStatusVO.getSessionId())) return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		try {
			OpenUpdateSessionStatusArg updateSessionArg = new OpenUpdateSessionStatusArg();
			updateSessionArg.setNotReadCount(updateSessionStatusVO.getNotReadCount());
			updateSessionArg.setNotReadFlag(updateSessionStatusVO.getNotReadFlag());
			updateSessionArg.setSessionId(updateSessionStatusVO.getSessionId());
			updateSessionArg.setEnterpriseAccount(updateSessionStatusVO.getEnterpriseAccount());
			updateSessionArg.setAppId(updateSessionStatusVO.getAppId());
			updateSessionArg.setUserId(updateSessionStatusVO.getUserId());
			updateSessionArg.setNotStrongNotification(updateSessionStatusVO.getNotStrongNotification());

			openSessionService.updateSessionStatus(updateSessionArg);

			return new MessageResult(MsgCodeEnum.SUCCESS);
		} catch (Exception e) {

			logger.error("updateSessionStatus:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 异步推送key=100的 通知用户更新列表消息 Created by fengyh on 2016/4/8.
	 */
	private class AsyncUpdateUserPropertiesBatch implements Runnable {

		private String appId;
		private String enterpriseAccont;
		private List<Integer> ownerIds;
		private Integer key;
		private String value;

		public AsyncUpdateUserPropertiesBatch(String appId, String enterpriseAccont, List<Integer> ownerIds, Integer key, String value) {
			this.appId = appId;
			this.enterpriseAccont = enterpriseAccont;
			this.ownerIds = ownerIds;
			this.key = key;
			this.value = value;
		}

		@Override
		public String toString() {
			return "AsyncUpdateUserPropertiesBatch{" + "openSessionService=" + openSessionService + ", appId='" + appId + '\'' + ", enterpriseAccont='" + enterpriseAccont + '\'' + ", ownerIds=" + ownerIds + ", key=" + key + ", value='" + value + '\'' + '}';
		}

		/**
		 * When an object implementing interface <code>Runnable</code> is used
		 * to create a thread, starting the thread causes the object's
		 * <code>run</code> method to be called in that separately executing
		 * thread.
		 * <p>
		 * The general contract of the method <code>run</code> is that it may
		 * take any action whatsoever.
		 *
		 * @see Thread#run()
		 */
		@Override
		public void run() {
			try {
				openSessionService.updateUserPropertiesBatch(appId, enterpriseAccont, ownerIds, key, value);
			} catch (Exception e) {
				logger.error("updateUserPropertiesBatchAsync update param={} , exception={}", this, e);
			}
		}
	}

	@Override
	public MsgBaseResult updateEaConnAppSessionConfig(EaConnAppSessionConfig config) {
		OpenOSS1DefinitionArg openOSS1DefinitionArg = new OpenOSS1DefinitionArg();
		openOSS1DefinitionArg.setAppId(config.getAppId());
		String upEa = config.getUpstreamEnterprise();
		openOSS1DefinitionArg.setUpstreamEnterprise(upEa);
		List<String> downEaList = config.getDownstreamEnterpriseList() != null ? config.getDownstreamEnterpriseList() : Lists.newArrayList();
		if (downEaList.isEmpty() && StringUtils.isNotEmpty(upEa)) {

			// 从互联平台rest接口获取
			logger.info("updateEaConnAppSessionConfig start. onCloud:{}, config:{}", true, config);
			int enterpriseId = getEnterpriseId(upEa);
			HeaderObj headerObj = HeaderObj.newInstance(config.getAppId(), enterpriseId, null, null);
			ListDownstreamEnterpriseSimpleInfosArg arg = new ListDownstreamEnterpriseSimpleInfosArg();
			arg.setUpstreamEa(upEa);
			ListAllDownstreamEasArg listAllDownstreamEasArg = new ListAllDownstreamEasArg();
			listAllDownstreamEasArg.setUpstreamEa(upEa);

			int offset = 0;
			listAllDownstreamEasArg.setOffset(offset);
			listAllDownstreamEasArg.setLimit(1000);

			while (offset < 50000) { //假定最多5万个下游。
				RestResult<Collection<String>> downResult = upstreamService.listAllDownstreamEas(headerObj, listAllDownstreamEasArg);
				logger.info("updateEaConnAppSessionConfig.collectionRestResult from cloud success. " +
						"onCloud:{}, restResult:{}", true, downResult);
				if (!downResult.isSuccess()) {
					logger.error("upstreamService.listAllDownstreamEas failed for :{}", headerObj);
					return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
				}

				if(CollectionUtils.isEmpty(downResult.getData())) {
					break;
				}

				downResult.getData().forEach(vo -> {
					if (StringUtils.isNotBlank(vo)) {
						downEaList.add(vo);
					}
				});

				if(downResult.getData().size() < 1000) {
					break;
				}
				offset+=1000;
				listAllDownstreamEasArg.setOffset(offset);
			}
		}
		downEaList.add(upEa);
		openOSS1DefinitionArg.setCustomMenus(config.getCustomMenu());
		openOSS1DefinitionArg.setName(config.getName());
		openOSS1DefinitionArg.setPortraitPath(config.getPortraitPath());
		openOSS1DefinitionArg.setDescription(config.getDescription());
		if (StringUtils.isNotEmpty(config.getDescription()) && Objects.isNull(config.getShowBlockMsgSwitch())) {
			openOSS1DefinitionArg.setShowSwitch(true);
		} else {
			openOSS1DefinitionArg.setShowSwitch(config.getShowBlockMsgSwitch());
		}
		openOSS1DefinitionArg.setDisable(config.isDisable());
		int batchProcessSize = 1000;
		while (!downEaList.isEmpty()) {
			if (downEaList.size() < batchProcessSize) {
				batchProcessSize = downEaList.size();
			}
			List<String> subDownEaList = downEaList.subList(0, batchProcessSize);
			openOSS1DefinitionArg.setDownstreamEnterpriseList(subDownEaList);
			UpdateOSS1DefinitionResult updateOSS1DefinitionResult = openCrossDefinitionService.updateOSS1Definition(openOSS1DefinitionArg);
			subDownEaList.clear();// 子列表清空会同步到原列表
			if (0 != updateOSS1DefinitionResult.getErrorCode()) {
				logger.error("updateEaConnAppSessionConfig error,openOSS1DefinitionArg:{},updateOSS1DefinitionResult:{}", openOSS1DefinitionArg, updateOSS1DefinitionResult);
				return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
			}
		}
		return new MessageResult(MsgCodeEnum.SUCCESS);
	}

	/**
	 * 企业互联session, 带上游企业的服务号session 清除红点，飘数等
	 */
	@Override
	public MsgBaseResult updateEaConnSessionStatus(EaConnAppSessionStatus arg) {
		UpdateOSS1SessionStatusArg updateOSS1SessionStatusArg = new UpdateOSS1SessionStatusArg();
		updateOSS1SessionStatusArg.setAppId(arg.getAppId());
		updateOSS1SessionStatusArg.setEnterpriseAccount(arg.getEnterpriseAccount());
		updateOSS1SessionStatusArg.setUpstreamEnterprise(arg.getUpstreamEnterprise());
		updateOSS1SessionStatusArg.setEmployeeId(arg.getEmployeeId());
		updateOSS1SessionStatusArg.setNotReadFlag(arg.getNotReadFlag());
		updateOSS1SessionStatusArg.setNotReadCount(arg.getNotReadCount());
		try {
			UpdateOSS1SessionStatusResult updateOSS1SessionStatusResult = openCrossSessionService.updateOSS1SessionStatus(updateOSS1SessionStatusArg);
			if (0 != updateOSS1SessionStatusResult.getErrorCode()) {
				return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
			}
		} catch (Exception e) {
			logger.error("openCrossSessionService.updateOSS1SessionStatus get exception for updateOSS1SessionStatusArg:{}", updateOSS1SessionStatusArg, e);
			return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
		}
		return new MsgBaseResult();
	}

	/**
	 * 配置 企业互联下的审批提醒session的title和头像，	 * 更新摘要和未审批数目。
	 * 更新摘要会引起未读数变化。
	 * <p>
	 * 不需要更新的字段传null.
	 *
	 * @param arg
	 */
	@Override
	public MsgBaseResult updateEaConnRemindSessionConfig(EaConnRemindSessionConfigVO arg) {
		final String bizName = "APPROVE";

		try {
			if (null != arg.getTitle() || null != arg.getPortrait()) {
				OpenUpdateCustomEntryDefinitionArg openUpdateCustomEntryDefinitionArg = new OpenUpdateCustomEntryDefinitionArg();
				openUpdateCustomEntryDefinitionArg.setBiz(bizName);
				openUpdateCustomEntryDefinitionArg.setName(arg.getTitle());
				openUpdateCustomEntryDefinitionArg.setPortraitPath(arg.getPortrait());
				openUpdateCustomEntryDefinitionArg.setForwardUrl(arg.getForwardURL());
				UpdateDefinitionResult updateDefinitionResult =
						openCrossDefinitionService.updateCustomEntryDefinition(openUpdateCustomEntryDefinitionArg);

				if (updateDefinitionResult.getErrorCode() != 0) {
					logger.error("updateCustomEntryDefinition fail, arg:{}, result:{}", arg, updateDefinitionResult);
					return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
				}
			}

			if (null != arg.getSummary()) {
				if(null == arg.getEa() || null == arg.getUserId()) {
					return new MsgBaseResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
				}
				OpenSendCrossCustomEntryMessageArg openSendCrossCustomEntryMessageArg = new OpenSendCrossCustomEntryMessageArg();
				openSendCrossCustomEntryMessageArg.setBiz(bizName);
				openSendCrossCustomEntryMessageArg.setEnterpriseAccount(arg.getEa());
				openSendCrossCustomEntryMessageArg.setEmployeeId(arg.getUserId());
				openSendCrossCustomEntryMessageArg.setLastSummary(arg.getSummary());
				openCrossMessageService.sendCustomEntryMessage(openSendCrossCustomEntryMessageArg);
			}

			if (null != arg.getUnprocessedApprovalNum()) {
				if(null == arg.getEa() || null == arg.getUserId()) {
					return new MsgBaseResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
				}
				UpdateCustomEntrySessionStatusArg updateCustomEntrySessionStatusArg = new UpdateCustomEntrySessionStatusArg();
				updateCustomEntrySessionStatusArg.setBiz(bizName);
				updateCustomEntrySessionStatusArg.setEnterpriseAccount(arg.getEa());
				updateCustomEntrySessionStatusArg.setEmployeeId(arg.getUserId());
				updateCustomEntrySessionStatusArg.setNotDealCount(arg.getUnprocessedApprovalNum());

				openCrossSessionService.updateCustomEntrySessionStatus(updateCustomEntrySessionStatusArg);
			}
		}catch (Exception e) {
			logger.error("updateEaConnRemindSessionConfig get exception, ", e);
			return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
		}

		return new MsgBaseResult();
	}
	
	@Override
	public MsgBaseResult updateEaConnRemindSessionInfo(UpdateEaConnRemindSessionInfo arg) {
		try {
			if (arg.getUpdateType().getType() == 1) {
				OpenUpdateCustomEntryDefinitionArg openUpdateCustomEntryDefinitionArg = new OpenUpdateCustomEntryDefinitionArg();
				openUpdateCustomEntryDefinitionArg.setBiz(arg.getAppId());
				openUpdateCustomEntryDefinitionArg.setName(arg.getUpdateSessionConfigVo().getTitle());
				openUpdateCustomEntryDefinitionArg.setPortraitPath(arg.getUpdateSessionConfigVo().getPortrait());
				openUpdateCustomEntryDefinitionArg.setForwardUrl(arg.getUpdateSessionConfigVo().getForwardURL());
				openUpdateCustomEntryDefinitionArg.setUpstreamEnterprise(arg.getUpStreamEa());
				openUpdateCustomEntryDefinitionArg.setDownstreamEnterpriseList(arg.getUpdateSessionConfigVo().getDownStreamEas());				
				UpdateDefinitionResult updateDefinitionResult =
						openCrossDefinitionService.updateCustomEntryDefinition(openUpdateCustomEntryDefinitionArg);

				if (updateDefinitionResult.getErrorCode() != 0) {
					logger.error("updateCustomEntryDefinition fail, arg:{}, result:{}", arg, updateDefinitionResult);
					return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
				}
			}
			
			if (arg.getUpdateType().getType() == 2) {
				OpenSendCrossCustomEntryMessageArg openSendCrossCustomEntryMessageArg = new OpenSendCrossCustomEntryMessageArg();
				openSendCrossCustomEntryMessageArg.setBiz(arg.getAppId());
				openSendCrossCustomEntryMessageArg.setEnterpriseAccount(arg.getSendSummaryMessageVo().getEa());
				openSendCrossCustomEntryMessageArg.setEmployeeId(arg.getSendSummaryMessageVo().getUserId());
				openSendCrossCustomEntryMessageArg.setLastSummary(arg.getSendSummaryMessageVo().getSummary());
				openSendCrossCustomEntryMessageArg.setUpstreamEnterprise(arg.getUpStreamEa());
				openCrossMessageService.sendCustomEntryMessage(openSendCrossCustomEntryMessageArg);
			}
			
			if (arg.getUpdateType().getType() == 3) {
				UpdateCustomEntrySessionStatusArg updateCustomEntrySessionStatusArg = new UpdateCustomEntrySessionStatusArg();
				updateCustomEntrySessionStatusArg.setBiz(arg.getAppId());
				updateCustomEntrySessionStatusArg.setEnterpriseAccount(arg.getUpdateUnReadNumVo().getEa());
				updateCustomEntrySessionStatusArg.setEmployeeId(arg.getUpdateUnReadNumVo().getUserId());
				updateCustomEntrySessionStatusArg.setNotDealCount(arg.getUpdateUnReadNumVo().getNotDealCount());
				updateCustomEntrySessionStatusArg.setNotReadCount(arg.getUpdateUnReadNumVo().getNotReadCount());
				updateCustomEntrySessionStatusArg.setNotReadFlag(arg.getUpdateUnReadNumVo().getNotReadFlag());
				
				openCrossSessionService.updateCustomEntrySessionStatus(updateCustomEntrySessionStatusArg);
			}

		} catch (Exception e) {
			logger.error("updateEaConnRemindSessionInfo get exception, ", e);
			return new MsgBaseResult(MsgCodeEnum.SYSTEM_ERROR);
		}
		
		return new MsgBaseResult();
	}

	/**
	 * ea转ei
	 *
	 * @param ea 企业ea
	 * @return
	 */
	private int getEnterpriseId(String ea) {
		try {
			return eIEAConverter.enterpriseAccountToId(ea);
		} catch (Exception e) {
			throw new IllegalArgumentException(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION.getErrorMsg());
		}
	}

}
