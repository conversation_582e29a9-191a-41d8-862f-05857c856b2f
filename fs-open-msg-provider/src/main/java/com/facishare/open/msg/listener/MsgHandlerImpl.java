package com.facishare.open.msg.listener;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.open.msg.util.*;

import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.service.EmployeeProviderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import org.apache.rocketmq.common.message.Message;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.autoreplymsg.constant.AutoReplyMsgTypeEnum;
import com.facishare.open.autoreplymsg.constant.AutoReplyTypeEnum;
import com.facishare.open.autoreplymsg.result.GetAutoReplyResult;
import com.facishare.open.autoreplymsg.result.GetCustomServiceSwitchResult;
import com.facishare.open.autoreplymsg.service.MsgAutoReplyService;
import com.facishare.open.callback.model.UserRequestVO;
import com.facishare.open.callback.result.CallBackBaseResult;
import com.facishare.open.callback.service.CallBackService;
import com.facishare.open.custom.menu.api.enums.PrefixEnum;
import com.facishare.open.custom.menu.api.result.BaseResult;
import com.facishare.open.custom.menu.api.service.CustomMenuService;
import com.facishare.open.material.api.enums.CreatorTypeEnum;
import com.facishare.open.material.api.enums.MaterialTypeEnum;
import com.facishare.open.material.api.model.vo.AccepterVO;
// import com.facishare.open.material.api.model.vo.DownstreamAccepterVO;
import com.facishare.open.material.api.model.vo.MessageVO;
import com.facishare.open.material.api.service.MaterialMessageService;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.constants.MsgOperConstants;
import com.facishare.open.msg.dao.CustomerEvaluateDAO;
import com.facishare.open.msg.dao.MsgTransmitRefDAO;
import com.facishare.open.msg.dao.OpenMsgDAO;
import com.facishare.open.msg.dao.WechatGroupIDMappingDAO;
import com.facishare.open.msg.manager.MessageRecordManager;
import com.facishare.open.msg.manager.UserIdManager;
import com.facishare.open.msg.model.OTMessageVO.ButtonWarp;
import com.facishare.open.msg.model.OTMessageVO.ContentWarp;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.service.SendMessageService;
import com.facishare.open.msg.util.DBDataRuleUtil.UCMessageHelper;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.oauth.model.enums.CustomAppDevStatus;
import com.facishare.open.oauth.result.AppResult;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.service.AppService;
import com.facishare.open.oauth.service.EaAuthService;
import com.facishare.qixin.api.constant.AuthSourceType;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.constant.PluginInstrument;
import com.facishare.qixin.api.constant.SessionType;
import com.facishare.qixin.api.model.message.result.SendMessageResult;
import com.facishare.qixin.api.model.open.arg.OpenCrossRevokeMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenCrossSendNormalCSMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenFindSessionInfoArg;
import com.facishare.qixin.api.model.open.arg.OpenRevokeMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenSendCSMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenSendCrossMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenSendNormalCSMessageArg;
import com.facishare.qixin.api.model.open.result.OpenCrossRevokeMessageResult;
import com.facishare.qixin.api.model.open.result.OpenRevokeMessageResult;
import com.facishare.qixin.api.model.open.result.OpenSendCSMessageResult;
import com.facishare.qixin.api.model.open.result.OpenSendMessageResult;
import com.facishare.qixin.api.model.open.result.OpenSendOSS1MessageResult;
import com.facishare.qixin.api.model.session.Session;
import com.facishare.qixin.api.open.OpenCrossCustomerService;
import com.facishare.qixin.api.open.OpenCrossMessageService;
import com.facishare.qixin.api.open.OpenCustomService;
import com.facishare.qixin.api.open.OpenMessageService;
import com.facishare.qixin.api.open.OpenSessionService;
import com.fxiaoke.cloud.DataPersistor;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;

/**
 * 处理企信后台通过MQ投递给开平消息系统的事件, 比如工作台的消息分发， 服务号中消息撤回等。
 *
 * @history 2017.3.14 hardy更新注释
 */

//IgnoreI18nFile
@Service("msgHandler")
public class MsgHandlerImpl {

	public static final Logger logger = LoggerFactory.getLogger(MsgHandlerImpl.class);
	private static final int SOURCE_INNER_U_C = 0;// 内部服务号 用户给客服发消息
	private static final int SOURCE_INNER_C_U = 1;// 内部服务号 客服回复
	private static final int SOURCE_WX_C_U = 2;// 微信外联服务号 客服回复
	private static final int SOURCE_CROSS_U_C = 3;// 互联服务号 用户给客服发消息
	private static final int SOURCE_CROSS_C_U = 4;// 互联服务号 客服回复

	@Autowired
	private MessageRecordManager messageRecordManager;

	@Autowired(required = false)
	private CustomMenuService customMenuService;

	@Autowired
	private MsgAutoReplyService msgAutoReplyService;

	@Autowired
	private SendMessageService sendMessageService;

	@Autowired
	private MaterialMessageService materialMessageService;

	@Autowired
	private OpenSessionService openSessionService;

	@Autowired
	private OpenCustomService openCustomService;

	@Autowired
	private OpenCrossCustomerService openCrossCustomerService;

	@Autowired
	private OpenMessageService openMessageService;

	@Autowired
	private OpenCrossMessageService openCrossMessageService;

	@Autowired
	private OpenAppAdminService openAppAdminService;

	@Autowired
	private EmployeeProviderService employeeProviderService;
	@Autowired
	private EIEAConverter eIEAConverter;

	@Autowired
	private EaAuthService eaAuthService;

	@Autowired
	private AppService appService;

	@Autowired
	private UserIdManager userIdManager;

	@Autowired
	private CallBackService callBackService;

	@Autowired
	private MsgTransmitRefDAO msgTransmitRefDAO;

	@Autowired
	private OpenMsgDAO openMsgDAO;

	@Autowired
	private CustomerEvaluateDAO customerEvaluateDAO;

	private static final String STAT_ACTION_INVOKE = "fs-customer-stat-invoke";

	/**
	 * @Resource private MessageService wxBaichuanMessageService;
	 */

	@Autowired
	private WechatGroupIDMappingDAO wechatGroupIDMappingDAO;

	private String grayEa;

	private String grayEaRevoke;

	@ReloadableProperty("evaluate.msg.url")
	private String linkBaseUrl;

	@ReloadableProperty("employee_profile_image_cdn_path")
	private String EMPLOYEE_PROFILE_IMAGE_CDN_PATH;

	@ReloadableProperty("VIDEO_FILE_TOO_LARGE")
	private String VIDEO_FILE_TOO_LARGE = "视频消息发送失败，请缩减视频时长或者文件大小后，重新发送";

	@PostConstruct
	private void init() {
		/**
		 * IConfigFactory factory = ConfigFactory.getInstance();
		 * IChangeableConfig config = factory.getConfig("gray-rel-message-send",
		 * iConfig -> { grayEa = iConfig.get("server5_4", ""); grayEaRevoke =
		 * iConfig.get("server5_4_1", "");
		 *
		 * logger.info("reload from cms, grayEa:{}", grayEa);
		 * logger.info("reload from cms, grayEaRevoke:{}", grayEaRevoke); });
		 *
		 * grayEa = config.get("server5_4", ""); grayEaRevoke =
		 * config.get("server5_4_1", "");
		 *
		 * logger.info("reload from cms, grayEa:{}", grayEa);
		 * logger.info("reload from cms, grayEaRevoke:{}", grayEaRevoke);
		 */
	}

	private String getWechatGroupID(String qixinGroupID) {
		return wechatGroupIDMappingDAO.getWechatGroupID(qixinGroupID);
	}

	private void SendBaichuanQixinMsgToWechatGroup(OpenMessageSendEvent notify, boolean twoPeopleChat) {
		/**
		 * String fromUser = notify.getFullSenderId(); String qixinGroupID =
		 * notify.getSessionId();
		 *
		 * Receiver receiver = new Receiver(); if(!twoPeopleChat) { //
		 * 如果微信群从三个人踢出一个，剩下两个。微信还是有群ID.消息还是丢到群ID.
		 * receiver.setType(ChatType.GROUP); String toWechatGroupID =
		 * getWechatGroupID(qixinGroupID); receiver.setId(toWechatGroupID); }
		 * else { receiver.setType(ChatType.SINGLE); //**从mongo中查 接收人的账号 String
		 * toUser = null;
		 *
		 * WechatGroupIDMappingDO wechatGroupIDMappingDO =
		 * wechatGroupIDMappingDAO.getRecord(qixinGroupID); String user1 =
		 * wechatGroupIDMappingDO.getUser1(); String user2 =
		 * wechatGroupIDMappingDO.getUser2();
		 *
		 * if(fromUser.equals(user1)) { toUser = user2; } else { toUser = user1;
		 * } receiver.setId(toUser); }
		 *
		 * if(MessageType.TEXT.equalsIgnoreCase(notify.getMessageType())) {
		 * logger.info("wxBaichuanMessageService.sendText msg notify:{},
		 * receiver:{}, fromUser :{} ", notify, receiver, fromUser);
		 * com.facishare.open.common.result.BaseResult<Void> baseResult =
		 * wxBaichuanMessageService.sendText(notify.getEnterpriseAccount(),
		 * receiver, fromUser, notify.getContent(),
		 * UUID.randomUUID().toString());
		 * logger.info("wxBaichuanMessageService.sendText msg notify:{},
		 * receiver:{}, fromUser :{} , baseResult:{} ", notify, receiver,
		 * fromUser, baseResult); } else { String fileFullPath = null; String
		 * fileName = "testfilename"; MsgTypeEnum msgTypeEnum =
		 * MsgTypeEnum.TEXT;
		 *
		 * if(MessageType.IMAGE.equalsIgnoreCase(notify.getMessageType())) {
		 * msgTypeEnum = MsgTypeEnum.IMAGE; Image qixinImgFormat =
		 * Image.fromJson(notify.getContent()); fileFullPath =
		 * qixinImgFormat.getImage(); } else
		 * if(MessageType.DOCUMENT.equalsIgnoreCase(notify.getMessageType())) {
		 * msgTypeEnum = MsgTypeEnum.TEXT; Document qixinDocFormat =
		 * Document.fromJson(notify.getContent()); fileFullPath =
		 * qixinDocFormat.getFile(); } else { logger.info("unsupported msg type
		 * :{} ", notify.getMessageType()); }
		 * wxBaichuanMessageService.sendMedia(notify.getEnterpriseAccount(),
		 * receiver, fromUser, fileFullPath, msgTypeEnum,
		 * UUID.randomUUID().toString(), fileName); }
		 */
	}

	public void handlerSendMessageEvent(OpenMessageSendEvent notify) {
		logger.debug("get msg from handlerSendMessageEvent(after parse):{}", notify);

		try {
			String fullSenderId = notify.getFullSenderId();
			String messageContent = notify.getContent();
			String appId = notify.getAppId();
			String messageType = notify.getMessageType();
			String sessionType = notify.getSessionType();
			String parentSessionType = notify.getParentSessionType();

			if (StringUtils.isEmpty(appId)) {
				logger.debug("handlerSendMessageEvent appId empty");
				return;
			}
			// 只消费source=qixin的消息
			if (!AuthSourceType.qixin.equals(notify.getSource())){
				return;
			}

			if (StringUtils.isBlank(appId) || !appId.startsWith("FSAID")) {
				return;
			}

			//增加app类型判断,如果是客户互联的app则直接返回,不处理上行的消息  & 5. 外发消息
			AppResult appResult = appService.getAppInfo(null, null, appId);
			logger.info("appResult appid:{}, isSuccess:{} ,getCustomAppDevStatus:{} ",appResult.getAppId(), appResult.isSuccess(), appResult.getCustomAppDevStatus());
			if (!appResult.isSuccess()) {
				logger.error("find appInfo error: errorCode:{} errMsg:{}", appResult.getErrCode(), appResult.getErrMessage());
				return;
			}

			int source = -1;
			if (appId.startsWith("FSAID") && "OSS1".equals(sessionType)) {
				source = SOURCE_INNER_U_C;
			}
			if (appId.startsWith("FSAID") && !Strings.isNullOrEmpty(notify.getParentSessionId()) && "CS".equals(parentSessionType)) {
				source = SOURCE_INNER_C_U;/** 内部服务号 客服回复 */
			}
			if (appId.startsWith("FSAID") && !Strings.isNullOrEmpty(notify.getParentSessionId()) && "CSW".equals(parentSessionType)) {
				source = SOURCE_WX_C_U;/** 微信外联服务号 客服回复 */
			}

			if (appId.startsWith("FSAID") && "OSS1".equals(sessionType) && !Strings.isNullOrEmpty(notify.getParentSessionId()) && "C".equals(parentSessionType)) {
				source = SOURCE_CROSS_U_C;/** 互联服务号 用户给客服发消息 */
			}

			if (appId.startsWith("FSAID") && "SL".equals(sessionType) && !Strings.isNullOrEmpty(notify.getParentSessionId()) && "CSC".equals(parentSessionType)) {
				String subCategory = notify.getSubCategory();
				// SL-TD为纷享客服,不处理
				if (!subCategory.startsWith("SL-TD")) {
					source = SOURCE_CROSS_C_U;/** 互联服务号 客服回复 */
				}
			}

			if (!Strings.isNullOrEmpty(notify.getParentSessionId()) && SessionType.Weixin.equals(parentSessionType) && AuthSourceType.qixin.equals(notify.getSource())) {
				logger.debug("handlerSendMessageEvent get wechat baichuan msg notify:{}  ", notify);

				/** 微信百川企信消息同步到微信 */
				if (SessionType.Single.equals(sessionType)) {
					/** 单聊 */
					SendBaichuanQixinMsgToWechatGroup(notify, true);
				} else if (SessionType.Discussion.equals(sessionType)) {
					/** 群聊 */
					SendBaichuanQixinMsgToWechatGroup(notify, false);
				}
				return;
			}

			logger.info("handlerSendMessageEvent trace notify:{}, source:{} ,appId:{} ", notify, source,appId);
			if (source == -1) {
				return;
			}
			/**
			 * 2. 消息存储到数据库
			 */
			String id = this.saveMsgCallBack(notify, source);

			/**
			 * 3. 多客服消息分发入口
			 */
			if (isCrossMsg(source)) {
				multiCrossCustomerService(notify, source,id);
			} else {
				multiCustomerService(notify, source);
			}

			/**
			 * 只有以用户身份发上来的消息，才需要 处理自动回复, 并且投递到 事件消息处理后台。
			 * 以客服身份发上来的消息，处理完客服消息分发，直接返回了。
			 */
			if (SOURCE_INNER_U_C != source && SOURCE_CROSS_U_C != source) {
				return;
			}

			// 4 自动回复消息
			if (MessageType.TEXT.equalsIgnoreCase(messageType)) {
				replyMessage(notify, messageContent);
			} else {
				replyMessage(notify, StringUtils.SPACE);
			}

			logger.info("appResult appid:{},getCustomAppDevStatus:{} ",appResult.getAppId(), appResult.getCustomAppDevStatus());

			if (appResult.getCustomAppDevStatus() == CustomAppDevStatus.ENABLED) {

				String fromOpenUserId = userIdManager.getOpenIdFromOauth(notify.getAppId(), fullSenderId);

				UserRequestVO userParam = new UserRequestVO();
				userParam.setFromUser(fromOpenUserId);
				userParam.setCreateTime(new Date().getTime());
				userParam.setToUser(notify.getAppId());
				userParam.setContent(messageContent);
				userParam.setMsgId(String.valueOf(notify.getMessageId()));
				userParam.setMsgType(MessageTypeEnum.TEXT.getType());

				logger.info("trace callBackService userParam {} ", userParam);
				// url 默认为空 ,由callback 去获取
				CallBackBaseResult result = callBackService.callBack("", userParam);
				logger.info("trace callBackService userParam {} result {}", userParam,result);

				if (result.isSuccess()) {
					messageRecordManager.updateOpenMsgStatus(id, MsgOperConstants.RESPONSE_SUCCESS);
				} else {
					messageRecordManager.updateOpenMsgStatus(id, MsgOperConstants.RESPONSE_FAIL);
				}
			}
		} catch (Exception e) {
			logger.error("handlerSendMessageEvent exception：{}", e);
		}
	}

	public void handlerSessionInstructionEvent(OpenSessionInstructionEvent event) {

		logger.debug("队列中获取普通消息(解析后):{}", event);

		try {
			StopWatch totalStopWatch = new StopWatch();
			totalStopWatch.start();

			// 2 消息存储到数据库
			String id = this.saveMsgCallBack(event);
			// 3 外发消息
			String message = event.getEventData();
			String eventKey = "";
			if (!StringUtils.isEmpty(message)) {
				Map<String, String> messageMap = GsonUtil.getGson().fromJson(message, new TypeToken<Map<String, String>>() {
				}.getType());
				eventKey = messageMap.get("eventKey");
			}
			boolean callResult = false;
			if (!StringUtils.isEmpty(eventKey) && eventKey.startsWith(PrefixEnum.PREFIX.getPrefix())) {
				List<String> str = Splitter.on(".").splitToList(event.getFullSenderId());
				logger.debug("customMenuService.responseMenuContent appId:{},ea:{},senderId:{},eventKey:{}", event.getAppId(), str.get(1), event.getSenderId(), eventKey);
				BaseResult<Void> result = customMenuService.responseMenuContent(event.getAppId(), str.get(1), event.getSenderId(), eventKey);
				callResult = result.isSuccess();
			} else {
				// 5. 外发消息
				AppResult appResult = appService.getAppInfo(null, null, event.getAppId());
				if (!appResult.isSuccess()) {
					logger.error("find appInfo error: errorCode:{} errMsg:{}", appResult.getErrCode(), appResult.getErrMessage());
				} else {
					if (appResult.getCustomAppDevStatus() == CustomAppDevStatus.ENABLED) {

						String fromOpenUserId = userIdManager.getOpenIdFromOauth(event.getAppId(), event.getFullSenderId());

						UserRequestVO userParam = new UserRequestVO();
						userParam.setFromUser(fromOpenUserId);
						userParam.setCreateTime(System.currentTimeMillis());
						userParam.setToUser(event.getAppId());
						userParam.setContent(event.getEventData());
						userParam.setMsgId("-1");
						userParam.setMsgType(MessageTypeEnum.EVENT.getType());
						userParam.setEvent(event.getEventType());

						// url 默认为空 ,由callback 去获取
						CallBackBaseResult result = callBackService.callBack("", userParam);

						callResult = result.isSuccess();
					}
				}
			}
			if (callResult) {
				messageRecordManager.updateOpenMsgStatus(id, MsgOperConstants.RESPONSE_SUCCESS);
			} else {
				messageRecordManager.updateOpenMsgStatus(id, MsgOperConstants.RESPONSE_FAIL);
			}

			totalStopWatch.stop();

			logger.debug("handlerSessionInstructionEvent enventData:{} timeCost:{}", event, totalStopWatch.getTotalTimeMillis());
		} catch (Exception e) {
			logger.error("handlerSessionInstructionEvent exception：{}", e);
		}

	}

	public void handlerMessageRevokeEvent(OpenMessageRevokeEvent event) {
		logger.info("队列中获取撤回消息(解析后) appid:{}, ea:{}", event.getAppId(), event.getEnterpriseAccount());

		try {
			String appId = event.getAppId();
			String enterpriseAccount = event.getEnterpriseAccount();
			long srcMsgId = event.getMessageId();

			int msgSource = -1;
			if (appId != null && appId.startsWith("FSAID") && "OSS1".equals(event.getSessionType())) {
				msgSource = 0;
			}
			if (appId != null && appId.startsWith("FSAID") && !Strings.isNullOrEmpty(event.getParentSessionId()) && "CS".equals(event.getParentSessionType())) {
				msgSource = 1;
			}

			if (appId != null && appId.startsWith("FSAID") && !Strings.isNullOrEmpty(event.getParentSessionId()) && "C".equals(event.getParentSessionType()) && "OSS1".equals(event.getSessionType())) {
				msgSource = 2;// 用户撤销
			}

			if (!Strings.isNullOrEmpty(event.getParentSessionId()) && "CSC".equals(event.getParentSessionType()) && "SL".equals(event.getSessionType())) {
				msgSource = 3;// 客服撤销
			}

			if (msgSource == -1) {
				return;
			}

			MsgTransmitRefDO msgTransmitRefDO = msgTransmitRefDAO.queryMsgTransmitRefBySrcMsg(enterpriseAccount, appId, srcMsgId, msgSource);
			logger.info("查询关联消息结果为:msgSource={} return={}", msgSource, msgTransmitRefDO);

			long rokeMsgId = srcMsgId;
			boolean isCross = false;
			if (msgTransmitRefDO != null && (msgSource == 2 || msgSource == 3)) {
				rokeMsgId = (msgSource == 2 ? msgTransmitRefDO.getTransmitMsgId() : msgTransmitRefDO.getSrcMsgId());
				isCross = (msgSource == 3);
			}
			openMsgDAO.deleteOpenMsgRevoke(enterpriseAccount, appId, rokeMsgId, isCross);
			logger.info("delete msg msgSource={}", msgSource);

			if (msgTransmitRefDO != null) {
				if (msgSource == 2 || msgSource == 3) {
					revokeCrossMessage(appId,msgTransmitRefDO);
				} else {
					revokeInnerMessage(appId, enterpriseAccount, msgTransmitRefDO);
				}

			}
		} catch (Exception e) {
			logger.error("handlerMessageRevokeEvent exception：{}", e);
		}
	}

	//插件事件
	public void handlerCustomerPluginSendEvent(OpenPluginSendEvent openPluginSendEvent) {
		// 判断应用状态
		String appId = openPluginSendEvent.getAppId();
		String enterpriseAccount = openPluginSendEvent.getEnterpriseAccount();

		AppResult appResult = appService.getAppInfo(null, null, appId);
		if (!appResult.isSuccess()) {
			logger.error("find appInfo error: errorCode:{} errMsg:{}", appResult.getErrCode(), appResult.getErrMessage());
			return;
		}
		if (!appCheck(enterpriseAccount, appId)) {
			logger.error("handlerPluginSendEvent appCheck false appId={} enterpriseAccount={}", appId, enterpriseAccount);
			return;
		}

		// 微信渠道和纷享渠道和webIm渠道的不发送
		boolean isWxApp = appResult.getAppType() == AppTypeEnum.OUT_SERVICE_APP.getValue();
		boolean isFsChannelApp = openPluginSendEvent.getSubCategory().startsWith("SL-TD-");
		boolean isWebImApp = SessionType.AnonymousUserCustomer.equals(openPluginSendEvent.getSessionType());
		if (isWxApp || isFsChannelApp || isWebImApp) return;

		OpenSendCSMessageArg arg = new OpenSendCSMessageArg();

		arg.setAppId(openPluginSendEvent.getAppId());
		arg.setEnterpriseAccount(openPluginSendEvent.getEnterpriseAccount());
		arg.setMessageType(MessageType.SYSTEM_PROMPT_TEXT);
		arg.setSource(AuthSourceType.system);
		arg.setSessionId(openPluginSendEvent.getSessionId());
		arg.setPostId(UidUtil.getUid());
		//不改变消息顺序，不置顶
		arg.setUpdateOrder(false);
		if (openPluginSendEvent.getInstrument().equals(PluginInstrument.OPEN_CUSTOMER_SESSION_OVER)) {
			arg.setMessageContent("服务已结束");
		}
		if (openPluginSendEvent.getInstrument().equals(PluginInstrument.OPEN_CUSTOMER_APPRAISE)) {
            List<EmployeeDto> employeeDtoList = this.getEmployeeDtoList(enterpriseAccount, Lists.newArrayList(openPluginSendEvent.getSenderId()));
            String customerName = employeeDtoList.get(0).getName();
			arg.setMessageContent("客服" + customerName + "发送服务评价邀请");
		}
		OpenSendCSMessageResult openSendCSMessageResult = openCustomService.sendCSMessage(arg);

		String userEa = "";
		int userId = 0;
		String srcSessionId = "";
		try {
			userEa = openSendCSMessageResult.getSession().getOriginalSessionVo().getOriginSessionParticipants().get(0).getEnterpriseAccount();
			if(Strings.isNullOrEmpty(userEa)) {
				userEa = openSendCSMessageResult.getSession().getOriginalSessionVo().getOriginSessionParticipants().get(1).getEnterpriseAccount();
			}
			userId = openSendCSMessageResult.getSession().getOriginalSessionVo().getOriginSessionParticipants().get(1).getParticipantId();
			if (userId == 0) {
				userId = openSendCSMessageResult.getSession().getOriginalSessionVo().getOriginSessionParticipants().get(0).getParticipantId();
			}
			srcSessionId = openSendCSMessageResult.getSession().getOriginalSessionVo().getOriginSessionId();

			if (appResult.getAppType() != AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE.getValue()) {
				userEa = enterpriseAccount;
			}
		} catch (Exception e) {
			logger.info("get srcUserId exception", e);
		}

		logger.info("platform Session to user Session userEa={}, userId={} srcSessionId={}", userEa, userId, srcSessionId);
		if (openPluginSendEvent.getInstrument().equals(PluginInstrument.OPEN_CUSTOMER_APPRAISE)) {
			CustomerEvaluateDO customerEvaluateDO = new CustomerEvaluateDO();
			customerEvaluateDO.setAppId(appId);
			customerEvaluateDO.setAppType(appResult.getAppType());
			customerEvaluateDO.setCreateTime(System.currentTimeMillis());
			customerEvaluateDO.setCustomerId(openPluginSendEvent.getSenderId());
			customerEvaluateDO.setCustomerSessionId(openPluginSendEvent.getSessionId());
			customerEvaluateDO.setEnterpriseAccount(enterpriseAccount);
			customerEvaluateDO.setEvaluateStatus(1);
			customerEvaluateDO.setEvaluatorId(userId);
			customerEvaluateDO.setOriginSessionId(srcSessionId);
			customerEvaluateDO.setReplyEa(userEa);
			customerEvaluateDO.setUpdateTime(System.currentTimeMillis());
			String msgLinkId = UidUtil.getUid();
			customerEvaluateDO.setMsgLinkId(msgLinkId);
			customerEvaluateDAO.createCustomerEvaluate(customerEvaluateDO);

			if (appResult.getAppType() != AppTypeEnum.OUT_SERVICE_APP.getValue()) {
				OTMessageVO otMessage = new OTMessageVO();

				ContentWarp title = otMessage.new ContentWarp();
				title.setContent("服务评价邀请");
				otMessage.setTitle(title);

				ContentWarp first = otMessage.new ContentWarp();
				first.setContent("如果您的问题已得到解决的话,麻烦您对客服的服务进行一个评价,我们需要您的鼓励哦!");
				otMessage.setFirst(first);

				ButtonWarp button = otMessage.new ButtonWarp();
				button.setTitle("查看详情");

				button.setUrl(linkBaseUrl + "/" + customerEvaluateDO.getAppId() + "/" + msgLinkId);
				otMessage.setButton(button);

				if (appResult.getAppType() == AppTypeEnum.SERVICE_APP.getValue() || appResult.getAppType() == AppTypeEnum.BASE_SERVICE_APP.getValue()) {
					openMessageService.sendMessage(
							customerEvaluateDO.getEnterpriseAccount(),
							customerEvaluateDO.getAppId(),
							customerEvaluateDO.getOriginSessionId(),
							MessageType.TEMPLATE, GsonUtil.getGson().toJson(otMessage),
							UidUtil.getUid(), AuthSourceType.system);
				}
				if (appResult.getAppType() == AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE.getValue()) {
					OpenSendCrossMessageArg  openSendCrossMessageArg = new OpenSendCrossMessageArg ();

					openSendCrossMessageArg.setAppId(customerEvaluateDO.getAppId());
					openSendCrossMessageArg.setMessageType(MessageType.TEMPLATE);
					openSendCrossMessageArg.setSource(AuthSourceType.system);
					openSendCrossMessageArg.setPostId(UidUtil.getUid());
					openSendCrossMessageArg.setMessageContent(GsonUtil.getGson().toJson(otMessage));
					openSendCrossMessageArg.setSessionId(customerEvaluateDO.getOriginSessionId());
					openSendCrossMessageArg.setUpstreamEnterprise(customerEvaluateDO.getEnterpriseAccount());
					OpenSendOSS1MessageResult openSendOSS1MessageResult = openCrossMessageService.sendOSS1Message(openSendCrossMessageArg);
				}
			}
		}

		StringBuffer sessionSb = new StringBuffer("");
		sessionSb.append(appId);
		//互联会话要加上下游公司Id
		if (appResult.getAppType() == AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE.getValue()) {
			sessionSb.append("-").append(userEa);
		}
		sessionSb.append("-").append(userId);
		openMsgDAO.updateUserMessageSessionStatus(userEa, sessionSb.toString(), MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
	}

	private void revokeInnerMessage(String appId, String enterpriseAccount, MsgTransmitRefDO msgTransmitRefDO) {
		OpenRevokeMessageArg arg = new OpenRevokeMessageArg();
		arg.setAppId(appId);
		arg.setEnterpriseAccount(enterpriseAccount);
		arg.setMessageId(msgTransmitRefDO.getTransmitMsgId());
		arg.setSessionId(msgTransmitRefDO.getTransmitSessionId());
		OpenRevokeMessageResult openRevokeMessageResult = openMessageService.revokeMessage(arg);
		logger.info("revoke msg param={} return={}", arg, openRevokeMessageResult);
	}

	private void revokeCrossMessage(String appId,MsgTransmitRefDO msgTransmitRefDO) {
		if (msgTransmitRefDO.getMsgSource() == 2) {
			OpenRevokeMessageArg arg = new OpenRevokeMessageArg();
			arg.setAppId(appId);
			arg.setEnterpriseAccount(msgTransmitRefDO.getTargetEa());
			arg.setMessageId(msgTransmitRefDO.getTransmitMsgId());
			arg.setSessionId(msgTransmitRefDO.getTransmitSessionId());
			OpenRevokeMessageResult openRevokeMessageResult = openMessageService.revokeMessage(arg);
			logger.info("revoke usermsg param={} return={}", arg, openRevokeMessageResult);
		}
		if (msgTransmitRefDO.getMsgSource() == 3) {
			OpenCrossRevokeMessageArg arg = new OpenCrossRevokeMessageArg();
			arg.setAppId(appId);
			arg.setEnterpriseAccount(msgTransmitRefDO.getTargetEa());
			arg.setMessageId(msgTransmitRefDO.getTransmitMsgId());
			arg.setSessionId(msgTransmitRefDO.getTransmitSessionId());
			OpenCrossRevokeMessageResult result = openCrossMessageService.revokeMessage(arg);
			logger.info("revoke customerMsg param={} return={}", arg, result);
		}

	}

	/**
	 * 保存回调文本消息
	 *
	 * @param notify
	 */
	private String saveMsgCallBack(OpenMessageSendEvent notify, int source) {

		// 异步存储
		OpenMsgDO openMsg = new OpenMsgDO();
		logger.debug("saveMsgCallBack notify:{} ", notify);
		long createTime = System.currentTimeMillis();
		boolean isAdministorReply = SOURCE_INNER_C_U == source || SOURCE_WX_C_U == source || SOURCE_CROSS_C_U == source;
		boolean isCross = isCrossMsg(source);

		UCMessageHelper ucMessagehelper = new DBDataRuleUtil.UCMessageHelper(openSessionService, notify, isAdministorReply, isCross);

		if(isCross){
			openMsg.setEnterpriseAccount(ucMessagehelper.getDownStreamEa());
			openMsg.setUpEnterpriseAccount(ucMessagehelper.getUpStreamEa());
		}else{
			openMsg.setEnterpriseAccount(notify.getEnterpriseAccount());
		}
		openMsg.setMsgId(notify.getMessageId());
		openMsg.setMsgType(notify.getMessageType());
		openMsg.setContent(notify.getContent());
		openMsg.setSender(ucMessagehelper.getSender());
		openMsg.setReceiver(ucMessagehelper.getReceiver());
		openMsg.setMessageTime(notify.getMessageTimestamp());
		openMsg.setCreateTime(createTime);
		openMsg.setUpdateTime(createTime);
		openMsg.setProcessStatus(ucMessagehelper.getProcessStatus());
		openMsg.setSessionId(ucMessagehelper.getDBSessionId());
		openMsg.setSendType(ucMessagehelper.getSendType());

		// 客服回复用户的消息，需要在应用中心 用户消息会话展示中，标识出 管理员的身份。
		if (isAdministorReply) {
			if (SOURCE_WX_C_U == source) {
				String receiver = GetWechatOpenID(notify.getSubCategory());
				openMsg.setSendType(MessageSendTypeEnum.ADMINISTRATOR_REPLY.getType());
				openMsg.setSessionId(DBDataRuleUtil.buildDBSessionId(notify.getAppId(),receiver, null));
				openMsg.setReceiver(receiver);
				openMsg.setProcessStatus(MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
			}

			// 消息类型为 app到人, 并设置管理员id
			openMsg.setAdminUserId(notify.getFullSenderId());
			openMsg.setStatus(1);
			// 更新这个session下面所有记录为 已读已回复， 不然红点不会消失。
			messageRecordManager.updateSingleUserSessionStatus(openMsg.getEnterpriseAccount(), openMsg.getSessionId(), openMsg.getMsgId(), MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
		}
		boolean result = messageRecordManager.syncSaveMsgSend(openMsg);
		logger.debug("saveMsgCallBack openmsg{}, result {}", openMsg, result);
		if (result) {
			return openMsg.getId().toString();
		} else {
			return new String("");
		}
	}

	/**
	 * 保存事件回调数据
	 *
	 * @param event
	 */
	private String saveMsgCallBack(OpenSessionInstructionEvent event) {
		OpenMsgDO openMsg = new OpenMsgDO();
		String upstremEa = event.getUpEnterpriseAccount();
		long createTime = System.currentTimeMillis();
		List<String> fsUserId = Splitter.on(".").splitToList(event.getFullSenderId());
		String ea = fsUserId.get(1);
		openMsg.setEnterpriseAccount(ea);
		openMsg.setMsgId(-1L);
		openMsg.setMsgType(MessageType.EVENT);
		openMsg.setContent(event.getEventData());
		openMsg.setSender(String.valueOf(event.getSenderId()));
		openMsg.setReceiver(event.getAppId());
		openMsg.setCreateTime(createTime);
		openMsg.setUpdateTime(createTime);
		openMsg.setMessageTime(event.getCreateTime());
		openMsg.setUpEnterpriseAccount(upstremEa);
		openMsg.setSessionId(DBDataRuleUtil.buildDBSessionId(event.getAppId(), event.getSenderId(), isCrossMsg(upstremEa) ? ea : null));
		openMsg.setSendType(MessageSendTypeEnum.UPLINK_MESSAGE_EVENT.getType());
		openMsg.setProcessStatus(MsgOperConstants.PROCESS_STATUS_READ_REPLIED);

		boolean result = messageRecordManager.syncSaveMsgSend(openMsg);
		logger.debug("saveMsgCallBack result {}", result);

		if (result) {
			return openMsg.getId().toString();
		} else {
			return "-1";
		}
	}

	/**
	 * 移动客服 分发总入口。 第三方回复 怎么处理？要不要分发给所有客服？！！！
	 */
	private void multiCustomerService(OpenMessageSendEvent notify, int source) {
		logger.debug("multiCustomerService param :{}", notify);

		/**
		 * 如果移动多客服开关在是关闭的，则客服和客户不能对话。
		 */
		try {
			String appID = notify.getAppId();
			List<String> fsUser = Splitter.on(".").splitToList(notify.getFullSenderId());
			String enterpriseAccount = fsUser.get(1);

			// 判断应用状态
			CommonResult appStatusResult = eaAuthService.isEaAuthStatusNormal(null, null, enterpriseAccount, appID);

			if (!appStatusResult.isSuccess()) {
				return;
			}

			GetCustomServiceSwitchResult result = msgAutoReplyService.queryCustomServiceReplySwitch(enterpriseAccount, appID);
			if ((false == result.isSuccess()) || (0 == result.getReplySwitch())) {
				return;
			}

			// 增加埋点日志
			Map<String, Object> statParamMap = new HashMap<String, Object>();
			statParamMap.put("appID", appID);
			statParamMap.put("enterpriseAccount", enterpriseAccount);
			statParamMap.put("source", source);

			logger.debug("before invoke DataPersistor.asyncLog, param = {}, source: {}", statParamMap, source);
			try {
				DataPersistor.asyncLog(STAT_ACTION_INVOKE, statParamMap);
			}catch (Exception e){}
			logger.info("after invoke DataPersistor.asyncLog, param = {}, source: {}", statParamMap, source);

			if (SOURCE_INNER_U_C == source) {// 内部服务号 用户发给客服的消息
				msgToAllServiceRepresentatives(notify, source);
				//增加客服消息统计埋点
				FengYunUtil.reportCustomerMsg(notify.getAppId(), notify.getEnterpriseAccount(),
						notify.getEnterpriseAccount(), "" + notify.getSenderId(), MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL);
			} else if ((SOURCE_INNER_C_U == source) || (SOURCE_WX_C_U == source)) {// 内部服务号
				CustomerServiceMsgToUser(notify);
			}
		} catch (Exception e) {
			logger.error("multiCustomerService get exception :{}", notify);
		}
	}

	/**
	 * 移动客服 消息在所有客服人员中分发(目前是一条消息所有人都发)。
	 *
	 * 当前机器自动回复，只会回复给用户，不会同步到所有客服！！
	 */
	private void msgToAllServiceRepresentatives(OpenMessageSendEvent notify, int source) {

		String appId = notify.getAppId();
		List<String> fsUser = Splitter.on(".").splitToList(notify.getFullSenderId());
		String enterpriseAccount = fsUser.get(1);
		logger.info("multiCustomerService appid:{}, fullsenderid:{}", appId, notify.getFullSenderId());

		String msgReplySource = "user";

		try {
			String origSessionID = notify.getSessionId();
			long origMsgID = notify.getMessageId();
			OpenSendMessageResult sendMessageResult = null;
			if (SOURCE_INNER_U_C == source) {
				OpenSendNormalCSMessageArg arg = new OpenSendNormalCSMessageArg();
				arg.setAppId(appId);
				arg.setEnterpriseAccount(enterpriseAccount);
				arg.setOriginMessageId(origMsgID);
				arg.setOriginSessionId(origSessionID);
				arg.setSource(msgReplySource);

				sendMessageResult = openCustomService.sendNormalCSMessage(arg);
			}
			if (!Objects.isNull(sendMessageResult)) {
				logger.info("multiCustomerService sendCSMessage params:source:{}, enterpriseAccount:{}, appid:{}, sessionid:{}, msgid:{},msgReplySource:{}, sendMessageResult:{} ", source, enterpriseAccount, appId, origSessionID, origMsgID, msgReplySource, sendMessageResult);
				// 记录分发关系以便后续撤回
				MsgTransmitRefDO msgTransmitRefDO = new MsgTransmitRefDO();
				msgTransmitRefDO.setAppId(appId);
				msgTransmitRefDO.setEnterpriseAccount(enterpriseAccount);
				msgTransmitRefDO.setMsgSource(0);
				msgTransmitRefDO.setSrcMsgId(origMsgID);
				msgTransmitRefDO.setSrcSessionId(origSessionID);
				msgTransmitRefDO.setTransmitMsgId(sendMessageResult.getMessageItem().getMessageId());
				msgTransmitRefDO.setTransmitSessionId(sendMessageResult.getSessionId());
				msgTransmitRefDO.setCreateTime(System.currentTimeMillis());
				msgTransmitRefDO.setUpdateTime(System.currentTimeMillis());

				msgTransmitRefDAO.addMsgTransmitRef(msgTransmitRefDO);
			}
		} catch (Exception e) {
			logger.error("multiCustomerService for user msg appid:{}, fullsenderid:{}, exception:{} ", appId, notify.getFullSenderId(), e);
		}
		// 设置未读列表.
		messageRecordManager.setUnReadUserSessions(enterpriseAccount, notify.getAppId());
	}

	/**
	 * 互联服务号移动客服 分发总入口。 第三方回复 怎么处理？要不要分发给所有客服？！！！
	 */
	private void multiCrossCustomerService(OpenMessageSendEvent notify, int source,String openMsgId) {
		logger.debug("multiCrossCustomerService param :{}", notify);

		/**
		 * 如果移动多客服开关在是关闭的，则客服和客户不能对话。
		 */
		try {
			String upstreamEa = notify.getUpEnterpriseAccount();
			if (StringUtils.isEmpty(upstreamEa)) {
				upstreamEa = notify.getEnterpriseAccount();// 客服回服的，发送企业就是上游
			}
			if (!appCheck(upstreamEa, notify.getAppId())) {
				logger.error("multiCrossCustomerService appCheck false");
				return;
			}

			if (SOURCE_CROSS_U_C == source) {// 互联服务号 用户发给客服的消息
				msgToAllCrossServiceRepresentatives(notify, source,openMsgId);
				FengYunUtil.reportEaConnMsg(notify.getAppId(), notify.getUpEnterpriseAccount(), notify.getEnterpriseAccount(), MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL);
				//增加客服消息统计埋点
				FengYunUtil.reportCustomerMsg(notify.getAppId(), notify.getUpEnterpriseAccount(), notify.getEnterpriseAccount(), "" + notify.getSenderId(), MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL);
			} else if (SOURCE_CROSS_C_U == source) {// 互联服务号 客服发给用户的消息
				CustomerServiceMsgToCrossUser(notify);
			}

		} catch (Exception e) {
			logger.error("multiCrossCustomerService get exception :{}", notify);
		}
	}

	/**
	 * 用户消息分发到所有客服
	 */
	private void msgToAllCrossServiceRepresentatives(OpenMessageSendEvent notify, int source,String openMsgId) {
		String appId = notify.getAppId();
		String upstreamEa = notify.getUpEnterpriseAccount();
		logger.info("msgToAllCrossServiceRepresentatives appid:{}, fullsenderid:{},openMsgId:{}", appId, notify.getFullSenderId(),openMsgId);
		String msgReplySource = "user";
		try {
			String origSessionID = notify.getSessionId();
			long origMsgID = notify.getMessageId();
			OpenCrossSendNormalCSMessageArg arg = new OpenCrossSendNormalCSMessageArg();
			arg.setAppId(appId);
			arg.setEnterpriseAccount(upstreamEa);// 上游企业帐号
			arg.setOriginMessageId(origMsgID);
			arg.setOriginSessionId(origSessionID);
			arg.setSource(msgReplySource);
			OpenSendMessageResult sendMessageResult = openCrossCustomerService.sendCrossNormalCSMessage(arg);
			logger.debug("msgToAllCrossServiceRepresentatives sendCrossNormalCSMessage arg:{},result:{}",arg, sendMessageResult);

			if (!Objects.isNull(sendMessageResult)) {
				logger.debug("msgToAllCrossServiceRepresentatives sendCSMessage params:source:{}, upstreamEa:{}, appid:{}, sessionid:{}, msgid:{},msgReplySource:{}, sendMessageResult:{} ", source, upstreamEa, appId, origSessionID, origMsgID, msgReplySource, sendMessageResult);
				if(StringUtils.isNotEmpty(openMsgId)){
					OpenMsgDO openMsgDO = new OpenMsgDO();
					openMsgDO.setId(new ObjectId(openMsgId));
					openMsgDO.setMsgId(sendMessageResult.getMessageItem().getMessageId());//统一用工作台这边的消息ID
					openMsgDO.setMessageTime(System.currentTimeMillis());
					openMsgDO.setResponse(sendMessageResult.toString());
					openMsgDO.setContent(sendMessageResult.getMessageItem().getContent());//要取最新的内容，否则有权限问题
					openMsgDAO.updateOpenMsg(openMsgDO);
					logger.debug("msgToAllCrossServiceRepresentatives updateOpenMsg openMsgDO:{}",openMsgDO);
				}

				// 记录分发关系以便后续撤回
				MsgTransmitRefDO msgTransmitRefDO = new MsgTransmitRefDO();
				msgTransmitRefDO.setAppId(appId);
				msgTransmitRefDO.setEnterpriseAccount(notify.getEnterpriseAccount());
				msgTransmitRefDO.setTargetEa(notify.getUpEnterpriseAccount());
				msgTransmitRefDO.setMsgSource(2);
				msgTransmitRefDO.setSrcMsgId(origMsgID);
				msgTransmitRefDO.setSrcSessionId(origSessionID);
				msgTransmitRefDO.setTransmitMsgId(sendMessageResult.getMessageItem().getMessageId());
				msgTransmitRefDO.setTransmitSessionId(sendMessageResult.getSessionId());
				msgTransmitRefDO.setCreateTime(System.currentTimeMillis());
				msgTransmitRefDO.setUpdateTime(System.currentTimeMillis());

				msgTransmitRefDAO.addMsgTransmitRef(msgTransmitRefDO);
			}
		} catch (Exception e) {
			logger.error("msgToAllCrossServiceRepresentatives for user msg appid:{}, fullsenderid:{}, exception:{} ", appId, notify.getFullSenderId(), e);
		}
		// 设置未读列表.
		messageRecordManager.setUnReadUserSessions(upstreamEa, notify.getAppId());
	}

	private String GetWechatOpenID(String subCategory) {
		/** in format OU-appid-senderOpenId **/
		int openIdIndex = subCategory.indexOf("-", 3);
		String openId = subCategory.substring(openIdIndex + 1);
		logger.info("trace  subCategory: {}, openid:{} ", subCategory, openId);
		return openId;
	}

	private boolean appCheck(String ea, String appId) {
		// 判断应用状态
		CommonResult appStatusResult = eaAuthService.isEaAuthStatusNormal(null, null, ea, appId);

		if (!appStatusResult.isSuccess()) {
			logger.error("appCheck isEaAuthStatusNormal error appStatusResult:{},appId:{},upEa:{}", appStatusResult, appId, ea);
			return false;
		}
		// 应用开关
		GetCustomServiceSwitchResult result = msgAutoReplyService.queryCustomServiceReplySwitch(ea, appId);
		if ((false == result.isSuccess()) || (0 == result.getReplySwitch())) {
			logger.error("appCheck queryCustomServiceReplySwitch errorOrClose result:{},appId:{},upEa:{}", result, appId, ea);
			return false;
		}
		return true;
	}

	/**
	 * 移动客服 消息分发给用户。 1. 人工客服回复的内容 是否出现在会话列表展示中？机器回复呢？
	 */
	private void CustomerServiceMsgToUser(OpenMessageSendEvent notify) {

		String appId = notify.getAppId();
		List<String> fsUser = Splitter.on(".").splitToList(notify.getFullSenderId());
		String enterpriseAccount = fsUser.get(1);

		logger.info("multiCustomerService appid:{}, fullsenderid:{} ", appId, notify.getFullSenderId());

		/** 判断是不是微客服消息 */
		try {
			String parentSessionType = notify.getParentSessionType();
			String parentSessionId = notify.getParentSessionId();
			String sessionType = notify.getSessionType();

			logger.info("multiCustomerService check wechat msg: appid:{}, parentSessionType:{}, parentSessionId:{}, sessionType:{} ", appId, parentSessionType, parentSessionId, sessionType);

			if (!Strings.isNullOrEmpty(parentSessionId) && parentSessionType.equals("CSW") && sessionType.equals("OU")) {
                //微客服消息下行已迁移至微客服,仅保留埋点
			    String subCategory = notify.getSubCategory();
			    /** in format OU-appid-senderOpenId**/
			    int openIdIndex = subCategory.indexOf("-", 3);
			    String openId = subCategory.substring(openIdIndex + 1);
				//增加客服消息统计埋点 微联服务号和内部服务号同一种类型，后续需要区分
				FengYunUtil.reportCustomerMsg(notify.getAppId(), notify.getEnterpriseAccount(), notify.getEnterpriseAccount(), openId, notify.getFullSenderId(), MessageSendTypeEnum.WETCHAT_ADMINISTRATOR_REPLY);
				return;
			}
		} catch (Exception e) {
			logger.error("multiCustomerService get exception for wechat reply: appid:{}, fullsenderid:{}, exception:{} ", appId, notify.getFullSenderId(), e);
			return;
		}

		/**
		 * 这是多客服消息。
		 *
		 * 区分人工回复还是机器回复
		 */
		try {
			String msgReplySource = "human";// "robot";
			/** 客服消息中带上来的是客服二级会话sessionid, 发送消息给用户要获取到用户一级会话sessionid才能发送. **/
			//SecondLevelSession secondLevelSession = openSessionService.findSecondLevelSession(enterpriseAccount, appId, notify.getSessionId());

			OpenFindSessionInfoArg findSessionInfoArg = new OpenFindSessionInfoArg();
			findSessionInfoArg.setSessionId(notify.getSessionId());
			findSessionInfoArg.setUserId(0);
			findSessionInfoArg.setEnterpriseAccount(enterpriseAccount);
			findSessionInfoArg.setAppId(appId);
			Session session = openSessionService.findSessionInfo(findSessionInfoArg);

			logger.debug("findSecondLevelSession secondLevelSession:{}", session);

			logger.info("multiCustomerService sendMessage params:enterpriseAccount：{}, appid:{}, usersessionid:{}, msgtype:{}, postid:{}, msgreplysource:{}", enterpriseAccount, appId, session.getOriginalSessionVo().getOriginSessionId(), notify.getMessageType(), UidUtil.getUid(), msgReplySource);

			SendMessageResult SendMessageResult = openMessageService.sendMessage(enterpriseAccount, appId, session.getOriginalSessionVo().getOriginSessionId(), notify.getMessageType(), notify.getContent(), UidUtil.getUid(), msgReplySource);

			logger.debug("multiCustomerService sendMessage Result:{}", SendMessageResult);

			//增加客服消息统计埋点
			int userId = session.getOriginalSessionVo().getOriginSessionParticipants().get(1).getParticipantId();
			if (userId == 0) {
				userId = session.getOriginalSessionVo().getOriginSessionParticipants().get(0).getParticipantId();
			}
			FengYunUtil.reportCustomerMsg(notify.getAppId(), notify.getEnterpriseAccount(), notify.getEnterpriseAccount(), "" + userId, MessageSendTypeEnum.ADMINISTRATOR_REPLY);

			// 增加回复通知功能
			ServiceReplyMsgItem serviceReplyMsgItem = new ServiceReplyMsgItem();
			serviceReplyMsgItem.setAppId(appId);
			serviceReplyMsgItem.setEnterpriseAccount(enterpriseAccount);
			serviceReplyMsgItem.setUserId(notify.getSenderId());

			Message message = new Message();
			message.setFlag(200);
			message.setTags("customerReplyTags");
			message.setBody(serviceReplyMsgItem.toProto());

			/**
			 * 人工回复的事件，通过MQ通知到应用中心。 应用中心可以做相应的处理。
			 */
			//serviceReplyRocketMQSender.send(message);

			// 记录分发id关系
			MsgTransmitRefDO msgTransmitRefDO = new MsgTransmitRefDO();
			msgTransmitRefDO.setAppId(appId);
			msgTransmitRefDO.setEnterpriseAccount(enterpriseAccount);
			msgTransmitRefDO.setMsgSource(1);
			msgTransmitRefDO.setSrcMsgId(notify.getMessageId());
			msgTransmitRefDO.setSrcSessionId(notify.getSessionId());
			msgTransmitRefDO.setTransmitMsgId(SendMessageResult.getMessageItem().getMessageId());
			msgTransmitRefDO.setTransmitSessionId(session.getOriginalSessionVo().getOriginSessionId());
			msgTransmitRefDO.setCreateTime(System.currentTimeMillis());
			msgTransmitRefDO.setUpdateTime(System.currentTimeMillis());

			msgTransmitRefDAO.addMsgTransmitRef(msgTransmitRefDO);

		} catch (Exception e) {
			logger.error("multiCustomerService for service msg, appid:{}, fullsenderid:{}, exception:{} ", appId, notify.getFullSenderId(), e);
		}
	}

	private void sendVideoTooLarge2QiXin(OpenMessageSendEvent event) {
		String fsAppId = event.getAppId();
		SendWechatPromptTextMessageArg arg = new SendWechatPromptTextMessageArg();
		arg.setEa(event.getEnterpriseAccount());
		arg.setAppId(fsAppId);
		arg.setWxOpenId(getWxOpenID(event.getSubCategory()));
		arg.setReceivers(Collections.singletonList(event.getSenderId()));
		arg.setPromptContent(VIDEO_FILE_TOO_LARGE);
		arg.setMessageType(MessageType.SYSTEM_PROMPT_TEXT);
		// 改变二级Session的排序，也就是会导致消息置顶
		arg.setUpdateOrder(true);
		MessageResult messageResult = sendMessageService.sendWechatPromptTextMessage(arg);
		logger.info("Send wx consult system Text message finish, arg={}, result={}", arg, messageResult);
	}

	/**
	 * 从subCategory出wxOpenId
	 */
	private String getWxOpenID(String subCategory) {
		// in format OU-appId-senderOpenId
		int openIdIndex = subCategory.indexOf("-", 3);
		return subCategory.substring(openIdIndex + 1);
	}

	/**
	 * 人工客服回复
	 */
	private void CustomerServiceMsgToCrossUser(OpenMessageSendEvent notify) {
		String appId = notify.getAppId();
		String enterpriseAccount = notify.getEnterpriseAccount();// 上游
		logger.debug("CustomerServiceMsgToCrossUser appid:{}, fullsenderid:{}, notify:{} ", appId, notify.getFullSenderId(), notify);

		try {

			OpenFindSessionInfoArg findSessionInfoArg = new OpenFindSessionInfoArg();
			findSessionInfoArg.setSessionId(notify.getSessionId());
			findSessionInfoArg.setEnterpriseAccount(enterpriseAccount);
			findSessionInfoArg.setAppId(appId);
			findSessionInfoArg.setUserId(0);
			Session session = openSessionService.findSessionInfo(findSessionInfoArg);

			OpenSendCrossMessageArg sendOSS1MessageArg = new OpenSendCrossMessageArg();
			sendOSS1MessageArg.setAppId(appId);
			sendOSS1MessageArg.setMessageContent(notify.getContent());
			sendOSS1MessageArg.setMessageType(notify.getMessageType());
			sendOSS1MessageArg.setPostId(UidUtil.getUid());
			sendOSS1MessageArg.setSessionId(session.getOriginalSessionVo().getOriginSessionId());
			sendOSS1MessageArg.setSource(AuthSourceType.human);
			sendOSS1MessageArg.setUpstreamEnterprise(enterpriseAccount);



			OpenSendOSS1MessageResult openSendOSS1MessageResult = openCrossMessageService.sendOSS1Message(sendOSS1MessageArg);
			logger.debug("CustomerServiceMsgToCrossUser sendMessage Result:{}", openSendOSS1MessageResult);

			/**蜂云埋点上报*/
			String downEa = session.getOriginalSessionVo().getOriginSessionParticipants().get(0).getEnterpriseAccount();
			FengYunUtil.reportEaConnMsg(notify.getAppId(), notify.getEnterpriseAccount(), downEa, MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY);

			//增加客服消息统计埋点
			int userId = session.getOriginalSessionVo().getOriginSessionParticipants().get(1).getParticipantId();
			if (userId == 0) {
				userId = session.getOriginalSessionVo().getOriginSessionParticipants().get(0).getParticipantId();
			}
			FengYunUtil.reportCustomerMsg(notify.getAppId(), notify.getEnterpriseAccount(), downEa, "" + userId, MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY);

			// 增加回复通知功能
			ServiceReplyMsgItem serviceReplyMsgItem = new ServiceReplyMsgItem();
			serviceReplyMsgItem.setAppId(appId);
			serviceReplyMsgItem.setEnterpriseAccount(enterpriseAccount);
			serviceReplyMsgItem.setUserId(notify.getSenderId());

			Message message = new Message();
			message.setFlag(200);
			message.setTags("customerReplyTags");
			message.setBody(serviceReplyMsgItem.toProto());

			/**
			 * 人工回复的事件，通过MQ通知到应用中心。 应用中心可以做相应的处理。
			 */
			//serviceReplyRocketMQSender.send(message);
			// 记录分发id关系
			MsgTransmitRefDO msgTransmitRefDO = new MsgTransmitRefDO();
			msgTransmitRefDO.setAppId(appId);
			msgTransmitRefDO.setEnterpriseAccount(enterpriseAccount);
			msgTransmitRefDO.setTargetEa(DBDataRuleUtil.getEaFromSession(session));
			msgTransmitRefDO.setMsgSource(3);
			msgTransmitRefDO.setSrcMsgId(notify.getMessageId());
			msgTransmitRefDO.setSrcSessionId(notify.getSessionId());
			msgTransmitRefDO.setTransmitMsgId(openSendOSS1MessageResult.getMessageItem().getMessageId());
			msgTransmitRefDO.setTransmitSessionId(session.getOriginalSessionVo().getOriginSessionId());
			msgTransmitRefDO.setCreateTime(System.currentTimeMillis());
			msgTransmitRefDO.setUpdateTime(System.currentTimeMillis());

			msgTransmitRefDAO.addMsgTransmitRef(msgTransmitRefDO);
		} catch (Exception e) {
			logger.error("CustomerServiceMsgToCrossUser for service msg, appid:{}, fullsenderid:{}, exception:{} ", appId, notify.getFullSenderId(), e);
		}
	}

	/**
	 * 自动回复
	 *
	 * @param
	 * @param
	 * @param messageContent
	 * @return
	 */

	private void replyMessage(OpenMessageSendEvent notify, String messageContent) {
		String fullSenderId = notify.getFullSenderId();
		String appId = notify.getAppId();
		ThreadPoolUtils.execute(new Runnable() {
			@Override
			public void run() {
				String upEnterpriseAccount = notify.getUpEnterpriseAccount();
				boolean isCross = isCrossMsg(upEnterpriseAccount);
				logger.debug("auto reply start, fullSenderId:{} appId:{} messageContent:{}", fullSenderId, appId, messageContent);

				try {

					GetAutoReplyResult autoReplyResult = null;
					if (isCross) {
						autoReplyResult = msgAutoReplyService.getCrossAutoReply(upEnterpriseAccount, fullSenderId, appId, messageContent);
						logger.info("getAutoReply result,upEnterpriseAccount:{}, fullSenderId:{} appId:{} messageContent:{} result:{}", upEnterpriseAccount, fullSenderId, appId, messageContent, autoReplyResult);
					} else {
						autoReplyResult = msgAutoReplyService.getAutoReply(fullSenderId, appId, messageContent);
						logger.info("getAutoReply result, fullSenderId:{} appId:{} messageContent:{} result:{}", fullSenderId, appId, messageContent, autoReplyResult);
					}

					if (!autoReplyResult.isSuccess()) {
						logger.error("getAutoReply error, fullSenderId:{} appId:{} messageContent:{} result:{}", fullSenderId, appId, messageContent, autoReplyResult);
						return;
					}
					if (!autoReplyResult.isNeedReply()) {
						return;
					}
					List<String> fsUser = Splitter.on(".").splitToList(fullSenderId);
					String enterpriseAccount = fsUser.get(1);
					String userId = fsUser.get(2);
					List<Integer> toUserList = Lists.newArrayList(Integer.valueOf(userId));
					if (isCross) {
						// 互联消息回复
						replyOutMessage(autoReplyResult, appId, upEnterpriseAccount, enterpriseAccount, toUserList, notify.getMessageId());
						/**
						if(REPLY_KEYWORD == autoReplyResult.getType()) {
							FengYunUtil.reportEaConnMsg(appId, upEnterpriseAccount, enterpriseAccount, MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY);
						}*/
					} else {
						replyInnerMessage(autoReplyResult, appId, enterpriseAccount, toUserList, notify.getMessageId());
					}
				} catch (Exception e) {
					logger.error("auto reply failed, fullSenderId:{} appId:{} messageContent:{}", fullSenderId, appId, messageContent, e);
				}

			}

			private void replyInnerMessage(GetAutoReplyResult autoReplyResult, String appId, String enterpriseAccount, List<Integer> toUserList, long messageId) {
				if (autoReplyResult.getReplyType() == AutoReplyMsgTypeEnum.TXT) {
					// 文本消息发送
					SendTextMessageVO param = new SendTextMessageVO();
					param.setAppId(appId);
					param.setEnterpriseAccount(enterpriseAccount);
					param.setToUserList(toUserList);
					param.setContent(autoReplyResult.getReplyTxt());
					param.setType(MessageTypeEnum.TEXT);
					param.setPostId(UidUtil.getUid());

					if (autoReplyResult.getType() == AutoReplyTypeEnum.REPLY_DEFAULT) {
						sendMessageService.sendTextMessage(param, MessageSendTypeEnum.DEFAULT_AUTO_REPLY);
					} else {
						sendMessageService.sendTextMessage(param, MessageSendTypeEnum.KEYWORDS_AUTO_REPLY);

						/** 更新这个session下面所有记录为 已读已回复， 不然 用户消息 上的红点不会消失。 */
						messageRecordManager.updateSingleUserSessionStatus(enterpriseAccount, DBDataRuleUtil.buildDBSessionId(appId, toUserList.get(0), null), messageId, MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
					}

				} else {
					// 图文消息发送
					AccepterVO accepter = new AccepterVO();
					accepter.setEa(enterpriseAccount);
					accepter.setIsAllEmployees(false);
					accepter.setEmployees(toUserList);

					com.facishare.open.material.api.model.vo.MessageVO param = new MessageVO();
					param.setAppId(appId);
					param.setCreatorTypeEnum(CreatorTypeEnum.AUTO_REPLY);
					param.setMaterialType(MaterialTypeEnum.IMAGE_TEXT);
					param.setMaterialId(autoReplyResult.getReplyImgTxtID());
					param.setAccepterVO(accepter);

					if (autoReplyResult.getType() == AutoReplyTypeEnum.REPLY_DEFAULT) {
						materialMessageService.sendMessage(param, com.facishare.open.material.api.enums.MessageSendTypeEnum.DEFAULT_AUTO_REPLY);
					} else {
						materialMessageService.sendMessage(param, com.facishare.open.material.api.enums.MessageSendTypeEnum.AUTO_REPLY_SEND);

						/** 更新这个session下面所有记录为 已读已回复， 不然 用户消息 上的红点不会消失。 */
						messageRecordManager.updateSingleUserSessionStatus(enterpriseAccount, DBDataRuleUtil.buildDBSessionId(appId, toUserList.get(0), null), messageId, MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
					}

				}
			}

			private void replyOutMessage(GetAutoReplyResult autoReplyResult, String appId, String upEnterpriseAccount, String enterpriseAccount, List<Integer> toUserList, long messageId) {
				logger.info("replyOutMessage autoReplyResult:{},upEnterpriseAccount:{},enterpriseAccount:{}", autoReplyResult, upEnterpriseAccount, enterpriseAccount);
				if (autoReplyResult.getReplyType() == AutoReplyMsgTypeEnum.TXT) {
					// 文本消息发送
					com.facishare.open.msg.model.MessageVO msg = new com.facishare.open.msg.model.MessageVO();
					msg.setAppId(appId);
					msg.setContent(autoReplyResult.getReplyTxt());
					msg.setEnterpriseAccount(enterpriseAccount);
					msg.setToUserList(toUserList);
					msg.setPostId(UidUtil.getUid());
					msg.setType(MessageTypeEnum.TEXT_QIXIN);

					SendMsgToEaConnServiceVO svo = new SendMsgToEaConnServiceVO();
					svo.setUpstreamEa(upEnterpriseAccount);
					svo.setMsgList(Lists.newArrayList(msg));
					MsgBaseResult sendMsgResult = null;
					if (autoReplyResult.getType() == AutoReplyTypeEnum.REPLY_DEFAULT) {
						svo.setMessageSendType(MessageSendTypeEnum.CROSS_DEFAULT_AUTO_REPLY);
						sendMsgResult = sendMessageService.sendMsgToEaConnService(svo);
					} else {
						svo.setMessageSendType(MessageSendTypeEnum.CROSS_KEYWORDS_AUTO_REPLY);
						sendMsgResult = sendMessageService.sendMsgToEaConnService(svo);

						/** 更新这个session下面所有记录为 已读已回复， 不然 用户消息 上的红点不会消失。 */
						messageRecordManager.updateSingleUserSessionStatus(enterpriseAccount, DBDataRuleUtil.buildDBSessionId(appId, toUserList.get(0), enterpriseAccount), messageId, MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
					}
					logger.info("replyOutMessage txt sendMsgResult:{}", sendMsgResult);
				} else {
					// 图文消息发送
					AccepterVO accepter = new AccepterVO();
					accepter.setEa(upEnterpriseAccount);
					accepter.setIsAllEmployees(false);
					// accepter.setEmployees(toUserList);

					DownstreamAccepterVO downstreamAccepterVO = new DownstreamAccepterVO();
					downstreamAccepterVO.setFsEas(Lists.newArrayList(enterpriseAccount));
					downstreamAccepterVO.setUserIds(toUserList.stream().map(x -> "E." + enterpriseAccount + "." + x).collect(Collectors.toList()));

					com.facishare.open.material.api.model.vo.MessageVO param = new MessageVO();
					param.setAppId(appId);
					param.setCreatorTypeEnum(CreatorTypeEnum.AUTO_REPLY);
					param.setMaterialType(MaterialTypeEnum.IMAGE_TEXT);
					param.setMaterialId(autoReplyResult.getReplyImgTxtID());
					param.setAccepterVO(accepter);
					param.setDownstreamAccepterVO(downstreamAccepterVO);
					logger.info("replyOutMessage imgtxt param:{}", param);
					com.facishare.open.common.result.BaseResult<String> materialSendResult = null;
					if (autoReplyResult.getType() == AutoReplyTypeEnum.REPLY_DEFAULT) {
						materialSendResult = materialMessageService.sendMessage(param, com.facishare.open.material.api.enums.MessageSendTypeEnum.DEFAULT_AUTO_REPLY);
					} else {
						materialSendResult = materialMessageService.sendMessage(param, com.facishare.open.material.api.enums.MessageSendTypeEnum.AUTO_REPLY_SEND);

						/** 更新这个session下面所有记录为 已读已回复， 不然 用户消息 上的红点不会消失。 */
						messageRecordManager.updateSingleUserSessionStatus(enterpriseAccount, DBDataRuleUtil.buildDBSessionId(appId, toUserList.get(0), enterpriseAccount), messageId, MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
					}
					logger.info("replyOutMessage imgtxt materialSendResult:{}", materialSendResult);
				}
			}
		});
	}

	private boolean isCrossMsg(int source) {
		return SOURCE_CROSS_U_C == source || SOURCE_CROSS_C_U == source;
	}

	private boolean isCrossMsg(String upsteamEa) {
		return StringUtils.isNotEmpty(upsteamEa);
	}

	/**
	 * 获取员工详情
	 *
	 * @param ea
	 * @param employeeIds
	 * @return
	 */
	private List<EmployeeDto> getEmployeeDtoList(String ea, List<Integer> employeeIds){
		BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
		batchGetEmployeeDtoArg.setEnterpriseId(this.getEnterpriseId(ea));
		batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
		batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

		List<EmployeeDto> employeeDtos = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg).getEmployeeDtos();
		if (CollectionUtils.isEmpty(employeeDtos)){
			return Lists.newArrayList();
		}
		// 将图片名称换为图片路径
		employeeDtos.stream().forEach(employeeDto -> {
			if (StringUtils.isNotEmpty(employeeDto.getProfileImage())) {
				String imagePath = "https://a" + (byte) (employeeDto.getProfileImage().charAt(employeeDto.getProfileImage().length() - 1)) % 7
						+ EMPLOYEE_PROFILE_IMAGE_CDN_PATH + employeeDto.getProfileImage() + ".jpg&ea=" + ea;
				employeeDto.setProfileImage(imagePath);
			}
		});
		return employeeDtos;
	}

	/**
	 * ea转ei
	 * @param enterpriseAccount
	 * @return
	 */
	private int getEnterpriseId(String enterpriseAccount) {
		try {
			return eIEAConverter.enterpriseAccountToId(enterpriseAccount);
		} catch (Exception e) {
			throw new IllegalArgumentException(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION.getErrorMsg());
		}
	}
}
