package com.facishare.open.msg.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEA;
import com.facishare.fsi.proxy.service.GlobalConfigService;
import com.facishare.open.app.center.api.model.vo.AppNameLogoVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppOrComponentNameService;
import com.facishare.open.autoreplymsg.model.CustomerSessionInfoVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.constant.CustomerServiceMsgType;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.dao.CustomerEvaluateDAO;
import com.facishare.open.msg.dao.OpenMsgDAO;
import com.facishare.open.msg.manager.MessageRecordManager;
import com.facishare.open.msg.model.*;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.oauth.result.AppResult;
import com.facishare.open.oauth.service.AppService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetLevelOneDepartmentByMainDepartmentArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetLevelOneDepartmentByMainDepartmentResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.qixin.api.constant.MessageType;
import com.fxiaoke.enterpriserelation2.arg.GetEnterpriseShortNameArg;
import com.fxiaoke.enterpriserelation2.arg.GetOuterTenantIdByEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.net.UrlEscapers;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 * @date 2016年3月4日
 */
//IgnoreI18nFile
@Service("messageExhibitionService")
public class MessageExhibitionServiceImpl implements MessageExhibitionService {

	public static final Logger logger = LoggerFactory.getLogger(MessageExhibitionServiceImpl.class);

	@Autowired
	private MessageRecordManager messageRecordManager;

	@Autowired
	private EmployeeProviderService employeeProviderService;
	@Autowired
	private EIEAConverter eIEAConverter;

	@Autowired
	private OpenAppOrComponentNameService openAppNameService;

	@Autowired
	private OpenMsgDAO openMsgDAO;
	
	@Autowired
	private GlobalConfigService globalConfigService;
	
	@Autowired
	private MsgCustomerService msgCustomerService;
	
	@Autowired
	private CustomerEvaluateDAO customerEvaluateDAO;
	
	@Autowired
	private AppService appService;

    @ReloadableProperty("employee_profile_image_cdn_path")
    private String EMPLOYEE_PROFILE_IMAGE_CDN_PATH=".fspage.com/FSC/EM/Avatar/GetAvatar?path=";
	
	@Autowired
	private DepartmentProviderService departmentProviderService;

	@Autowired
	private EnterpriseRelationService enterpriseRelationService;

	@Autowired
	private FxiaokeAccountService fxiaokeAccountService;

	private static final String UNSUPPORTED_MESSAGE = "[不支持的消息]";

	private static final Map<String, String> kvs = new ImmutableMap.Builder<String, String>().put("L", "[位置]").put("I", "[图片]").put("A", "[语音]").put("D", "[文档]").put("E", "[表情]").put("IMAGE_TEXT", "[图文]").put("OPENMESSAGE", "[图文]").put("IGT", "[不支持的消息]").put("LWN", "[不支持的消息]").put("LWV", "[不支持的消息]").build();

	private static final Map<Integer, String> typeDesc = new ImmutableMap.Builder<Integer, String>().put(-2, "")// -2表示上行事件消息
			.put(-1, "")// -1表示上行普通消息 use
			.put(0, "") // 0表示其他<包括音视频，红包>
			.put(1, "")// 1表示第三方推送
			.put(2, "")// 2表示管理员推送
			.put(3, "(自动回复)")// 3表示默认自动回复 use
			.put(4, "(客服)")// 4表示管理员的回复 use
			.put(5, "(自动回复)")// 5表示关键字回复 use
			.put(6, "")// 6表示系统提示
			.put(7, "(客服)")// 管理员的回复 use
			.put(8, "(自动回复)")// 默认自动回复 use
			.put(9, "(自动回复)")// 关键字回复 use
			.build();

	@Override
	public MessageExhibitionResult<Pager<UserSessionVO>> queryAllUserSessions(int currentPage, int pageSize, Map<String, Object> paramMap) {
		logger.info("queryAllUserSessions currentPage:{} pageSize:{} paramMap:{}", currentPage, pageSize, paramMap);

		MessageExhibitionResult<Pager<UserSessionVO>> result = null;

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(currentPage);
		pager.setPageSize(pageSize);
		pager.setParams(paramMap);

		try {
			pager = messageRecordManager.queryAllUserSessions(pager);

			Pager<UserSessionVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());

			// 优化通讯录批量获取人员信息 start
			String ea = (String) paramMap.get("ea");
			Map<String, EmployeeDto> employeeMap = loadEmployeeMsgMap(pager.getData(), ea);

			List<UserSessionVO> list = pager.getData().stream().map(openMsg -> openMsgDO2UserSessionVO(openMsg, employeeMap, null)).collect(Collectors.toList());

			voPager.setData(list);

			result = new MessageExhibitionResult<>(voPager);

		} catch (Exception e) {
			logger.error("queryAllUserSessions currentPage:{} pageSize:{} paramMap:{} error:", currentPage, pageSize, paramMap, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		logger.info("queryAllUserSessions currentPage:{} pageSize:{} paramMap:{} result:{}", currentPage, pageSize, paramMap, result);
		return result;
	}

	@Override
	public MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> querySingleUserSessionMessages(QuerySingleUserSessionVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			Map<String, Object> paramMap = Maps.newHashMap();
			paramMap.put("ea", arg.getEa());
			paramMap.put("appId", arg.getAppId());
			paramMap.put("sessionId", arg.getSessionId());
			paramMap.put("lastMessageId", arg.getLastMessageId());
			return querySingleUserSessionMessages(arg.getCurrentPage(), arg.getPageSize(), paramMap);
		}

		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("upEnterpriseAccount", arg.getEa());
		paramMap.put("appId", arg.getAppId());
		paramMap.put("sessionId", arg.getSessionId());
		paramMap.put("lastMessageId", arg.getLastMessageId());
		MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> result = null;

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(arg.getCurrentPage());
		pager.setPageSize(arg.getPageSize());
		pager.setParams(paramMap);

		try {
			pager = openMsgDAO.queryEaConnSingleUserSessionMessages(pager);

			Pager<SingleUserSessionMessagesVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());

			String sessionId = (String) paramMap.get("sessionId");
			// String ea = (String) paramMap.get("ea");
			List<String> dialogue = Splitter.on("-").splitToList(sessionId);
			String appId = dialogue.get(0);
			String ea = dialogue.get(1);
			String userId = dialogue.get(2);

			final AppNameLogoVO appNameLogo = getAppNameLogoVO(appId);
			final EmployeeDto employee = getEmployee(ea, userId);
			final String enerpriseShortName = getEnterpriseShortName(arg.getEa(), ea);

			// 获取管理员信息
			List<Integer> adminUserIds = new ArrayList<>();
			pager.getData().stream().forEach(openMsgDO -> {
				if (openMsgDO.getSendType() > 0) {
					if (openMsgDO.getAdminUserId() != null) {
						Integer adminUserId = FsUserVO.getUserId(openMsgDO.getAdminUserId());
						if (!adminUserIds.contains(adminUserId)) {
							adminUserIds.add(adminUserId);
						}
					}
				}
			});
			final Map<Integer, EmployeeDto> adminUserMap = getEmployeeMap(arg.getEa(), adminUserIds);

			List<SingleUserSessionMessagesVO> list = pager.getData().stream().map(openMsg -> openMsgDO2SingleUserSessionMessagesVO(openMsg, appNameLogo, employee, adminUserMap, enerpriseShortName)).collect(Collectors.toList());
			voPager.setData(list);

			result = new MessageExhibitionResult<>(voPager);
		} catch (Exception e) {
			logger.error("querySingleUserSessionMessages arg:{} error:", arg, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		logger.info("querySingleUserSessionMessages arg:{}, result:{]", arg, result);
		return result;
	}

	@Override
	public MessageExhibitionResult<Void> updateSingleUserSessionStatus(UpdateSingleUserSessionVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return updateSingleUserSessionStatus(arg.getUpStreamEa(), arg.getSessionId(), arg.getLastMessageId(), arg.getStatus());
		}

		try {
			openMsgDAO.updateEaConnSingleUserSessionStatus(arg.getUpStreamEa(), arg.getSessionId(), arg.getLastMessageId(), arg.getStatus());
			return new MessageExhibitionResult<>();
		} catch (Exception e) {
			logger.error("updateSingleUserSessionStatus arg:{}, get exception, ", arg, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageExhibitionResult<Pager<UserSessionVO>> queryAllUserSessions(QueryAllUserSessionVO arg) {

		if (CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE == arg.getFilterType()) {
			Map<String, Object> paramMap = Maps.newHashMap();
			paramMap.put("ea", arg.getEa());
			paramMap.put("appId", arg.getAppId());
			paramMap.put("status", arg.getStatus());
			paramMap.put("lastMessageId", arg.getLastMessageId());

			return this.queryAllUserSessions(arg.getCurrentPage(), arg.getPageSize(), paramMap);
		}

		MessageExhibitionResult<Pager<UserSessionVO>> result = null;
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("upEnterpriseAccount", arg.getEa());
		paramMap.put("appId", arg.getAppId());
		paramMap.put("status", arg.getStatus());
		paramMap.put("lastMessageId", arg.getLastMessageId());

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(arg.getCurrentPage());
		pager.setPageSize(arg.getPageSize());
		pager.setParams(paramMap);

		try {
			pager = openMsgDAO.queryEaConnAllUserSessions(pager);

			Pager<UserSessionVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());

			/**
			 * 按照下游企业 遍历所有下游人员的员工信息。
			 */

			Map<String, List<OpenMsgDO>> eaGroupMap = new LinkedHashMap<>();// 要用有序的LinkedHashMap
			for (OpenMsgDO openMsgDO : pager.getData()) {
				if (null != eaGroupMap.get(openMsgDO.getEnterpriseAccount())) {
					eaGroupMap.get(openMsgDO.getEnterpriseAccount()).add(openMsgDO);
				} else {
					eaGroupMap.put(openMsgDO.getEnterpriseAccount(), Lists.newArrayList(openMsgDO));
				}
			}
			List<UserSessionVO> list = new ArrayList<>();
			Map<String, EmployeeDto> employeeMap = Maps.newHashMap();
			Map<String, String> eaNamesMap = Maps.newHashMap();
			for (String ea : eaGroupMap.keySet()) {
				eaNamesMap.put(ea, getEnterpriseShortName(arg.getEa(), ea));
				employeeMap.putAll(loadEmployeeMsgMap(eaGroupMap.get(ea), ea));
			}

			for (OpenMsgDO openMsgDO : pager.getData()) {
				UserSessionVO vo = openMsgDO2UserSessionVO(openMsgDO, employeeMap, eaNamesMap.get(openMsgDO.getEnterpriseAccount()));
				list.add(vo);
			}
			voPager.setData(list);

			result = new MessageExhibitionResult<>(voPager);

		} catch (Exception e) {
			logger.error("queryAllUserSessions arg:{} error:", arg, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		logger.info("queryAllUserSessions arg:{} result:{}", arg, result);
		return result;
	}

	@Override
	public MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> querySingleUserSessionMessages(int currentPage, int pageSize, Map<String, Object> paramMap) {

		MessageExhibitionResult<Pager<SingleUserSessionMessagesVO>> result = null;

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(currentPage);
		pager.setPageSize(pageSize);
		pager.setParams(paramMap);

		try {
			pager = messageRecordManager.querySingleUserSessionMessages(pager);

			Pager<SingleUserSessionMessagesVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());

			String sessionId = (String) paramMap.get("sessionId");
			String ea = (String) paramMap.get("ea");
			List<String> dialogue = Splitter.on("-").splitToList(sessionId);
			String appId = dialogue.get(0);
			String userId = dialogue.get(1);

			final AppNameLogoVO appNameLogo = getAppNameLogoVO(appId);
			final EmployeeDto employee = getEmployee(ea, userId);
			// 获取管理员信息
			List<Integer> adminUserIds = new ArrayList<>();
			pager.getData().stream().forEach(openMsgDO -> {
				if (openMsgDO.getSendType() > 0) {
					if (openMsgDO.getAdminUserId() != null) {
						Integer adminUserId = FsUserVO.getUserId(openMsgDO.getAdminUserId());
						if (!adminUserIds.contains(adminUserId)) {
							adminUserIds.add(adminUserId);
						}
					}
				}
			});
			final Map<Integer, EmployeeDto> adminUserMap = getEmployeeMap(ea, adminUserIds);

			List<SingleUserSessionMessagesVO> list = pager.getData().stream().map(openMsg -> openMsgDO2SingleUserSessionMessagesVO(openMsg, appNameLogo, employee, adminUserMap, "")).collect(Collectors.toList());
			voPager.setData(list);

			result = new MessageExhibitionResult<>(voPager);
		} catch (Exception e) {
			logger.error("querySingleUserSessionMessages currentPage:{} pageSize:{} paramMap:{} error:", currentPage, pageSize, paramMap, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		logger.info("querySingleUserSessionMessages currentPage:{} pageSize:{} paramMap:{} result:{}", currentPage, pageSize, paramMap, result);
		return result;
	}

	/**
	 * 获取员工信息
	 * 
	 * @param fsEa
	 * @param employeeIds
	 * @return
	 */
	private Map<Integer, EmployeeDto> getEmployeeMap(String fsEa, List<Integer> employeeIds) {

		HashMap<Integer, EmployeeDto> employeeMap = new HashMap<>();

		if (CollectionUtils.isNotEmpty(employeeIds)){
			List<EmployeeDto> employeeDtoList = this.getEmployeeDtoList(fsEa, employeeIds);
			if (CollectionUtils.isNotEmpty(employeeDtoList)){
				employeeDtoList.forEach(employee -> {
					employeeMap.put(employee.getEmployeeId(), employee);
				});
			}
		}
		return employeeMap;
	}

	@Override
	public MessageExhibitionResult<Void> updateSingleUserSessionStatus(String ea, String sessionId, long lastMessageId, int status) {

		MessageExhibitionResult<Void> result = null;

		try {
			messageRecordManager.updateSingleUserSessionStatus(ea, sessionId, lastMessageId, status);

			result = new MessageExhibitionResult<>();
		} catch (Exception e) {
			logger.error("updateSingleUserSessionStatus ea:{} lastMessageId:{} status:{} error:", ea, lastMessageId, status, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Boolean> existUnReadUserSessions(String ea, String appId, long lastMessageId) {

		try {
			Boolean result = messageRecordManager.existUnReadUserSessions(ea, appId, lastMessageId);

			return new MessageExhibitionResult<>(result);
		} catch (Exception e) {
			logger.error("existUnReadUserSessions ea:{} appId:{} lastMessageId:{} error:", ea, appId, lastMessageId, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	@Override
	public MessageExhibitionResult<Boolean> existUnReadUserSessions(ExistUnReadUserSessionVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return existUnReadUserSessions(arg.getUpStreamEa(), arg.getAppId(), arg.getLastMessageId());
		}

		try {
			long count = openMsgDAO.countUnReadUserSessions(arg.getUpStreamEa(), arg.getAppId(), arg.getLastMessageId());
			return new MessageExhibitionResult<>(count > 0L ? true : false);
		} catch (Exception e) {
			logger.error("existUnReadUserSessions arg:{} error:", arg, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageExhibitionResult<Long> countSingleUserSessionUnReadMessages(String ea, String sessionId, long lastMessageId) {
		logger.info("countSingleUserSessionUnReadMessages ea:{} sessionId:{} lastMessageId:{}", ea, sessionId, lastMessageId);

		MessageExhibitionResult<Long> result = null;

		try {
			Long msgRecordResult = messageRecordManager.countSingleUserSessionUnReadMessages(ea, sessionId, lastMessageId);

			result = new MessageExhibitionResult<>(msgRecordResult);

		} catch (Exception e) {
			logger.error("countSingleUserSessionUnReadMessages ea:{} sessionId:{} lastMessageId:{} error:", ea, sessionId, lastMessageId, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Long> countSingleUserSessionUnReadMessages(CountSingleUserSessionUnReadMessageVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return countSingleUserSessionUnReadMessages(arg.getUpStreamEa(), arg.getSessionId(), arg.getLastMessageId());
		}

		try {
			long msgRecordResult = openMsgDAO.countEaConnSingleUserSessionUnReadMessages(arg.getUpStreamEa(), arg.getSessionId(), arg.getLastMessageId());
			return new MessageExhibitionResult<>(msgRecordResult);
		} catch (Exception e) {
			logger.error("countSingleUserSessionUnReadMessages arg:{} error:", arg, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessionsByAppIds(String ea, List<String> appIds) {
		logger.info("queryUnReadUserSessionsByAppIds ea:{} appIds:{}", ea, appIds);

		MessageExhibitionResult<Map<String, Boolean>> result = null;

		try {
			Map<String, Boolean> unReadUserSessionsResult = messageRecordManager.queryUnReadUserSessionsByAppIds(ea, appIds);

			result = new MessageExhibitionResult<>(unReadUserSessionsResult);
		} catch (Exception e) {
			logger.error("queryUnReadUserSessionsByAppIds ea:{} appIds:{} error", ea, appIds, e);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessionsByAppIds(QueryUnReadUserSessionsByAppIdVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return queryUnReadUserSessionsByAppIds(arg.getUpStreamEa(), arg.getAppIds());
		}

		MessageExhibitionResult<Map<String, Boolean>> result = null;
		try {
			Map<String, Boolean> unReadUserSessionsResult = openMsgDAO.queryEaConnUnReadUserSessionsByAppIds(arg.getUpStreamEa(), arg.getAppIds());
			result = new MessageExhibitionResult<>(unReadUserSessionsResult);
		} catch (Exception e) {
			logger.error("queryUnReadUserSessionsByAppIds arg:{} error, ", arg, e);
		}
		return result;
	}

	@Override
	public MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessions(String ea, List<String> appIds) {
		logger.info("queryUnReadUserSessions ea:{} appIds:{}", ea, appIds);
		if (StringUtils.isBlank(ea) || org.springframework.util.CollectionUtils.isEmpty(appIds)) {
			return new MessageExhibitionResult<>(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		}
		try {
			Map<String, Boolean> unReadUserSessionsResult = messageRecordManager.queryUnReadUserSessions(ea, appIds, false);
			return new MessageExhibitionResult<>(unReadUserSessionsResult);
		} catch (Exception e) {
			logger.error("queryUnReadUserSessions ea:{} appIds:{} error", ea, appIds, e);
		}
		return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
	}

	@Override
	public MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessions(QueryUnReadUserSessionVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return queryUnReadUserSessions(arg.getUpStreamEa(), arg.getAppIds());
		}

		try {
			Map<String, Boolean> unReadUserSessionsResult = messageRecordManager.queryUnReadUserSessions(arg.getUpStreamEa(), arg.getAppIds(), true);
			return new MessageExhibitionResult<>(unReadUserSessionsResult);
		} catch (Exception e) {
			logger.error("queryUnReadUserSessions arg:{} error, ", arg, e);
		}
		return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
	}

	@Override
	public MessageExhibitionResult<Void> resetUnReadUserSessions(String ea, List<String> appIds) {
		logger.info("resetUnReadUserSessions ea:{} appIds:{}", ea, appIds);
		if (StringUtils.isBlank(ea) || org.springframework.util.CollectionUtils.isEmpty(appIds)) {
			return new MessageExhibitionResult<>(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		}
		try {
			messageRecordManager.resetUnReadUserSessions(ea, appIds);
			return new MessageExhibitionResult<>(MsgCodeEnum.SUCCESS);
		} catch (Exception e) {
			logger.error("resetUnReadUserSessions ea:{} appIds:{} error", ea, appIds, e);
		}
		return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
	}

	@Override
	public MessageExhibitionResult<Void> resetUnReadUserSessions(ResetUnReadUserSessionVO arg) {
		return resetUnReadUserSessions(arg.getUpStreamEa(), arg.getAppIds());
	}

	@Override
	public MessageExhibitionResult<Void> updateMsgStarStatus(String ea, long messageId, String markUserId, int starStatus) {

		MessageExhibitionResult<Void> result = null;

		try {
			openMsgDAO.updateMsgStarStatus(ea, messageId, markUserId, starStatus, false);

			result = new MessageExhibitionResult<>();
		} catch (Exception e) {
			logger.error("updateMsgStarStatus ea:{} messageId:{} markUserId:{} starStatus:{} error:", ea, messageId, markUserId, starStatus, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;

	}

	@Override
	public MessageExhibitionResult<Void> updateMsgStarStatus(UpdateMsgStarStatusVO arg) {
		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			return updateMsgStarStatus(arg.getUpStreamEa(), arg.getMessageId(), arg.getMarkUserId(), arg.getStarStatus());
		}

		MessageExhibitionResult<Void> result = null;

		try {
			openMsgDAO.updateMsgStarStatus(arg.getUpStreamEa(), arg.getMessageId(), arg.getMarkUserId(), arg.getStarStatus(), true);
			result = new MessageExhibitionResult<>();
		} catch (Exception e) {
			logger.error("updateMsgStarStatus arg:{} error:", arg, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Pager<StartMessagesVO>> queryAllStarMessages(int currentPage, int pageSize, Map<String, Object> paramMap) {

		MessageExhibitionResult<Pager<StartMessagesVO>> result = null;

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(currentPage);
		pager.setPageSize(pageSize);
		pager.setParams(paramMap);

		try {
			pager = openMsgDAO.queryAllStarMessages(pager);

			Pager<StartMessagesVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());
			List<StartMessagesVO> list = Lists.newArrayList();
			if (!Objects.isNull(pager.getData()) && !pager.getData().isEmpty()) {
				final AppNameLogoVO appNameLogo = getAppNameLogoVO((String) paramMap.get("appId"));

				Map<Integer, EmployeeDto> employeeMap = loadEmployeeMap(pager.getData(), (String) paramMap.get("ea"));
				list = pager.getData().stream().map(openMsgDO -> openMsgDO2StartMessageVO(openMsgDO, appNameLogo, employeeMap, null, employeeMap)).collect(Collectors.toList());
			}
			voPager.setData(list);
			result = new MessageExhibitionResult<>(voPager);
		} catch (Exception e) {
			logger.error("queryAllStarMessages currentPage:{} pageSize:{} params:{} error:", currentPage, pageSize, paramMap, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Pager<StartMessagesVO>> queryAllStarMessages(QueryAllStarMessageVO arg) {

		if (arg.getFilterType() == CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE) {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("ea", arg.getUpStreamEa());
			paramMap.put("appId", arg.getAppId());
			return queryAllStarMessages(arg.getCurrentPage(), arg.getPageSize(), paramMap);
		}

		MessageExhibitionResult<Pager<StartMessagesVO>> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("upEnterpriseAccount", arg.getUpStreamEa());
		paramMap.put("appId", arg.getAppId());

		Pager<OpenMsgDO> pager = new Pager<>();
		pager.setCurrentPage(arg.getCurrentPage());
		pager.setPageSize(arg.getPageSize());
		pager.setParams(paramMap);

		try {
			pager = openMsgDAO.queryEaConnAllStarMessages(pager);

			Pager<StartMessagesVO> voPager = new Pager<>();
			voPager.setCurrentPage(pager.getCurrentPage());
			voPager.setPageSize(pager.getPageSize());
			voPager.setRecordSize(pager.getRecordSize());
			List<StartMessagesVO> list = new ArrayList<>();

			if(!Objects.isNull(pager.getData()) && !pager.getData().isEmpty()){
				Map<String, List<OpenMsgDO>> msgGroupByEa = openMsgGroupByEa(pager.getData());
				
				final AppNameLogoVO appNameLogo = getAppNameLogoVO((String) paramMap.get("appId"));
				
				// 星标导出通讯录优化获取人员信息 start
				// Map<Integer, Employee> employeeMap =
				loadEmployeeMap(pager.getData(), (String) paramMap.get("ea"));
				List<Integer> adminUserIds = openMsgAdminUserIds(pager.getData());
				Map<Integer, EmployeeDto> upEmployeeMap = loadEmployeeMapByIds(adminUserIds, arg.getUpStreamEa());
				Map<Integer, EmployeeDto> employeeMap = Maps.newHashMap();
				Map<String, String> eaNamesMap = Maps.newHashMap();
				for (String ea : msgGroupByEa.keySet()) {
					employeeMap.putAll(loadEmployeeMap(msgGroupByEa.get(ea), ea));
					String eaShortName = this.getEnterpriseShortName(arg.getUpStreamEa(), ea);
					eaNamesMap.put(ea, eaShortName);
				}
				for(OpenMsgDO openMsgDO :pager.getData()){
					StartMessagesVO vo = openMsgDO2StartMessageVO(openMsgDO, appNameLogo, employeeMap, eaNamesMap.get(openMsgDO.getEnterpriseAccount()),upEmployeeMap);
					list.add(vo);
				}
			}
			voPager.setData(list);

			result = new MessageExhibitionResult<>(voPager);
		} catch (Exception e) {
			logger.error("queryAllStarMessages arg:{} error:", arg, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
		return result;
	}

	@Override
	public MessageExhibitionResult<List<ExportMsgVO>> queryAllExportMessages(Long beginTime, Long endTime, Map<String, Object> paramMap) {

		MessageExhibitionResult<List<ExportMsgVO>> result = null;

		try {
			List<OpenMsgDO> listData = openMsgDAO.queryAllExportMessages(beginTime, endTime, paramMap);

			final AppNameLogoVO appNameLogo = getAppNameLogoVO((String) paramMap.get("appId"));

			// 优化通讯录批量获取人员信息 start
			Map<Integer, EmployeeDto> employeeMap = loadEmployeeMap(listData, (String) paramMap.get("ea"));

			List<ExportMsgVO> list = listData.stream().map(openMsgDO -> openMsgDO2ExportMsgVO(openMsgDO, appNameLogo, employeeMap, null, null)).collect(Collectors.toList());

			result = new MessageExhibitionResult<>(list);
		} catch (Exception e) {
			logger.error("queryAllExportMessages beginTime:{} endTime:{} params:{} error:", beginTime, endTime, paramMap, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<List<ExportMsgVO>> queryAllCrossExportMessages(Long beginTime, Long endTime, Map<String, Object> paramMap) {

		MessageExhibitionResult<List<ExportMsgVO>> result = null;

		try {
			String upEa = String.valueOf(paramMap.get("ea"));
			List<OpenMsgDO> listData = openMsgDAO.queryAllCrossExportMessages(beginTime, endTime, paramMap);

			final AppNameLogoVO appNameLogo = getAppNameLogoVO((String) paramMap.get("appId"));

			Map<String, List<OpenMsgDO>> eaGroupMap = new HashMap<>();
			List<Integer> adminUserIds = new ArrayList<>();
			for (OpenMsgDO openMsgDO : listData) {
				if (null != eaGroupMap.get(openMsgDO.getEnterpriseAccount())) {
					eaGroupMap.get(openMsgDO.getEnterpriseAccount()).add(openMsgDO);
				} else {
					eaGroupMap.put(openMsgDO.getEnterpriseAccount(), Lists.newArrayList(openMsgDO));
				}
				if (openMsgDO.getSendType() > 0 && !Objects.isNull(openMsgDO.getAdminUserId())) {// 获取管理员信息
					Integer adminUserId = FsUserVO.getUserId(openMsgDO.getAdminUserId());
					if (!adminUserIds.contains(adminUserId)) {
						adminUserIds.add(adminUserId);
					}
				}
			}

			final Map<Integer, EmployeeDto> adminUserMap = getEmployeeMap(upEa, adminUserIds);

			List<ExportMsgVO> list = new ArrayList<>();
			for (String ea : eaGroupMap.keySet()) {
				Map<Integer, EmployeeDto> employeeMap = loadEmployeeMap(eaGroupMap.get(ea), ea);
				String eaShortName = this.getEnterpriseShortName(upEa, ea);
				List<ExportMsgVO> exportMsgVOList = eaGroupMap.get(ea).stream().map(openMsg -> openMsgDO2ExportMsgVO(openMsg, appNameLogo, employeeMap, adminUserMap, eaShortName)).collect(Collectors.toList());
				list.addAll(exportMsgVOList);
			}

			result = new MessageExhibitionResult<>(list);
		} catch (Exception e) {
			logger.error("queryAllCrossExportMessages beginTime:{} endTime:{} params:{} error:", beginTime, endTime, paramMap, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Long> countExportMessages(Long beginTime, Long endTime, Map<String, Object> paramMap) {
		MessageExhibitionResult<Long> result = null;

		try {
			long recorderTotalSize = openMsgDAO.countExportMessages(beginTime, endTime, paramMap);

			result = new MessageExhibitionResult<>(recorderTotalSize > 60000L ? 60000L : recorderTotalSize);
		} catch (Exception e) {
			logger.error("countExportMessages beginTime:{} endTime:{} params:{} error:", beginTime, endTime, paramMap, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Long> countSendNumByAppId(String ea, String appId, MessageSendTypeEnum messageSendTypeEnum) {
		MessageExhibitionResult<Long> result = null;

		try {
			long recorderTotalSize = openMsgDAO.countSendNumByAppId(ea, appId, messageSendTypeEnum.getType());

			result = new MessageExhibitionResult<>(recorderTotalSize);
		} catch (Exception e) {
			logger.error("countSendNumByAppId appId:{} messageSendTypeEnum:{} error: {}", appId, messageSendTypeEnum, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Long> countReceiveNumByAppId(String ea, String appId) {
		MessageExhibitionResult<Long> result = null;

		try {
			long recorderTotalSize = openMsgDAO.countReceiveNumByAppId(ea, appId);

			result = new MessageExhibitionResult<>(recorderTotalSize);
		} catch (Exception e) {
			logger.error("countReceiveNumByAppId appId:{} error: {}", appId, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}
	
	@Override
	public MessageExhibitionResult<Long> countReceiveMsgNumByAppId(CountReceiveMsgNumByAppIdArg countReceiveMsgNumByAppIdArg) {
		MessageExhibitionResult<Long> result = null;

		try {
			List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
					MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL,
					MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL,
					MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL);
			
			long recorderTotalSize = openMsgDAO.countMsgNumByConditon(
					countReceiveMsgNumByAppIdArg.getEa(),
					countReceiveMsgNumByAppIdArg.getAppId(),
					countReceiveMsgNumByAppIdArg.getBeginTime(),
					countReceiveMsgNumByAppIdArg.getEndTime(), typeEnumList, false);

			result = new MessageExhibitionResult<>(recorderTotalSize);
		} catch (Exception e) {
			logger.error("countReceiveMsgNumByAppId countReceiveMsgNumByAppIdArg:{} error: {}", countReceiveMsgNumByAppIdArg, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}
    
	@Override
	public MessageExhibitionResult<Long> countUpMsgUserNumByAppId(CountUpMsgUserNumByAppIdArg countUpMsgUserNumByAppIdArg) {
		MessageExhibitionResult<Long> result = null;

		try {
			List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
					MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL,
					MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL,
					MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL);
			
			long recorderTotalSize = openMsgDAO.countMsgNumByConditon(
					countUpMsgUserNumByAppIdArg.getEa(),
					countUpMsgUserNumByAppIdArg.getAppId(),
					countUpMsgUserNumByAppIdArg.getBeginTime(),
					countUpMsgUserNumByAppIdArg.getEndTime(), typeEnumList, true);
			
			result = new MessageExhibitionResult<>(recorderTotalSize);
		} catch (Exception e) {
			logger.error("countUpMsgUserNumByAppId countUpMsgUserNumByAppIdArg:{} error: {}", countUpMsgUserNumByAppIdArg, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}
    
	@Override
	public MessageExhibitionResult<Long> countReplyMsgNumByAppId(CountReplyMsgNumByAppIdArg countReplyMsgNumByAppIdArg) {
		MessageExhibitionResult<Long> result = null;

		try {
			List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
					MessageSendTypeEnum.ADMINISTRATOR_REPLY,
					MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY);
			
			long recorderTotalSize = openMsgDAO.countMsgNumByConditon(
					countReplyMsgNumByAppIdArg.getEa(),
					countReplyMsgNumByAppIdArg.getAppId(),
					countReplyMsgNumByAppIdArg.getBeginTime(),
					countReplyMsgNumByAppIdArg.getEndTime(), typeEnumList, false);
			
			result = new MessageExhibitionResult<>(recorderTotalSize);
		} catch (Exception e) {
			logger.error("countReplyMsgNumByAppId countReplyMsgNumByAppIdArg:{} error: {}", countReplyMsgNumByAppIdArg, e);

			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}
	
	@Override
    public MessageExhibitionResult<StatInternalAppMsgVO> statInternalAppMsg(Long startTime, Long endTime, String appId, String ea, Boolean needDetail) {
		StatInternalAppMsgVO statInternalAppMsgVO = new StatInternalAppMsgVO();
		
		List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
				MessageSendTypeEnum.ADMINISTRATOR_REPLY,
				MessageSendTypeEnum.UPLINK_MESSAGE_NORMAL);
		try {
			List<OpenMsgDO> msgList = openMsgDAO.queryDialogMessages(ea, appId, startTime, endTime, typeEnumList, false);
			
			logger.info("statInternalAppMsg msgList={}", msgList);
			
			Integer upMsgCount = 0;
			Integer customerReplyMsgCount = 0;
			List<DeptStatMsgVO> deptStatList = Lists.newArrayList();
			
			Map<String, Integer> userCountMap = Maps.newLinkedHashMap();
			
			if (msgList != null) {
				 for (OpenMsgDO msg : msgList) {
					if (msg.getSendType() > 0) {
						customerReplyMsgCount++;
						continue;
					}
					upMsgCount++;
					if (userCountMap.get(msg.getSender()) == null) {
						userCountMap.put(msg.getSender(), 1);
					} else {
						Integer sendNum = userCountMap.get(msg.getSender());
						userCountMap.put(msg.getSender(), ++sendNum);
					}
				}
			}
			
			if (needDetail && !userCountMap.isEmpty()) {
		        GetEnterpriseConfigByEA.Args arg = new GetEnterpriseConfigByEA.Args();

		        arg.EnterpriseAccount = ea;
		        GetEnterpriseConfigByEA.Result eIResult = globalConfigService.getEnterpriseConfigByEA(arg);
				
				BatchGetLevelOneDepartmentByMainDepartmentArg batchGetLevelOneDepartmentByMainDepartmentArg = new BatchGetLevelOneDepartmentByMainDepartmentArg();
				batchGetLevelOneDepartmentByMainDepartmentArg.setEnterpriseId(eIResult.EnterpriseConfig.EnterpriseID);
				
				List<Integer> employeeIds = userCountMap.keySet().stream().map(userId -> {
					return Integer.parseInt(userId);
				}).collect(Collectors.toList());
				batchGetLevelOneDepartmentByMainDepartmentArg.setEmployeeIds(employeeIds);
				
				BatchGetLevelOneDepartmentByMainDepartmentResult batchGetLevelOneDepartmentByMainDepartmentResult = departmentProviderService
						.batchGetLevelOneDepartmentByMainDepartment(batchGetLevelOneDepartmentByMainDepartmentArg);
				
				if (batchGetLevelOneDepartmentByMainDepartmentResult != null) {
					Map<Integer, DepartmentDto> userDeptMap = batchGetLevelOneDepartmentByMainDepartmentResult.getBelongMap();
					Map<Integer, DeptStatMsgVO> topDeptMsg = Maps.newHashMap(); 
					if (userDeptMap != null && userDeptMap.size() > 0) {
						for(String userIdStr : userCountMap.keySet()) {
							DepartmentDto departmentDto = userDeptMap.get(Integer.parseInt(userIdStr));
							if (departmentDto == null ) continue;
							
							DeptStatMsgVO deptStatMsgVO = topDeptMsg.get(departmentDto.getDepartmentId());
							if (deptStatMsgVO == null) {
								deptStatMsgVO = new DeptStatMsgVO();
								deptStatMsgVO.setDeptId(departmentDto.getDepartmentId());
								deptStatMsgVO.setDeptName(departmentDto.getName());
								deptStatMsgVO.setUpMsgCount(userCountMap.get(userIdStr));
								deptStatMsgVO.setUpMsgUserCount(1);
								topDeptMsg.put(departmentDto.getDepartmentId(), deptStatMsgVO);
								deptStatList.add(deptStatMsgVO);
							} else {
								deptStatMsgVO.setUpMsgCount(deptStatMsgVO.getUpMsgCount() + userCountMap.get(userIdStr));
								deptStatMsgVO.setUpMsgUserCount(deptStatMsgVO.getUpMsgUserCount() + 1);
							}
						}
					}
				}
			}
			
			statInternalAppMsgVO.setCustomerReplyMsgCount(customerReplyMsgCount);
			statInternalAppMsgVO.setUpMsgCount(upMsgCount);
			statInternalAppMsgVO.setUpMsgUserCount(userCountMap.size());
			statInternalAppMsgVO.setDeptStatMsgs(deptStatList);
			
	    	return new MessageExhibitionResult(statInternalAppMsgVO);
		} catch (Exception e) {
			logger.error("statInternalAppMsg startTime:{} endTime:{} appId:{} ea:{} needDetail:{} error:", startTime, endTime, appId, ea, needDetail, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
    }
    
	@Override
    public MessageExhibitionResult<StatCrossAppMsgVO> statCrossAppMsg(Long startTime, Long endTime, String appId, String ea, Boolean needDetail) {
		StatCrossAppMsgVO statCrossAppMsgVO = new StatCrossAppMsgVO();
		
		List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
				MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY,
				MessageSendTypeEnum.CROSS_UPLINK_MESSAGE_NORMAL);
		try {
			List<OpenMsgDO> msgList = openMsgDAO.queryDialogMessages(ea, appId, startTime, endTime, typeEnumList, true);
			Integer upMsgCount = 0;
			Integer customerReplyMsgCount = 0;
			List<EnterpriseStatMsgVO> enterpriseStatList = Lists.newArrayList();
			
			Set<String> userCountSet = Sets.newHashSet();
			Map<String, Integer> eaMsgCount = Maps.newLinkedHashMap();
			Map<String, Integer> eaUserCount = Maps.newHashMap();
			
			if (msgList != null) {
				for (OpenMsgDO msg : msgList) {
					if (msg.getSendType() > 0) {
						customerReplyMsgCount++;
						continue;
					}
					upMsgCount++;
					if (eaMsgCount.get(msg.getEnterpriseAccount()) == null) {
						eaMsgCount.put(msg.getEnterpriseAccount(), 1);
					} else {
						Integer sendNum = eaMsgCount.get(msg
								.getEnterpriseAccount());
						
						eaMsgCount.put(msg.getEnterpriseAccount(), ++sendNum);
					}

					if (eaUserCount.get(msg.getEnterpriseAccount()) == null) {
						eaUserCount.put(msg.getEnterpriseAccount(), 1);
					} else {
						if (!userCountSet.contains(msg.getSessionId())) {
							Integer sendNum = eaUserCount.get(msg
									.getEnterpriseAccount());
							
							eaUserCount.put(msg.getEnterpriseAccount(), ++sendNum);
						}
					}

					if (!userCountSet.contains(msg.getSessionId())) {
						userCountSet.add(msg.getSessionId());
					}
				}
				if (needDetail && !eaMsgCount.isEmpty()) {
					for (String downEa : eaMsgCount.keySet()) {
						EnterpriseStatMsgVO enterpriseStatMsgVO = new EnterpriseStatMsgVO();
						enterpriseStatMsgVO.setUpMsgCount(eaMsgCount.get(downEa));
						enterpriseStatMsgVO.setUpMsgUserCount(eaUserCount.get(downEa));
						
			            GetEnterpriseConfigByEA.Args arg = new GetEnterpriseConfigByEA.Args();

			            arg.EnterpriseAccount = downEa;
			            GetEnterpriseConfigByEA.Result eIResult = globalConfigService.getEnterpriseConfigByEA(arg);
			            
			            enterpriseStatMsgVO.setEnterpriseName(eIResult.EnterpriseConfig.EnterpriseName);
			            enterpriseStatMsgVO.setEnterpriseId(eIResult.EnterpriseConfig.EnterpriseID);
						
						enterpriseStatList.add(enterpriseStatMsgVO);
					}
				}
			}
			
			statCrossAppMsgVO.setCustomerReplyMsgCount(customerReplyMsgCount);
			statCrossAppMsgVO.setUpMsgCount(upMsgCount);
			statCrossAppMsgVO.setUpMsgUserCount(userCountSet.size());
			statCrossAppMsgVO.setEnterpriseStatMsgs(enterpriseStatList);
			
	    	return new MessageExhibitionResult(statCrossAppMsgVO);
		} catch (Exception e) {
			logger.error("statCrossAppMsg startTime:{} endTime:{} appId:{} ea:{} needDetail:{} error:", startTime, endTime, appId, ea, needDetail, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
    }
	
	@Override
	public MessageExhibitionResult<StatCustomerMsgVO> statCustomerMsg(Long startTime, Long endTime, String appId, String ea) {
		StatCustomerMsgVO statCustomerMsgVO = new StatCustomerMsgVO();
		Integer totalReplyMsgCount = 0;
		List<ReplyStatMsgVO> replyCustomerStat = Lists.newArrayList();
		
		try {
			CustomerSessionResult<CustomerSessionInfoVO> customerSessionInfo = msgCustomerService.getCustomerSessionInfo(appId, ea);
			List<String> customerList = customerSessionInfo.getData().getCustomerList();
			
			if (customerList != null && customerList.size() > 0) {
				List<OpenMsgDO> msgList = Lists.newArrayList();
				Map<String, Integer> adminReplyMap = Maps.newHashMap();
				
				AppResult appResult = appService.getAppInfo(null, null, appId);
				
				List<MessageSendTypeEnum> typeEnumList = Lists.newArrayList(
						MessageSendTypeEnum.ADMINISTRATOR_REPLY,
						MessageSendTypeEnum.CROSS_ADMINISTRATOR_REPLY);
				
				if (appResult.getAppType() == AppTypeEnum.EA_CONNECT_CUSTOMER_SERVICE.getValue()) {
					msgList = openMsgDAO.queryDialogMessages(ea, appId, startTime, endTime, typeEnumList, true);
				} else {
					msgList = openMsgDAO.queryDialogMessages(ea, appId, startTime, endTime, typeEnumList, false);
				}
				
				if (msgList != null && msgList.size() > 0) {
					totalReplyMsgCount = msgList.size();
					for (OpenMsgDO msgDO : msgList) {					
						if (adminReplyMap.get(msgDO.getAdminUserId()) == null) {
							adminReplyMap.put(msgDO.getAdminUserId(), 1);
						} else {
							Integer sendNum = adminReplyMap.get(msgDO
									.getAdminUserId());
							adminReplyMap.put(msgDO.getAdminUserId(), ++sendNum);
						}
					}
				}
				List<CustomerEvaluateDO> customerEvalList = customerEvaluateDAO.getCustomerEvaluatesByTime(appId, startTime, endTime);
				Map<Integer, CustomerScoreVO> customerScoreMap = Maps.newHashMap(); 
				
				if (customerEvalList != null && customerEvalList.size() > 0) {
					for (CustomerEvaluateDO customerEval : customerEvalList) {
						if (customerScoreMap.get(customerEval.getCustomerId()) == null) {
							CustomerScoreVO customerScoreVO = new CustomerScoreVO();
							customerScoreVO.setEvalSendNum(1);
							if (customerEval.getEvaluateStatus() == 2) {
								customerScoreVO.setEvalReplyNum(1);
							}
							customerScoreVO.setTotalScore(customerEval.getScore() == null ? 0 : customerEval.getScore());
							customerScoreMap.put(customerEval.getCustomerId(), customerScoreVO);
						} else {
							CustomerScoreVO customerScoreVO = customerScoreMap.get(customerEval.getCustomerId());
							customerScoreVO.setEvalSendNum(customerScoreVO.getEvalSendNum() + 1);
							if (customerEval.getEvaluateStatus() == 2) {
								customerScoreVO.setEvalReplyNum(customerScoreVO.getEvalReplyNum() + 1);
							}
							customerScoreVO.setTotalScore(customerScoreVO.getTotalScore() + (customerEval.getScore() == null ? 0 : customerEval.getScore()));
						}
					}
				}
				//通讯录人员信息获取
				List<Integer> customerEmpIds = customerList.stream().map(customer -> {
					return Integer.parseInt(customer);
				}).collect(Collectors.toList());
				List<EmployeeDto> employList = this.getEmployeeDtoList(ea, customerEmpIds);
				//组装客服回复信息
				if (CollectionUtils.isNotEmpty(employList)) {
					Map<Integer, EmployeeDto> employeeMap = Maps.newHashMap();
					employList.forEach(employeeInfo -> {
						employeeMap.put(employeeInfo.getEmployeeId(), employeeInfo);
					});
					for(Integer employeeId : customerEmpIds) {
						EmployeeDto employee = employeeMap.get(employeeId);
						if (employee == null) continue;
						ReplyStatMsgVO replyStatMsgVO = new ReplyStatMsgVO();
						replyStatMsgVO.setEmployeeId(employeeId);
						replyStatMsgVO.setEmployeeName(employee.getName());
						Integer adminReplyNum = adminReplyMap.get("E." + ea + "." + employeeId);
                        replyStatMsgVO.setReplyMsgCount(adminReplyNum == null ? 0 : adminReplyNum);
                        //获取主属部门信息
                        DepartmentDto departmentInfo = this.getDepartment(ea, this.getMainDepId(employee));
                        if (Objects.nonNull(departmentInfo)) {
                            replyStatMsgVO.setMainDeptName(departmentInfo.getName());
                        }

                        CustomerScoreVO customerScoreVO = customerScoreMap.get(employeeId);
						if (customerScoreVO != null) {
							replyStatMsgVO.setEvalTotalScore(customerScoreVO.getTotalScore());
							replyStatMsgVO.setEvalSendNum(customerScoreVO.getEvalSendNum());
							replyStatMsgVO.setEvalReplyNum(customerScoreVO.getEvalReplyNum());
						} else {
							replyStatMsgVO.setEvalTotalScore(0);
							replyStatMsgVO.setEvalSendNum(0);
							replyStatMsgVO.setEvalReplyNum(0);
						}

						replyCustomerStat.add(replyStatMsgVO);
					}
				}
	 		}
			
			statCustomerMsgVO.setTotalReplyMsgCount(totalReplyMsgCount);
			statCustomerMsgVO.setReplyStatMsgs(replyCustomerStat);
			return new MessageExhibitionResult(statCustomerMsgVO);
		} catch (Exception e) {
			logger.error("statCustomerMsg startTime:{} endTime:{} appId:{} ea:{} error:", startTime, endTime, appId, ea, e);
			return new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}
	}
	
	private AppNameLogoVO getAppNameLogoVO(String appId) {
		AppNameLogoVO appNameLogo = null;

		List<String> appIds = Lists.newArrayList(appId);
		BaseResult<List<AppNameLogoVO>> appNameLogoResult = openAppNameService.queryAppNameLogoByAppIds(appIds);

		if (appNameLogoResult.isSuccess()) {
			List<AppNameLogoVO> appNameLogoVOs = appNameLogoResult.getResult();
			if (!appNameLogoVOs.isEmpty()) {
				appNameLogo = appNameLogoVOs.get(0);
			}
		}

		return appNameLogo;
	}

	private EmployeeDto getEmployee(String ea, String userId) {

		EmployeeDto employeeDto = null;
		Integer currentEmployeeId = Integer.valueOf(userId);
		List<Integer> employeeIds = Lists.newArrayList(currentEmployeeId);

		List<EmployeeDto> employeeDtoList = this.getEmployeeDtoList(ea, employeeIds);
		if (!employeeDtoList.isEmpty()) {
			employeeDto = employeeDtoList.get(0);
		}

		return employeeDto;
	}

	private String getEnterpriseShortName(String upstreamEa, String ea) {
		if (StringUtils.isEmpty(upstreamEa) || StringUtils.isEmpty(ea)) {
			return "";
		}
		// 从互联平台rest接口获取
		logger.info("getEnterpriseShortName start. onCloud:{}, upstreamEa:{}, ea:{}", true, upstreamEa, ea);
		GetEnterpriseShortNameArg arg = new GetEnterpriseShortNameArg();
		GetOuterTenantIdByEaArg tenantIdByEaArg = new GetOuterTenantIdByEaArg();

		tenantIdByEaArg.setEa(upstreamEa);
		arg.setSourceOuterTenantId(fxiaokeAccountService.getOuterTenantIdByEa(HeaderObj.newInstance(null), tenantIdByEaArg).getData());

		tenantIdByEaArg.setEa(ea);
		arg.setDestOuterTenantId(fxiaokeAccountService.getOuterTenantIdByEa(HeaderObj.newInstance(null), tenantIdByEaArg).getData());
		int enterpriseId = getEnterpriseId(upstreamEa);
		HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
		RestResult<String> restResult = enterpriseRelationService.getEnterpriseShortName(headerObj, arg);

		logger.info("getEnterpriseShortName from cloud success, upstreamEa:{}, ea:{}, restResult:{}", upstreamEa, ea, restResult);
		if (!restResult.isSuccess()) {
			logger.error("getEnterpriseShortName failed, upstreamEa:{}, ea:{}, restResult:{}", upstreamEa, ea, restResult);
			return StringUtils.EMPTY;
		}
		return restResult.getData();
	}

	private UserSessionVO openMsgDO2UserSessionVO(final OpenMsgDO openMsg, Map<String, EmployeeDto> employeeMap,
                                                  String eaName) {
		UserSessionVO vo = new UserSessionVO();

		String ea = openMsg.getEnterpriseAccount();
		String lastContentType = openMsg.getMsgType();
		String lastContentSummary = openMsg.getContent();

		String userId = StringUtils.EMPTY;
		String appId = StringUtils.EMPTY;
		int processStatus = openMsg.getProcessStatus();
		int sendType = openMsg.getSendType();
		// 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
		if (sendType > 0) {
			userId = openMsg.getReceiver();
			appId = openMsg.getSender();
		} else {
			userId = openMsg.getSender();
			appId = openMsg.getReceiver();
		}

		if (!MessageType.TEXT.equalsIgnoreCase(openMsg.getMsgType())) {
			lastContentSummary = kvs.getOrDefault(lastContentType.toUpperCase(), UNSUPPORTED_MESSAGE);
		}

		vo.setAppId(appId);
		vo.setSessionId(openMsg.getSessionId());
		vo.setFullFsUserId(Joiner.on(".").join("E", ea, userId));

		vo.setUserStatus(2);
		String userName = StringUtils.EMPTY;
		// TODO: 需优化 将会修改为批量获取 员工信息
		// 改用employeeService.getAdminIds(enterpriseAccount) 获取
		EmployeeDto employee = employeeMap.get(Joiner.on("-").join(Integer.parseInt(userId), ea));
		if (employee != null) {
			userName = employee.getName();
			vo.setProfileImage(employee.getProfileImage());
			vo.setUserStatus(Objects.equals(employee.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
		}

		vo.setUserName(userName);
		vo.setLastMessageId(openMsg.getMsgId());
		vo.setLastContentSummary(lastContentSummary);
		vo.setLastContentType(lastContentType);
		vo.setStatus(processStatus);
		vo.setDatetime(openMsg.getMessageTime());

		vo.setEa(ea);
		if (!StringUtils.isEmpty(openMsg.getUpEnterpriseAccount())) {
			vo.setEaName(eaName);
		}

		return vo;
	}

	private SingleUserSessionMessagesVO openMsgDO2SingleUserSessionMessagesVO(final OpenMsgDO openMsg, final AppNameLogoVO appNameLogo, final EmployeeDto employee, final Map<Integer, EmployeeDto> adminUserMap, final String enerpriseShortName) {
		SingleUserSessionMessagesVO vo = new SingleUserSessionMessagesVO();

		String senderId = StringUtils.EMPTY;
		String senderName = StringUtils.EMPTY;
		String profileImageUrl = StringUtils.EMPTY;
		// 星标导出接收人
		String receiveUserName = StringUtils.EMPTY;
		int sendType = openMsg.getSendType();
		boolean isUplink = false;

		// 回复的管理员
		EmployeeDto adminUser;
		String adminUserName = StringUtils.EMPTY;
		;

		// 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
		if (sendType > 0) {
			senderId = openMsg.getSender();

			if (appNameLogo != null) {
				senderName = appNameLogo.getAppName();
				profileImageUrl = appNameLogo.getLogoAddress();
			}
			// 星标导出接收人
			if (employee != null) {
				receiveUserName = employee.getName();
			}

			// 回复的管理员信息
			if (openMsg.getAdminUserId() != null) {
				adminUser = adminUserMap.get(FsUserVO.getUserId(openMsg.getAdminUserId()));
				if (adminUser != null) {
					adminUserName = adminUser.getName();
					vo.setAdminUserState(Objects.equals(adminUser.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
				}
			}
		} else {
			isUplink = true;
			senderId = Joiner.on(".").join("E", openMsg.getEnterpriseAccount(), openMsg.getSender());

			if (employee != null) {
				senderName = employee.getName();
				profileImageUrl = employee.getProfileImage();
				vo.setSenderState(Objects.equals(employee.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
			}
		}

		vo.setMessageId(openMsg.getMsgId());
		vo.setSenderId(senderId);
		vo.setSenderName(senderName);
		vo.setSendType(sendType);
		vo.setProfileImageUrl(profileImageUrl);
		vo.setContent(UrlEscapers.urlFragmentEscaper().escape(openMsg.getContent()));
		vo.setContentType(openMsg.getMsgType());
		vo.setUplink(isUplink);
		vo.setDatetime(openMsg.getMessageTime());
		vo.setAdminUserId(openMsg.getAdminUserId());
		vo.setAdminUserName(adminUserName);

		// 星标导出新加
		vo.setStartMark(openMsg.getStarMark());
		vo.setSessionId(openMsg.getSessionId());
		vo.setReceiveUserName(receiveUserName);

		vo.setEaName(enerpriseShortName);

		return vo;
	}

	private StartMessagesVO openMsgDO2StartMessageVO(final OpenMsgDO openMsg, final AppNameLogoVO appNameLogo, final Map<Integer, EmployeeDto> receiverEmployeeMap, final String receiverEaShortName, final Map<Integer, EmployeeDto> senderEmployeeMap) {
		StartMessagesVO vo = new StartMessagesVO();

		String senderId = StringUtils.EMPTY;
		String userId = StringUtils.EMPTY;

		String senderName = StringUtils.EMPTY;
		String profileImageUrl = StringUtils.EMPTY;

		// 星标导出接收人
		String receiveUserName = StringUtils.EMPTY;

		String adminUserName = StringUtils.EMPTY;

		int sendType = openMsg.getSendType();
		boolean isUplink = false;

		// 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
		if (sendType > 0) {
			senderId = openMsg.getSender();

			userId = openMsg.getReceiver();

			if (appNameLogo != null) {
				senderName = appNameLogo.getAppName();
				profileImageUrl = appNameLogo.getLogoAddress();
			}

			EmployeeDto employee = receiverEmployeeMap.get(Integer.parseInt(userId));
			if (employee != null) {
				receiveUserName = employee.getName();
				vo.setReceiveUserState(Objects.equals(employee.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
			}

		} else {
			isUplink = true;
			senderId = Joiner.on(".").join("E", openMsg.getEnterpriseAccount(), openMsg.getSender());

			userId = openMsg.getSender();

			EmployeeDto employee = receiverEmployeeMap.get(Integer.parseInt(userId));
			if (employee != null) {
				senderName = employee.getName();
				profileImageUrl = employee.getProfileImage();
				vo.setSenderState(Objects.equals(employee.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
			}

			if (appNameLogo != null) {
				receiveUserName = appNameLogo.getAppName();
			}
		}

		// TODO: 需优化 将会修改为批量获取 员工信息
		// 改用employeeService.getAdminIds(enterpriseAccount) 获取

		vo.setMessageId(openMsg.getMsgId());
		vo.setSenderId(senderId);
		vo.setSenderName(senderName);
		vo.setProfileImageUrl(profileImageUrl);
		vo.setContent(UrlEscapers.urlFragmentEscaper().escape(openMsg.getContent()));
		vo.setContentType(openMsg.getMsgType());
		vo.setUplink(isUplink);
		vo.setDatetime(openMsg.getMessageTime());
		vo.setSendType(openMsg.getSendType());

		vo.setAdminReply(sendType == 4 ? true : false);

		if (!Objects.isNull(senderEmployeeMap) && !Strings.isNullOrEmpty(openMsg.getAdminUserId())) {
			String adminId = Splitter.on(".").splitToList(openMsg.getAdminUserId()).get(2);

			EmployeeDto employee = senderEmployeeMap.get(Integer.parseInt(adminId));
			if (employee != null) {
				adminUserName = employee.getName();
				vo.setAdminUserState(Objects.equals(employee.getStatus().getValue(), EmployeeEntityStatus.NORMAL.getValue()) ? 1 : 2);
			}
		}

		vo.setAdminUserId(openMsg.getAdminUserId());
		vo.setAdminUserName(adminUserName);
		vo.setStartMark(openMsg.getStarMark());
		vo.setSessionId(openMsg.getSessionId());
		vo.setReceiveUserName(receiveUserName);
		vo.setEa(openMsg.getEnterpriseAccount());
		if (StringUtils.isNotEmpty(receiverEaShortName)) {
			vo.setEaName(receiverEaShortName);
		}

		return vo;
	}

	private ExportMsgVO openMsgDO2ExportMsgVO(final OpenMsgDO openMsg, final AppNameLogoVO appNameLogo, final Map<Integer, EmployeeDto> employeeMap, Map<Integer, EmployeeDto> adminUserMap, final String eaShortName) {
		ExportMsgVO vo = new ExportMsgVO();

		String userId = StringUtils.EMPTY;

		String senderName = StringUtils.EMPTY;
		// 星标导出接收人
		String receiveUserName = StringUtils.EMPTY;
		String adminUserName = StringUtils.EMPTY;
		String sessionUserName = StringUtils.EMPTY;

		int sendType = openMsg.getSendType();
		boolean isUplink = false;

		// 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
		if (sendType > 0) {// 下行

			userId = openMsg.getReceiver();

			if (appNameLogo != null) {
				senderName = appNameLogo.getAppName();
			}

			EmployeeDto employee = employeeMap.get(Integer.parseInt(userId));
			if (employee != null) {
				receiveUserName = employee.getName();
				sessionUserName = employee.getName();
			}

		} else {// 上行
			isUplink = true;

			userId = openMsg.getSender();

			EmployeeDto employee = employeeMap.get(Integer.parseInt(userId));
			if (employee != null) {
				senderName = employee.getName();
				sessionUserName = employee.getName();
			}

			if (appNameLogo != null) {
				receiveUserName = appNameLogo.getAppName();
			}
		}

		vo.setUplink(isUplink);
		vo.setMsgId(openMsg.getMsgId());
		vo.setMsgType(openMsg.getMsgType());
		vo.setSendType(sendType);
		vo.setStarMark(openMsg.getStarMark());
		vo.setSenderName(senderName);
		vo.setReceiverName(receiveUserName);
		vo.setSendTypeName(typeDesc.getOrDefault(sendType, ""));

		String lastContentSummary = openMsg.getContent();
		if (!MessageType.TEXT.equalsIgnoreCase(openMsg.getMsgType())) {
			lastContentSummary = kvs.getOrDefault(openMsg.getMsgType().toUpperCase(), UNSUPPORTED_MESSAGE);
		}
		vo.setContent(lastContentSummary);

		vo.setCreateTime(openMsg.getMessageTime());
		vo.setSessionId(openMsg.getSessionId());

		if (!Strings.isNullOrEmpty(openMsg.getAdminUserId())) {
			EmployeeDto adminUser = null;
			if (!Objects.isNull(adminUserMap)) {
				adminUser = adminUserMap.get(FsUserVO.getUserId(openMsg.getAdminUserId()));
			} else if (!Objects.isNull(employeeMap)) {
				adminUser = employeeMap.get(FsUserVO.getUserId(openMsg.getAdminUserId()));
			}
			if (adminUser != null) {
				adminUserName = adminUser.getName();
			}
		}

		vo.setAdminUserName(adminUserName);
		vo.setSessionUserName(sessionUserName);

		vo.setEa(openMsg.getEnterpriseAccount());
		if (!StringUtils.isEmpty(eaShortName)) {
			vo.setEaName(eaShortName);
		}

		return vo;
	}

	/**
	 * 加载员工映射关系
	 * 
	 * @param openMsgList
	 * @param ea
	 * @return
	 */
	private Map<Integer, EmployeeDto> loadEmployeeMap(List<OpenMsgDO> openMsgList, String ea) {

		Map<Integer, EmployeeDto> employeeMap = new HashMap<Integer, EmployeeDto>();

		if (CollectionUtils.isNotEmpty(openMsgList)) {
			List<Integer> employeeIdList = new ArrayList<Integer>();

			openMsgList.stream().forEach(openMsgDO -> {
				int sendType = openMsgDO.getSendType();

				String userId = StringUtils.EMPTY;
				// 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
				if (sendType > 0) {
					userId = openMsgDO.getReceiver();
				} else {
					userId = openMsgDO.getSender();
				}

				if (!employeeIdList.contains(Integer.parseInt(userId))) {
					employeeIdList.add(Integer.parseInt(userId));
				}

				if (!Strings.isNullOrEmpty(openMsgDO.getAdminUserId())) {
					List<String> nameList = Splitter.on(".").splitToList(openMsgDO.getAdminUserId());

					if (nameList != null && nameList.size() == 3) {
						if (!employeeIdList.contains(Integer.parseInt(nameList.get(2)))) {
							employeeIdList.add(Integer.parseInt(nameList.get(2)));
						}

					}
				}
			});
			employeeMap = this.loadEmployeeMapByIds(employeeIdList, ea);
		}

		return employeeMap;
	}

	@Override
	public MessageExhibitionResult<Map<String, Long>> countSendNumBySender(String appId, String ea, List<String> senders) {
		MessageExhibitionResult<Map<String, Long>> result = null;
		Map<String, Long> map = openMsgDAO.countSendNumBySender(appId, ea, senders);
		result = new MessageExhibitionResult<>(map);
		return result;
	}

	@Override
	public MessageExhibitionResult<Long> getLastUpMsgTime(String appId, String ea, String sender) {
		MessageExhibitionResult<Long> result = null;
		Map<String, Long> map = openMsgDAO.getLastUpMsgTime(appId, ea, sender);
		if (map.isEmpty()) {
			result = new MessageExhibitionResult<>(MsgCodeEnum.PARAM_OPENID_NOT_EXIST);
		} else {
			result = new MessageExhibitionResult<>(map.get(sender));
		}
		return result;
	}

	@Override
	public MessageExhibitionResult<List<String>> getAppIdList(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList) {

		MessageExhibitionResult<List<String>> result = null;

		try {
			List<String> appIDList = openMsgDAO.getAppIdList(startTime, endTime, sendTypeEnumList);

			result = new MessageExhibitionResult<>(appIDList);
		} catch (Exception e) {
			logger.error("countAppId startTime:{} , endTime:{}, sendTypeEnum:{}, error: {}", startTime, endTime, sendTypeEnumList, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	@Override
	public MessageExhibitionResult<Long> countReplyMsgNum(Long startTime, Long endTime, List<MessageSendTypeEnum> sendTypeEnumList) {
		MessageExhibitionResult<Long> result;

		try {
			long msgNum = openMsgDAO.countMsgNum(startTime, endTime, sendTypeEnumList);

			result = new MessageExhibitionResult<>(msgNum);
		} catch (Exception e) {
			logger.error("countMsgNum startTime:{} , endTime:{}, sendTypeEnum:{}, error: {}", startTime, endTime, sendTypeEnumList, e);
			result = new MessageExhibitionResult<>(MsgCodeEnum.SYSTEM_ERROR);
		}

		return result;
	}

	private Map<String, List<OpenMsgDO>> openMsgGroupByEa(List<OpenMsgDO> openMsgList) {
		Map<String, List<OpenMsgDO>> eaGroupMap = new HashMap<>();
		if (Objects.isNull(openMsgList)) {
			return eaGroupMap;
		}
		for (OpenMsgDO openMsgDO : openMsgList) {
			if (null != eaGroupMap.get(openMsgDO.getEnterpriseAccount())) {
				eaGroupMap.get(openMsgDO.getEnterpriseAccount()).add(openMsgDO);
			} else {
				eaGroupMap.put(openMsgDO.getEnterpriseAccount(), Lists.newArrayList(openMsgDO));
			}
		}
		return eaGroupMap;
	}

	private List<Integer> openMsgAdminUserIds(List<OpenMsgDO> openMsgList) {
		List<Integer> adminUserIds = Lists.newArrayList();
		if (Objects.isNull(openMsgList)) {
			return adminUserIds;
		}
		for (OpenMsgDO openMsgDO : openMsgList) {
			if (StringUtils.isEmpty(openMsgDO.getAdminUserId())) {
				continue;
			}
			List<String> nameList = Splitter.on(".").splitToList(openMsgDO.getAdminUserId());
			if (nameList.size() == 3 && !adminUserIds.contains(nameList.get(2))) {
				adminUserIds.add(Integer.parseInt(nameList.get(2)));
			}
		}
		return adminUserIds;
	}

	/**
	 * 加载员工映射关系
	 * 
	 * @param openMsgList
	 * @param ea
	 * @return
	 */
	private Map<Integer, EmployeeDto> loadEmployeeMapByIds(List<Integer> employeeIds, String ea) {

		Map<Integer, EmployeeDto> employeeMap = Maps.newHashMap();

		if (CollectionUtils.isNotEmpty(employeeIds) && StringUtils.isNotEmpty(ea)){
			List<EmployeeDto> employeeDtoList = this.getEmployeeDtoList(ea, employeeIds);
			if (!employeeDtoList.isEmpty()) {
				employeeDtoList.forEach(employeeDto -> {
					employeeMap.put(employeeDto.getEmployeeId(), employeeDto);
				});
			}
		}
		return employeeMap;
	}

    /**
     * 加载员工映射关系
     *
     * @param openMsgList
     * @param ea
     * @return Map(userId + ea, employee)
     */
    private Map<String, EmployeeDto> loadEmployeeMsgMap(List<OpenMsgDO> openMsgList, String ea) {

        Map<String, EmployeeDto> employeeMap = new HashMap<String, EmployeeDto>();

        if (CollectionUtils.isNotEmpty(openMsgList)) {
            List<Integer> employeeIdList = new ArrayList<Integer>();

            openMsgList.stream().forEach(openMsgDO -> {
                int sendType = openMsgDO.getSendType();

                String userId = StringUtils.EMPTY;
                // 通过sendType区分上行与下行消息 @see MessageSendTypeEnum
                if (sendType > 0) {
                    userId = openMsgDO.getReceiver();
                } else {
                    userId = openMsgDO.getSender();
                }

                if (!employeeIdList.contains(Integer.parseInt(userId))) {
                    employeeIdList.add(Integer.parseInt(userId));
                }

                if (!Strings.isNullOrEmpty(openMsgDO.getAdminUserId())) {
                    List<String> nameList = Splitter.on(".").splitToList(openMsgDO.getAdminUserId());

                    if (nameList != null && nameList.size() == 3) {
                        if (!employeeIdList.contains(Integer.parseInt(nameList.get(2)))) {
                            employeeIdList.add(Integer.parseInt(nameList.get(2)));
                        }

                    }
                }
            });
            employeeMap = this.loadEmployeesMap(employeeIdList, ea);
        }

        return employeeMap;
    }

    /**
     * 加载员工映射关系
     *
     * @param openMsgList
     * @param ea
     * @return Map(userId + ea, EmployeeDto)
     */
    private Map<String, EmployeeDto> loadEmployeesMap(List<Integer> employeeIds, String ea) {

        Map<String, EmployeeDto> employeeMap = new HashMap<String, EmployeeDto>();

        if (CollectionUtils.isNotEmpty(employeeIds) && StringUtils.isNotEmpty(ea)) {
			List<EmployeeDto> employeeListResult = this.getEmployeeDtoList(ea, employeeIds);

			if (!employeeListResult.isEmpty()) {
				employeeListResult.stream().forEach(employee -> {
					employeeMap.put(Joiner.on("-").join(employee.getEmployeeId(), ea), employee);
				});
			}
        }
        return employeeMap;
    }

	/**
	 * 获取员工详情
	 *
	 * @param ea          企业ea
	 * @param employeeIds 员工ID List<Integer>
	 * @return
	 */
	private List<EmployeeDto> getEmployeeDtoList(String ea, List<Integer> employeeIds) {
		BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
		batchGetEmployeeDtoArg.setEnterpriseId(this.getEnterpriseId(ea));
		batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
		batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

		List<EmployeeDto> employeeDtos = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg).getEmployeeDtos();
		if (CollectionUtils.isEmpty(employeeDtos)) {
			return Lists.newArrayList();
		}
		// 将图片名称换为图片路径
		employeeDtos.stream().forEach(employeeDto -> {
			if (StringUtils.isNotEmpty(employeeDto.getProfileImage())) {
				String imagePath = "https://a" + (byte) (employeeDto.getProfileImage().charAt(employeeDto.getProfileImage().length() - 1)) % 7
						+ EMPLOYEE_PROFILE_IMAGE_CDN_PATH + employeeDto.getProfileImage() + ".jpg&ea=" + ea;
				employeeDto.setProfileImage(imagePath);
			}
		});
		return employeeDtos;
	}

	/**
	 * 根据部门ID获取部门信息
	 *
	 * @param ea           企业ea
	 * @param departmentId 部门ID
	 * @return
	 */
	private DepartmentDto getDepartment(String ea, int departmentId) {
		GetDepartmentDtoArg getDepartmentDtoArg = new GetDepartmentDtoArg();
		getDepartmentDtoArg.setEnterpriseId(this.getEnterpriseId(ea));
		getDepartmentDtoArg.setDepartmentId(departmentId);

		GetDepartmentDtoResult departmentDto = departmentProviderService.getDepartmentDto(getDepartmentDtoArg);
		if (Objects.nonNull(departmentDto)) {
			return departmentDto.getDepartment();
		}
		return null;
	}

    /**
     * 获取主属部门
     *
     * @param employeeDto
     * @return
     */
    public int getMainDepId(EmployeeDto employeeDto) {
        List<Integer> mainDepartmentIds = employeeDto.getMainDepartmentIds();
        return CollectionUtils.isEmpty(mainDepartmentIds) ? 0 : mainDepartmentIds.get(0);
    }

	/**
	 * ea转ei
	 *
	 * @param ea 企业ea
	 * @return
	 */
	private int getEnterpriseId(String ea) {
		try {
			return eIEAConverter.enterpriseAccountToId(ea);
		} catch (Exception e) {
			throw new IllegalArgumentException(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION.getErrorMsg());
		}
	}
}
