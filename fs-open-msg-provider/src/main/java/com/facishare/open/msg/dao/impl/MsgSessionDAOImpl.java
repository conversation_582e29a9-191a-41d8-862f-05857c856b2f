package com.facishare.open.msg.dao.impl;

import org.mongodb.morphia.Key;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.facishare.open.msg.dao.MsgSessionDAO;
import com.facishare.open.msg.dao.base.MsgBaseDAO;
import com.facishare.open.msg.model.MsgSessionDO;

/**
 * 存储session操作记录
 * <AUTHOR>
 * @date 2015/8/28 14:34
 */
@Component
public class MsgSessionDAOImpl extends MsgBaseDAO<MsgSessionDO> implements MsgSessionDAO {

    private static final Logger logger = LoggerFactory.getLogger(MsgSessionDAOImpl.class);

    @Override
    public boolean addMsgSession(MsgSessionDO msgSession) {
        Key<MsgSessionDO> key = getDatastore().save(msgSession);

        return key.getId() == null ? false : true;
    }

    @Override
    protected Class<MsgSessionDO> getEntityClazz() {
        return MsgSessionDO.class;
    }
}

