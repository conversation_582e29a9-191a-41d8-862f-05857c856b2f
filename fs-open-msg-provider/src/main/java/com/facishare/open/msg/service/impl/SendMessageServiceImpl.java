package com.facishare.open.msg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.result.TemplateResult;
import com.facishare.open.app.center.api.service.template.TemplateService;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constants.MsgOperConstants;
import com.facishare.open.msg.dao.WechatGroupIDMappingDAO;
import com.facishare.open.msg.manager.MessageRecordManager;
import com.facishare.open.msg.model.MessageListVO;
import com.facishare.open.msg.model.MessagePbVO;
import com.facishare.open.msg.model.MessageVO;
import com.facishare.open.msg.model.OpenMsgDO;
import com.facishare.open.msg.model.RevokeMsgVO;
import com.facishare.open.msg.model.SendMsgToEaConnServiceVO;
import com.facishare.open.msg.model.SendOfficeMessageVO;
import com.facishare.open.msg.model.SendTemplateMessageVO;
import com.facishare.open.msg.model.SendTextMessageVO;
import com.facishare.open.msg.model.SendWechatPromptTextMessageArg;
import com.facishare.open.msg.model.WechatBaichuanNotifyMessageVO;
import com.facishare.open.msg.model.WechatCreateSessionVO;
import com.facishare.open.msg.model.WechatGroupIDMappingDO;
import com.facishare.open.msg.model.WechatGroupMessageVO;
import com.facishare.open.msg.model.WechatMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.result.MsgBaseResult;
import com.facishare.open.msg.result.MsgCodeEnum;
import com.facishare.open.msg.result.WechatGroupResult;
import com.facishare.open.msg.service.MsgGroupService;
import com.facishare.open.msg.service.SendMessageService;
import com.facishare.open.msg.util.DBDataRuleUtil;
import com.facishare.open.msg.util.FengYunUtil;
import com.facishare.open.msg.util.OfficeMsgUtil;
import com.facishare.open.msg.util.TemplateUtils;
import com.facishare.open.msg.util.UidUtil;
import com.facishare.open.oauth.model.enums.OauthCodeEnum;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.service.EaAuthService;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.constant.OpenSourceType;
import com.facishare.qixin.api.constant.SessionType;
import com.facishare.qixin.api.model.EmployeeId;
import com.facishare.qixin.api.model.open.CustomerMembersTO;
import com.facishare.qixin.api.model.open.OpenMessageSimpleItem;
import com.facishare.qixin.api.model.open.WorkbenchType;
import com.facishare.qixin.api.model.open.arg.OpenRevokeMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenSendOuterUserMessageArg;
import com.facishare.qixin.api.model.open.arg.OpenSendOuterUserSystemMessageArg;
import com.facishare.qixin.api.model.open.result.OpenRevokeMessageResult;
import com.facishare.qixin.api.open.OpenCrossMessageService;
import com.facishare.qixin.api.open.OpenCustomService;
import com.facishare.qixin.api.open.OpenMessageService;
import com.facishare.qixin.api.weixin.WeixinMessageService;
import com.facishare.qixin.api.weixin.model.arg.WXSendMessageArg;
import com.facishare.qixin.api.weixin.model.arg.WXSendWBMessageArg;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 发送消息
 * 
 * <AUTHOR>
 * @date 2015年8月28日 下午2:39:28
 */
@Service
public class SendMessageServiceImpl implements SendMessageService {

	public static final Logger logger = LoggerFactory.getLogger(SendMessageServiceImpl.class);

	@Resource
	private MessageRecordManager messageRecordManager;

	@Autowired
	private TemplateService templateService;


	@Autowired
	private OpenCustomService openCustomService;

	@Autowired
	private OpenMessageService openMessageService;

	// 撤回群发消息，最多重试多少次。
	@ReloadableProperty("revokeAppToCTryTimes")
	private int revokeAppToCTryTimes = 1000;

	// 撤回群发消息，重试时，每次建个多少个毫秒。
	@ReloadableProperty("revokeAppToCSleepTimeInMinisecond")
	private int revokeAppToCSleepTimeInMinisecond = 20000;

	@Resource
	private WeixinMessageService weixinMessageService;

	@Autowired
	private WechatGroupIDMappingDAO wechatGroupIDMappingDAO;

	@Autowired
	private MsgGroupService msgGroupService;

	@Autowired
	private OpenCrossMessageService openCrossMessageService;
	
	@Autowired
	private EaAuthService eaAuthService;

	@Autowired
	private MessageRecordManager MessageRecordManager;

	@Autowired
	private AsyncBatchSendOpenMessageServiceImpl asyncBatchSendOpenMessageService;


	/**
	 * 发送文本消息
	 */
	@Override
	public MessageResult sendTextMessage(SendTextMessageVO param) {
		return sendTextMessage(param, MessageSendTypeEnum.THIRD_PARTY_PUSH);
	}

	/**
	 * 发送文本消息
	 */
	@Override
	public MessageResult sendTextMessage(SendTextMessageVO param, MessageSendTypeEnum messageSendType) {
		try {
			logger.info("sendTextMessage param:{} type:{}", param, messageSendType);
			if (param == null || !param.validateParam()) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}

			return this.sendMessage(param, MessageType.TEXT, messageSendType);

		} catch (Exception e) {
			logger.error("sendTextMessage error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	/**
	 * 发送模板消息
	 * 
	 * @param param
	 *            模板消息对象
	 * @return
	 */
	@Override
	public MessageResult sendTemplateMessage(SendTemplateMessageVO param) {
		return sendTemplateMessage(param, MessageSendTypeEnum.THIRD_PARTY_PUSH);
	}

	/**
	 * 发送模板消息
	 * 
	 * @param param
	 *            模板消息对象
	 * @param messageSendType
	 *            消息发送类型
	 * @return
	 */
	@Override
	public MessageResult sendTemplateMessage(SendTemplateMessageVO param, MessageSendTypeEnum messageSendType) {
		try {
			logger.info("msg-provider sendTemplateMessage param:{} type:{}", param, messageSendType);
			// 类似文本消息 按照模板拼接后的消息
			if (param == null || !param.validateParam()) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}

			// 1 .根据appId 获取 模板
			TemplateResult templateResult = null;
			try {
				templateResult = templateService.getTemplateByParams(param.getTemplateId(),
						CommonConstant.APP_TEMPLATE_ENABLE_STATUS);
			} catch (Exception e) {
				logger.error("get template error {}", e);
				return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
			}
			if (templateResult == null || templateResult.getResult() == null) {
				return new MessageResult(MsgCodeEnum.PARAM_TEMPLATE_ID_ILLEGAL);
			}
			String template = templateResult.getResult().getTemplateContent();

			// 2 . 替换模板内容
			String templateContent = TemplateUtils.getTemplateContent(template, param.getData(), param.getUrl(),
					param.getTopColor());

			param.setContent(templateContent);

			return this.sendMessage(param, MessageType.TEMPLATE, messageSendType);

		} catch (Exception e) {
			logger.error("sendTemplateMessage error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult sendOfficeMessage(SendOfficeMessageVO param, MessageSendTypeEnum messageSendType) {
		try {
			logger.info("msg-provider sendOfficeMessage param:{} type:{}", param, messageSendType);
			// 类似文本消息 按照模板拼接后的消息
			if (param == null || !param.validateParam()) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}

			// 1 .根据appId 获取 模板
			if(StringUtils.isNotEmpty(param.getContent())){
				param.setContent(OfficeMsgUtil.ruleContent(param.getContent()));
				logger.info("msg-provider sendOfficeMessage newContent:{}", param.getContent());
			}
			return this.sendMessage(param, MessageType.TEMPLATE, messageSendType);

		} catch (Exception e) {
			logger.error("sendOfficeMessage error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}


	@Override
    public MessageResult sendImageMessage(MessageVO param, MessageSendTypeEnum messageSendType) {
		try {
			logger.info("msg-provider sendImageMessage param:{} type:{}", param, messageSendType);
			// 类似文本消息 按照模板拼接后的消息
			if (param == null || !param.validateParam()) {
				return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
			}

			return this.sendMessage(param, MessageType.IMAGE, messageSendType);

		} catch (Exception e) {
			logger.error("sendImageMessage error:{}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}
	
	private MessageResult sendMessage(MessageVO param, String type, MessageSendTypeEnum messageSendType) {
		logger.info("sendMessage MessageVO:{},type:{},messageSendType:{}", JSONObject.toJSONString(param),type,messageSendType);
		String postId = param.getPostId();
		if (StringUtils.isBlank(postId)) {
			postId = UidUtil.getUid();
			logger.info(" sendMessage param.postid is empty:{}, generate postid:{}", param, postId);
		}

		long createTime = System.currentTimeMillis();
		// 1 本地存储消息
		String enterpriseAccount = param.getEnterpriseAccount();
		String appId = param.getAppId();
        if (param.getAppId() != null && !param.getAppId().startsWith("CR:")) {
    		CommonResult statusResult = eaAuthService.isEaAuthStatusNormal(null, null, param.getEnterpriseAccount(), param.getAppId());
    		OauthCodeEnum oauthCodeEnum = statusResult.getCodeEnum();
    		if (oauthCodeEnum != null && oauthCodeEnum.getErrcode() == OauthCodeEnum.EA_APP_STOP.getErrcode()) {
    			return new MessageResult(MsgCodeEnum.SUCCESS);
    		}
        }
        
		String messageContent = param.getContent();

		// 将消息内容的存储改为 toUserList
		OpenMsgDO openMsg = new OpenMsgDO();

		openMsg.setEnterpriseAccount(enterpriseAccount);
		openMsg.setSender(appId);
		openMsg.setReceiver(Joiner.on(",").join(param.getToUserList()));
		openMsg.setMsgType(type);
		if (StringUtils.equalsIgnoreCase(type, MessageType.TEMPLATE)) {
			openMsg.setContent(StringUtils.EMPTY);
		} else {
			openMsg.setContent(messageContent);
		}
		openMsg.setCreateTime(createTime);
		openMsg.setUpdateTime(createTime);
		openMsg.setPostId(postId);
		if (param.getToUserList().size() == 1) {
			int userId = param.getToUserList().get(0);
			openMsg.setSessionId(DBDataRuleUtil.buildDBSessionId(appId, userId, null));
		} else {
			openMsg.setSessionId(StringUtils.EMPTY);
		}
		openMsg.setSendType(messageSendType.getType());
		if (messageSendType == MessageSendTypeEnum.DEFAULT_AUTO_REPLY) {
			openMsg.setProcessStatus(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);
		} else {
			openMsg.setProcessStatus(MsgOperConstants.PROCESS_STATUS_READ_REPLIED);
			if (messageSendType == MessageSendTypeEnum.ADMINISTRATOR_REPLY) {
				openMsg.setAdminUserId(param.getAdminUserId());
			}
		}

		try {
			if (!param.isLowPriority()) {
				Future<?> future = messageRecordManager.saveMsgSend(openMsg);
				future.get();				
			}

			MessagePbVO messagePbVO = new MessagePbVO();
			BeanUtils.copyProperties(messagePbVO, param);
			messagePbVO.setPostId(postId);
			messagePbVO.setQxType(type);
			messagePbVO.setMessageSendType(messageSendType);

			MessageListVO messageListVO = new MessageListVO();
			messageListVO.setMsgFlag(MsgOperConstants.MESSAGE_FLAG_NORMAL);
			messageListVO.setMsgList(Lists.newArrayList(messagePbVO));
			messageListVO.setUpstreamEa(messagePbVO.getEnterpriseAccount());
			logger.info("before send to rocketmq. messagePbVO:{}", messagePbVO);

			asyncBatchSendOpenMessageService.asyncBatchSendMessage(messageListVO);

		} catch (Exception e) {
			logger.error("Async sendMessage error {}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}

		return new MessageResult(MsgCodeEnum.SUCCESS);
	}

	private Future<?> saveWechatUserMsg(WechatMessageVO wetchatMessageVO) {
		/*** 把消息保存到mongo中* */
		long createTime = System.currentTimeMillis();

		OpenMsgDO openMsg = new OpenMsgDO();
		openMsg.setEnterpriseAccount(wetchatMessageVO.getEnterpriseAccount());
		openMsg.setMsgId(-1);
		openMsg.setMsgType(wetchatMessageVO.getMessageType());
		openMsg.setContent(wetchatMessageVO.getContent());
		openMsg.setSender(wetchatMessageVO.getSenderOpenId());
		openMsg.setReceiver(wetchatMessageVO.getAppId());
		openMsg.setMessageTime(createTime);
		openMsg.setCreateTime(createTime);
		openMsg.setUpdateTime(createTime);
		openMsg.setSessionId(Joiner.on("-").join(wetchatMessageVO.getAppId(), wetchatMessageVO.getSenderOpenId()));
		openMsg.setSendType(MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL.getType());
		openMsg.setProcessStatus(MsgOperConstants.PROCESS_STATUS_UNREAD_UNREPLIED);

		Future<?> future = messageRecordManager.saveMsgSend(openMsg);
		return future;
	}

	@Override
	public MessageResult sendWechatPromptTextMessage(String appID, String ea,
			String wxOpenID, String promptContent, List<Integer> receivers) {
		SendWechatPromptTextMessageArg sendWechatPromptTextMessageArg = new SendWechatPromptTextMessageArg();

		sendWechatPromptTextMessageArg.setAppId(appID);
		sendWechatPromptTextMessageArg.setEa(ea);
		sendWechatPromptTextMessageArg.setPromptContent(promptContent);
		sendWechatPromptTextMessageArg.setReceivers(receivers);
		sendWechatPromptTextMessageArg.setWxOpenId(wxOpenID);
		sendWechatPromptTextMessageArg.setUpdateOrder(true);

		return sendWechatPromptTextMessage(sendWechatPromptTextMessageArg);
	}
	
	@Override
	public MessageResult sendWechatPromptTextMessage(SendWechatPromptTextMessageArg sendWechatPromptTextMessageArg) {
		try {

			OpenSendOuterUserSystemMessageArg openSendOuterUserSystemMessageArg = new OpenSendOuterUserSystemMessageArg();

			openSendOuterUserSystemMessageArg.setAppId(sendWechatPromptTextMessageArg.getAppId());
			openSendOuterUserSystemMessageArg.setEnterpriseAccount(sendWechatPromptTextMessageArg.getEa());
			openSendOuterUserSystemMessageArg.setOpenId(sendWechatPromptTextMessageArg.getWxOpenId());
			openSendOuterUserSystemMessageArg.setReceiverIds(sendWechatPromptTextMessageArg.getReceivers());
			openSendOuterUserSystemMessageArg.setContent(sendWechatPromptTextMessageArg.getPromptContent());
			openSendOuterUserSystemMessageArg.setUpdateOrder(sendWechatPromptTextMessageArg.isUpdateOrder());
			
			openSendOuterUserSystemMessageArg.setMessageType("ST");
			
			if (!Strings.isNullOrEmpty(sendWechatPromptTextMessageArg.getMessageType())) {
				openSendOuterUserSystemMessageArg.setMessageType(sendWechatPromptTextMessageArg.getMessageType());
			}
			openSendOuterUserSystemMessageArg.setPostId(UUID.randomUUID().toString());
			openSendOuterUserSystemMessageArg.setSource(OpenSourceType.system);

			openCustomService.sendCSOuterUserSystemMessage(openSendOuterUserSystemMessageArg);

			return new MessageResult();

		} catch (IllegalArgumentException ex) {
			logger.error("sendWechatPromptTextMessage paramValidate exception params={} exception: ", sendWechatPromptTextMessageArg, ex);
			return new MessageResult(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}

	@Override
	public MessageResult sendWechatMessage(WechatMessageVO wechatMessageVO) {
		try {
			wechatMessageVO.checkParams();

			/** 异步保存微信用户上行的消息到mongo */
			Future<?> future = this.saveWechatUserMsg(wechatMessageVO);

			OpenSendOuterUserMessageArg openSendOuterUserMessageArg = new OpenSendOuterUserMessageArg();

			openSendOuterUserMessageArg.setAppId(wechatMessageVO.getAppId());
			openSendOuterUserMessageArg.setContent(wechatMessageVO.getContent());
			openSendOuterUserMessageArg.setEnterpriseAccount(wechatMessageVO.getEnterpriseAccount());
			openSendOuterUserMessageArg.setMessageType(wechatMessageVO.getMessageType());
			openSendOuterUserMessageArg.setPostId(wechatMessageVO.getPostId());
			openSendOuterUserMessageArg.setCustomerIds(wechatMessageVO.getCustomerIDs());
			openSendOuterUserMessageArg.setSenderOpenId(wechatMessageVO.getSenderOpenId());
			openSendOuterUserMessageArg.setSenderName(wechatMessageVO.getSenderNickName());
			openSendOuterUserMessageArg.setSenderPortrait(wechatMessageVO.getSenderPortraitUrl());
			openSendOuterUserMessageArg.setSource(OpenSourceType.outUser);
			
			if (wechatMessageVO.getCustomerMembers() != null && wechatMessageVO.getCustomerMembers().size() > 0) {
				List<CustomerMembersTO> customerMembers = wechatMessageVO.getCustomerMembers().stream().map(member -> {
					CustomerMembersTO customerMembersTO = new CustomerMembersTO();
					customerMembersTO.setType(member.getType());
					customerMembersTO.setMembers(member.getMembers());
					
					return customerMembersTO;
				}).collect(Collectors.toList());
				
				openSendOuterUserMessageArg.setCustomerMembers(customerMembers);
			}
			
			if (wechatMessageVO.getExpertMembers() != null && wechatMessageVO.getExpertMembers().size() > 0) {
				List<CustomerMembersTO> expertMembers = wechatMessageVO.getExpertMembers().stream().map(member -> {
					CustomerMembersTO customerMembersTO = new CustomerMembersTO();
					customerMembersTO.setType(member.getType());
					customerMembersTO.setMembers(member.getMembers());
					
					return customerMembersTO;
				}).collect(Collectors.toList());
				
				openSendOuterUserMessageArg.setExpertMembers(expertMembers);
			}
			
			future.get();
			openCustomService.sendCSOuterUserMessage(openSendOuterUserMessageArg);
			//增加微联服务号上行工作台消息埋点
			FengYunUtil.reportCustomerMsg(wechatMessageVO.getAppId(), wechatMessageVO.getEnterpriseAccount(), wechatMessageVO.getEnterpriseAccount(), "" + wechatMessageVO.getSenderOpenId(), MessageSendTypeEnum.WECHAT_MESSAGE_NORMAL);
			return new MessageResult();

		} catch (IllegalArgumentException ex) {
			logger.error("sendWetchatMessage paramValidate exception params={} exception={}", wechatMessageVO, ex);
			return new MessageResult(MsgCodeEnum.PARAM_CUSTOMERSERVICE_ILLEGAL.getErrorCode(), ex.getMessage());
		} catch (Exception e) {
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}

	}

	@Override
	public MessageResult sendWechatGroupMessage(WechatGroupMessageVO arg) {

		MessageResult messageResult = new MessageResult();
		WXSendMessageArg wxSendMessageArg = new WXSendMessageArg();

		String qixinGroupID = null;
		logger.info(" arg:{} ", arg);
		try {
			if (SessionType.Single.equals(arg.getSessionType())) {
				/** 单聊时，微信没有群，企信有群。要先判断企信群有没有创建。 */
				WechatGroupIDMappingDO wechatGroupIDMappingDO = wechatGroupIDMappingDAO.getRecord(arg.getFromEmployee(),
						arg.getWechatSessionID());
				if (null == wechatGroupIDMappingDO) {
					wechatGroupIDMappingDO = wechatGroupIDMappingDAO.getRecord(arg.getWechatSessionID(),
							arg.getFromEmployee());
				}

				if (null != wechatGroupIDMappingDO) {
					qixinGroupID = wechatGroupIDMappingDO.getQixinGroupID();
				} else { // 还没有在企信创建群
					WechatCreateSessionVO wechatCreateSessionVO = new WechatCreateSessionVO();
					wechatCreateSessionVO.setSessionName(UUID.randomUUID().toString().substring(0, 31));
					wechatCreateSessionVO.setEnterpriseAccount(arg.getEnterpriseAccount());
					wechatCreateSessionVO.setFromEmployee(arg.getFromEmployee());
					List<String> participantsList = new ArrayList<>();
					participantsList.add(arg.getFromEmployee());
					participantsList.add(arg.getWechatSessionID());
					wechatCreateSessionVO.setParticipants(participantsList);
					WechatGroupResult WechatGroupResult = msgGroupService.createGroupSession(wechatCreateSessionVO);

					qixinGroupID = WechatGroupResult.getSessionId();
					boolean saveMappingRet = wechatGroupIDMappingDAO.saveMapping(qixinGroupID, qixinGroupID,
							arg.getFromEmployee(), arg.getWechatSessionID());
					logger.info(" qixinGroupID:{}, saveMappingRet:{} ", qixinGroupID, saveMappingRet);
				}
			} else {
				// 群聊
				qixinGroupID = wechatGroupIDMappingDAO.getQixinGroupID(arg.getWechatSessionID());
			}
			wxSendMessageArg.setSessionId(qixinGroupID);

			wxSendMessageArg.setEnterpriseAccount(arg.getEnterpriseAccount());
			wxSendMessageArg.setPostId(arg.getPostID());
			wxSendMessageArg.setFromEmployee(EmployeeId.buildFromFullId(arg.getFromEmployee()));
			wxSendMessageArg.setMessageContent(arg.getMessageContent());
			wxSendMessageArg.setMessageType(arg.getMessageType());
			// wxSendMessageArg.setSenderName(arg.getSenderName());

			logger.info(" wxSendMessageArg:{} ", wxSendMessageArg);
			weixinMessageService.sendMessage(wxSendMessageArg);
		} catch (Exception e) {
			messageResult.setResultCodeEnum(MsgCodeEnum.SYSTEM_ERROR);
		}

		return new MessageResult();
	}

	@Override
	public MessageResult sendWechatBaichuanNotify(WechatBaichuanNotifyMessageVO wechatBaichuanNotifyMessageVO) {
		WXSendWBMessageArg wxSendWBMessageArg = new WXSendWBMessageArg();
		wxSendWBMessageArg.setEnterpriseAccount(wechatBaichuanNotifyMessageVO.getEnterpriseAccount());
		wxSendWBMessageArg.setLastSummary(wechatBaichuanNotifyMessageVO.getLastSummary());

		List<WXSendWBMessageArg.EmployeeSessionData> employeeSessionDataList = new ArrayList<WXSendWBMessageArg.EmployeeSessionData>();
		for (Integer receiverid : wechatBaichuanNotifyMessageVO.getReceivers()) {
			WXSendWBMessageArg.EmployeeSessionData employeeSessionData = new WXSendWBMessageArg.EmployeeSessionData();
			employeeSessionData.setEmployeeId(receiverid);
			employeeSessionData.setExtraData(wechatBaichuanNotifyMessageVO.getRedirectUrl());
			employeeSessionDataList.add(employeeSessionData);
		}
		wxSendWBMessageArg.setReceivers(employeeSessionDataList);
		wxSendWBMessageArg.setType(WorkbenchType.WEIXIN_BC_NOTICE);
		wxSendWBMessageArg.setSessionIcon(wechatBaichuanNotifyMessageVO.getSessionIcon());
		wxSendWBMessageArg.setSessionName(wechatBaichuanNotifyMessageVO.getSessionName());
		try {
			weixinMessageService.sendWBMessage(wxSendWBMessageArg);
		} catch (Exception e) {
			logger.error("weixinMessageService.sendWBMessage exception params={} exception=",
					wechatBaichuanNotifyMessageVO, e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
		return new MessageResult();
	}

	/** 推消息给企业互联助手 */
	@Override
	public MsgBaseResult sendMsgToEaConnService(MessageVO msg) {
		return sendMsgToEaConnService(null, msg);
	}

	@Override
	public MsgBaseResult sendMsgToEaConnService(String upstreamEa, MessageVO msg) {
		SendMsgToEaConnServiceVO arg = new SendMsgToEaConnServiceVO();
		arg.setUpstreamEa(upstreamEa);
		arg.setMsgList(Lists.newArrayList(msg));
		arg.setMessageSendType(MessageSendTypeEnum.THIRD_PARTY_PUSH);
		return this.sendMsgToEaConnService(arg);
	}

	@Override
	public MsgBaseResult sendMsgToEaConnService(SendMsgToEaConnServiceVO arg) {
		List<MessageVO> msgList = arg.getMsgList();
		String tempSendId = UUID.randomUUID().toString();
		logger.info("sendMsgToEaConnService tempSendId:{},param:{}",tempSendId,arg);
		if (Objects.isNull(msgList) || msgList.isEmpty()) {
			return new MessageResult(MsgCodeEnum.PARAM_ILLEGAL_EXCEPTION);
		}
		try {
			MessageListVO messageEaConnVO = new MessageListVO();
			messageEaConnVO.setTempSendId(tempSendId);
			messageEaConnVO.setUpstreamEa(arg.getUpstreamEa());
			for (MessageVO msg : msgList) {
				if(StringUtils.isEmpty(msg.getEnterpriseAccount()) || Objects.isNull(msg.getToUserList()) || msg.getToUserList().isEmpty()){
					logger.warn("sendMsgToEaConnService param error msg:{}",msg);
					continue;
				}
				MessagePbVO messagePbVO = new MessagePbVO();
				BeanUtils.copyProperties(messagePbVO, msg);
				messagePbVO.setPostId(msg.getPostId());
				messagePbVO.setQxType(msg.getType().getType());
				messagePbVO.setMessageSendType(arg.getMessageSendType());
				messageEaConnVO.addMessage(messagePbVO);
			}
			
		/*	TempOpenMsgDO tempOpenMsgDO = new TempOpenMsgDO();
			tempOpenMsgDO.setTempSendId(tempSendId);
			tempOpenMsgDO.setUpEnterpriseAccount(arg.getUpstreamEa());
			tempOpenMsgDO.setCreateTime(System.currentTimeMillis());
			messageRecordManager.saveTempMsg(tempOpenMsgDO);*/

			if (CollectionUtils.isNotEmpty(messageEaConnVO.getMsgList())){
				MessageListVO messageListVO = new MessageListVO();
				messageListVO.setMsgFlag(MsgOperConstants.MESSAGE_FLAG_EACONN);
				messageListVO.setMsgList(messageEaConnVO.getMsgList());
				messageListVO.setUpstreamEa(messageEaConnVO.getUpstreamEa());
				//messageEaConnVO.setMsgList(messageEaConnVO.getMsgList());
				//message.setBody(messageEaConnVO.toProto());
				asyncBatchSendOpenMessageService.asyncBatchSendMessage(messageListVO);
			}
			return new MessageResult(tempSendId);
		} catch (Exception e) {
			logger.error("Async sendMessage error {}", e);
			return new MessageResult(MsgCodeEnum.SYSTEM_ERROR);
		}
	}


	private Map<Integer, OpenMessageSimpleItem> convertResponseToSimpleItemMap(String line) {
		Map<String, String> replacements = new HashMap<String, String>();
		String rx = "(\\d+)(=)";
		StringBuffer sb = new StringBuffer();
		Pattern p = Pattern.compile(rx);
		Matcher m = p.matcher(line);
		while (m.find())        {
			replacements.put(m.group(1), "\""+m.group(1)+"\"=");
		}
		m = p.matcher(line);
		while (m.find())
		{
			// Avoids throwing a NullPointerException in the case that you
			// Don't have a replacement defined in the map for the match
			String repString = replacements.get(m.group(1));
			if (repString != null)
				m.appendReplacement(sb, repString);
		}
		m.appendTail(sb);
		String rsp = sb.toString();
		rsp = rsp.replace("OpenMessageSimpleItem", "");
		rsp = rsp.replace("=", ":");

		try {
			Map<Integer, OpenMessageSimpleItem> map = new Gson().fromJson(rsp, new TypeToken<Map<Integer, OpenMessageSimpleItem>>() {}.getType());
			return map;
		}catch (Exception e) {
			System.out.println("convert json get exception, " + e);
		}
		return null;
	}

	/**
	 * 服务号群发消息撤回。
	 * 该接口会立即返回，然后异步执行撤回消息的操作。
	 *
	 * @param revokeMsgVO @see RevokeMsgVO
	 * @return @see MessageResult
	 */
	@Override
	public MessageResult revokeAppToCMsg(RevokeMsgVO revokeMsgVO) {
		Thread worker = new Thread() {
			public void run() {
				OpenRevokeMessageArg openRevokeMessageArg = new OpenRevokeMessageArg();
				OpenMsgDO openMsgDO = null;
				int cntRetry = 0;
				while (null == openMsgDO) {
					if (cntRetry >= revokeAppToCTryTimes) {
						logger.warn("revokeAppToCMsg try more than 100 times, arg :{} ", revokeMsgVO);
						return;
					}
					cntRetry++;
					try {
						Thread.sleep(revokeAppToCSleepTimeInMinisecond);
					} catch (Exception e) {}

					openMsgDO = messageRecordManager.getOpenMsgByPostId(revokeMsgVO.getFsEa(), revokeMsgVO.getFsAppId(), revokeMsgVO.getPostId());
				}

				Map<Integer, OpenMessageSimpleItem> map = null;
				try {
					map = convertResponseToSimpleItemMap(openMsgDO.getResponse());
					if((null == map) || (map.isEmpty())) {
						logger.warn("trace convertResponseToSimpleItemMap  map is empty, revokeMsgVO:{} ", revokeMsgVO);
						return;
					}
				}catch (Exception e) {
					logger.warn("trace convertResponseToSimpleItemMap fail, revokeMsgVO:{}, get exception,  ", revokeMsgVO, e);
					return;
				}
				logger.info("trace before revoke part of response:{},   msgnum:{}, revokeMsgVO:{} ",
						openMsgDO.getResponse().substring(0,openMsgDO.getResponse().length() > 200 ? 200 : openMsgDO.getResponse().length()), map.size(),  revokeMsgVO);

				openRevokeMessageArg.setEnterpriseAccount(revokeMsgVO.getFsEa());
				openRevokeMessageArg.setAppId(revokeMsgVO.getFsAppId());
				for (Integer userIdkey : map.keySet()) {
					OpenMessageSimpleItem item = map.get(userIdkey);
					openRevokeMessageArg.setMessageId(item.getMessageId());
					openRevokeMessageArg.setSessionId(item.getSessionId());
					try {
						OpenRevokeMessageResult openRevokeMessageResult = openMessageService.revokeMessage(openRevokeMessageArg);
						logger.info("trace revokeMessage arg:{}, result:{} ", openRevokeMessageArg, openRevokeMessageResult);
					} catch (Exception e) {
						logger.error("trace revokeMessage get exception arg:{}, exception ", openRevokeMessageArg, e);
					}
				}

				//每个接受者消息都撤回了，要删掉mongo中的记录。以免被查询接口搜索出来。
				//messageRecordManager.deleteOpenMsgRevoke(revokeMsgVO.getFsEa(), revokeMsgVO.getFsAppId(), revokeMsgVO.getPostId());
				logger.info("trace revokeAppToCMsg complete, arg: {} ", revokeMsgVO);
			}
		};
		worker.start();

		return new MessageResult();
	}
}
